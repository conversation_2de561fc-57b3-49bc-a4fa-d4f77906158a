# 数据库连接问题修复指南

## 🚨 问题描述

在启动Dendrite时遇到红包数据库连接错误：
```
无法连接到红包数据库 error="failed to find maximum connections: dial tcp: lookup hostname: no such host"
```

## 🔧 问题原因

红包服务配置中使用了占位符 `hostname`，导致无法解析数据库主机名。

## ✅ 解决方案

### 方案1: 使用全局数据库配置（推荐）

我已经修改了配置，让红包服务使用全局数据库配置：

#### 1. 修改配置文件
在 [`cmd/dendrite/dendrite.yaml`](cmd/dendrite/dendrite.yaml) 中，红包服务的数据库配置已被注释掉：

```yaml
redpacket:
  enabled: true
  max_amount: 200.0
  max_count: 100
  default_expires_in: 86400
  min_amount: 0.01
  allow_lucky_redpacket: true
  
  # 注释掉单独的数据库配置，使用全局数据库配置
  # database:
  #   connection_string: "**************************************************************************"
  #   max_open_conns: 90
  #   max_idle_conns: 5
  #   conn_max_lifetime: -1
```

#### 2. 修改代码逻辑
在 [`redpacketapi/redpacketapi.go`](redpacketapi/redpacketapi.go) 中，已修改为使用全局数据库配置：

```go
// 创建数据库连接 - 使用全局数据库配置
// 传递nil让连接管理器使用全局数据库配置
redpacketDB, err := postgres.NewDatabase(context.Background(), cm, nil)
if err != nil {
    logrus.WithError(err).Panic("无法连接到红包数据库")
}
```

### 方案2: 配置独立数据库（可选）

如果你想为红包服务使用独立的数据库，请修改配置：

```yaml
redpacket:
  enabled: true
  # ... 其他配置
  database:
    connection_string: "****************************************************************************"
    max_open_conns: 90
    max_idle_conns: 5
    conn_max_lifetime: -1
```

## 🚀 验证修复

### 1. 重新编译
```bash
go build -o bin/dendrite ./cmd/dendrite
```

### 2. 启动服务
```bash
./bin/dendrite --config dendrite.yaml
```

### 3. 检查启动日志
应该看到类似的成功日志：
```
INFO[2024-01-01T12:00:00Z] Dendrite 版本 0.13.7+dev
INFO[2024-01-01T12:00:00Z] 红包服务已启用
INFO[2024-01-01T12:00:00Z] 数据库表创建成功
```

### 4. 运行测试
```bash
./test_redpacket_simple.sh
```

## 📋 数据库表说明

红包服务会自动在全局数据库中创建以下表：

### 红包主表
```sql
CREATE TABLE IF NOT EXISTS redpacketapi_redpackets (
    id TEXT PRIMARY KEY,
    event_id TEXT NOT NULL UNIQUE,
    room_id TEXT NOT NULL,
    sender_id TEXT NOT NULL,
    type TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    total_count INTEGER NOT NULL,
    remaining_amount DECIMAL(10,2) NOT NULL,
    remaining_count INTEGER NOT NULL,
    message TEXT NOT NULL DEFAULT '',
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL
);
```

### 红包领取记录表
```sql
CREATE TABLE IF NOT EXISTS redpacketapi_redpacket_grabs (
    id TEXT PRIMARY KEY,
    redpacket_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    grabbed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    is_luckiest BOOLEAN NOT NULL DEFAULT FALSE,
    user_nickname TEXT NOT NULL DEFAULT '',
    FOREIGN KEY (redpacket_id) REFERENCES redpacketapi_redpackets(id) ON DELETE CASCADE,
    UNIQUE(redpacket_id, user_id)
);
```

## 🔍 故障排除

### 如果仍然遇到数据库连接问题：

1. **检查全局数据库配置**
   ```yaml
   global:
     database:
       connection_string: ****************************************************************************
   ```

2. **测试数据库连接**
   ```bash
   psql -h ************* -p 5000 -U zhoujiayi -d dendrite -c "SELECT 1;"
   ```

3. **检查网络连接**
   ```bash
   ping *************
   telnet ************* 5000
   ```

4. **查看详细错误日志**
   ```bash
   ./bin/dendrite --config dendrite.yaml 2>&1 | grep -i error
   ```

### 常见问题解决：

1. **主机名解析失败**
   - 确保数据库主机名或IP地址正确
   - 检查DNS解析或使用IP地址

2. **端口连接失败**
   - 确认PostgreSQL服务正在运行
   - 检查防火墙设置
   - 验证端口号是否正确

3. **认证失败**
   - 检查用户名和密码
   - 确认用户有数据库访问权限

4. **数据库不存在**
   - 创建数据库：`createdb -h ************* -p 5000 -U zhoujiayi dendrite`

## ✅ 预期结果

修复后，Dendrite应该能够成功启动，红包功能应该可以正常工作：

```
🎉 红包功能测试开始...
📋 步骤1: 注册测试用户
✅ 用户注册完成
📋 步骤2: 用户登录
✅ 用户登录成功
📋 步骤3: 创建测试房间
✅ 房间创建成功
📋 步骤4: 邀请用户加入房间
✅ 用户加入房间成功
📋 步骤5: 发送红包
✅ 红包发送成功
📋 步骤6: 抢红包
✅ 抢红包成功！获得金额: 3.45 元
🎊 红包功能测试全部通过！
```

现在数据库连接问题应该已经解决了！🎉