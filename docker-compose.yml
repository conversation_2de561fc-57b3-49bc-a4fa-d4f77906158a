version: "3.8"

services:
  # Dendrite Matrix 服务器主服务
  dendrite:
    # 使用项目根目录的 Dockerfile 构建镜像
    build:
      context: .              # 构建上下文为当前目录
      dockerfile: Dockerfile  # 使用的 Dockerfile 文件名

    container_name: dendrite  # 容器名称

    # 端口映射：主机端口:容器端口
    ports:
      - "8008:8008"   # Matrix 客户端 API 端口
      - "8448:8448"   # Matrix 联邦 API 端口（服务器间通信）

    # 数据卷挂载
    volumes:
      # 将整个项目目录挂载到容器内，这样所有文件都可以访问
      - ./config:/etc/dendrite
      # 持久化数据目录
      - dendrite_media:/etc/dendrite/media_store           # 媒体文件存储
      - dendrite_jetstream:/etc/dendrite/jetstream   # 消息队列数据
      - dendrite_search_index:/etc/dendrite/searchindex # 搜索索引
      # 日志目录（可写）
      - dendrite_logs:/etc/dendrite/logs

    # 启动命令：指定配置文件路径并启用开放注册
    command: [
      "--config", "/etc/dendrite/dendrite.yaml", # 配置文件
      "--really-enable-open-registration" # 启用开放注册 （没有验证码的）
    ]

    restart: unless-stopped  # 容器重启策略：除非手动停止，否则总是重启

    # 网络配置
    networks:
      - dendrite_network

# 网络定义
networks:
  dendrite_network:
    driver: bridge  # 使用桥接网络驱动

# 数据卷定义（用于持久化数据）
volumes:
  dendrite_media:        # 媒体文件存储卷
    driver: local
  dendrite_jetstream:    # 消息队列数据卷
    driver: local
  dendrite_search_index: # 搜索索引数据卷
    driver: local
  dendrite_logs: # 搜索索引数据卷
    driver: local