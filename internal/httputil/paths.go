// Copyright 2024 New Vector Ltd.
// Copyright 2020 The Matrix.org Foundation C.I.C.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package httputil

const (
	PublicClientPathPrefix     = "/_matrix/client/"
	PublicFederationPathPrefix = "/_matrix/federation/"
	PublicKeyPathPrefix        = "/_matrix/key/"
	PublicMediaPathPrefix      = "/_matrix/media/"
	PublicStaticPath           = "/_matrix/static/"
	PublicWellKnownPrefix      = "/.well-known/matrix/"
	DendriteAdminPathPrefix    = "/_dendrite/"
	SynapseAdminPathPrefix     = "/_synapse/"
)
