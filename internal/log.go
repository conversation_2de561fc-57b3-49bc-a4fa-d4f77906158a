// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// internal 包 - 包含 Dendrite 的内部工具函数和日志管理
package internal

import (
	"context"   // 上下文管理
	"fmt"       // 格式化输出
	"io"        // IO 接口
	"net/http"  // HTTP 服务器
	"os"        // 操作系统接口
	"path"      // 路径操作
	"path/filepath" // 文件路径操作
	"runtime"   // 运行时信息
	"strings"   // 字符串操作
	"sync"      // 同步原语

	"github.com/matrix-org/util" // Matrix 工具库

	"github.com/matrix-org/dugong"  // 日志轮转库
	"github.com/sirupsen/logrus"    // 结构化日志库

	"github.com/element-hq/dendrite/setup/config" // Dendrite 配置
)

// logrus 在使用 `logrus.AddHook` 时使用全局变量
// 这不幸地导致我们多次添加相同的钩子
// 这个映射确保我们只添加一个级别钩子
var stdLevelLogAdded = make(map[logrus.Level]bool) // 标准级别日志已添加的映射
var levelLogAddedMu = &sync.Mutex{}                // 级别日志添加的互斥锁

// utcFormatter UTC 时间格式化器 - 将所有日志时间转换为 UTC 时区
type utcFormatter struct {
	logrus.Formatter // 嵌入 logrus 格式化器
}

// Format 格式化日志条目，将时间转换为 UTC
func (f utcFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	entry.Time = entry.Time.UTC() // 将时间转换为 UTC
	return f.Formatter.Format(entry) // 调用底层格式化器
}

// logLevelHook Logrus 钩子，包装另一个钩子并根据级别过滤日志条目
// （注意我们不能仅使用 logrus.SetLevel，因为 Dendrite 支持同时使用多个日志级别）
type logLevelHook struct {
	level logrus.Level // 日志级别
	logrus.Hook        // 嵌入 logrus 钩子
}

// Levels 返回此钩子支持的所有级别
func (h *logLevelHook) Levels() []logrus.Level {
	levels := make([]logrus.Level, 0) // 创建级别切片

	// 遍历所有日志级别
	for _, level := range logrus.AllLevels {
		if level <= h.level { // 如果级别小于等于设定级别
			levels = append(levels, level) // 添加到支持的级别列表
		}
	}

	return levels
}

// callerPrettyfier 是一个函数，给定一个 runtime.Frame 对象，
// 将提取调用函数的名称和文件，并以格式良好的方式返回它们
func callerPrettyfier(f *runtime.Frame) (string, string) {
	// 仅检索函数名称
	s := strings.Split(f.Function, ".") // 按点分割函数全名
	funcname := s[len(s)-1]             // 获取最后一部分（函数名）

	// 在其后附加换行符 + 制表符，将实际日志内容移动到自己的行
	funcname += "\n\t"

	// 使用缩短的文件路径，仅包含文件名，避免大量冗余目录
	// 这些目录会显著增加整体日志大小！
	filename := fmt.Sprintf(" [%s:%d]", path.Base(f.File), f.Line)

	return funcname, filename
}

// SetupPprof 启动 pprof 监听器。我们在这里使用 DefaultServeMux，因为它是
// 最简单的，并且它给了我们在单独端口上运行 pprof 的自由
func SetupPprof() {
	if hostPort := os.Getenv("PPROFLISTEN"); hostPort != "" { // 检查环境变量 PPROFLISTEN
		logrus.Warn("Starting pprof on ", hostPort) // 警告：在指定端口启动 pprof
		go func() { // 在新的 goroutine 中启动
			logrus.WithError(http.ListenAndServe(hostPort, nil)).Error("Failed to setup pprof listener")
		}()
	}
}

// SetupStdLogging 配置标准输出的日志格式。通常在配置尚未加载时调用
func SetupStdLogging() {
	levelLogAddedMu.Lock()         // 获取锁
	defer levelLogAddedMu.Unlock() // 延迟释放锁
	logrus.SetReportCaller(true)   // 设置报告调用者信息
	logrus.SetFormatter(&utcFormatter{ // 设置 UTC 格式化器
		&logrus.TextFormatter{
			TimestampFormat:  "2006-01-02T15:04:05.000000000Z07:00", // 时间戳格式（RFC3339 纳秒精度）
			FullTimestamp:    true,                                   // 完整时间戳
			DisableColors:    false,                                  // 启用颜色
			DisableTimestamp: false,                                  // 启用时间戳
			QuoteEmptyFields: true,                                   // 引用空字段
			CallerPrettyfier: callerPrettyfier,                       // 调用者美化器
		},
	})
}

// checkFileHookParams 文件类型钩子应该提供一个目录路径来存储日志文件
func checkFileHookParams(params map[string]interface{}) {
	path, ok := params["path"] // 检查是否存在 path 参数
	if !ok {
		logrus.Fatalf("Expecting a parameter \"path\" for logging hook of type \"file\"")
	}

	if _, ok := path.(string); !ok { // 检查 path 参数是否为字符串类型
		logrus.Fatalf("Parameter \"path\" for logging hook of type \"file\" should be a string")
	}
}

// setupFileHook 向日志器添加新的 FSHook。每个组件将在自己的文件中记录日志
func setupFileHook(hook config.LogrusHook, level logrus.Level) {
	dirPath := (hook.Params["path"]).(string)           // 获取目录路径
	fullPath := filepath.Join(dirPath, "dendrite.log") // 构建完整的日志文件路径

	// 创建目录（如果不存在）
	if err := os.MkdirAll(path.Dir(fullPath), os.ModePerm); err != nil {
		logrus.Fatalf("Couldn't create directory %s: %q", path.Dir(fullPath), err)
	}

	// 添加文件日志钩子
	logrus.AddHook(&logLevelHook{
		level, // 日志级别
		dugong.NewFSHook( // 创建文件系统钩子
			fullPath, // 日志文件路径
			&utcFormatter{ // UTC 格式化器
				&logrus.TextFormatter{
					TimestampFormat:  "2006-01-02T15:04:05.000000000Z07:00", // 时间戳格式
					DisableColors:    true,                                   // 禁用颜色（文件输出）
					DisableTimestamp: false,                                  // 启用时间戳
					DisableSorting:   false,                                  // 启用字段排序
					QuoteEmptyFields: true,                                   // 引用空字段
				},
			},
			&dugong.DailyRotationSchedule{GZip: true}, // 每日轮转计划，启用 GZip 压缩
		),
	})
}

// CloseAndLogIfError 关闭 io.Closer 并在有错误时记录错误
func CloseAndLogIfError(ctx context.Context, closer io.Closer, message string) {
	if closer == nil { // 如果 closer 为 nil，直接返回
		return
	}
	err := closer.Close() // 关闭资源
	if ctx == nil {       // 如果上下文为 nil，使用默认上下文
		ctx = context.TODO()
	}
	if err != nil { // 如果有错误，记录错误日志
		util.GetLogger(ctx).WithError(err).Error(message)
	}
}
