// Copyright 2024 New Vector Ltd.
// Copyright 2020 The Matrix.org Foundation C.I.C.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// setup 包 - 负责 Dendrite 服务器的设置和配置
package setup

import (
	"github.com/element-hq/dendrite/aiservice"                       // AI 服务实现
	aiserviceAPI "github.com/element-hq/dendrite/aiservice/api"      // AI 服务 API 接口
	appserviceAPI "github.com/element-hq/dendrite/appservice/api"    // 应用服务 API 接口
	"github.com/element-hq/dendrite/clientapi"                       // 客户端 API 实现
	"github.com/element-hq/dendrite/clientapi/api"                   // 客户端 API 接口
	"github.com/element-hq/dendrite/federationapi"                   // 联邦 API 实现
	federationAPI "github.com/element-hq/dendrite/federationapi/api" // 联邦 API 接口
	"github.com/element-hq/dendrite/internal/caching"                // 内部缓存系统
	"github.com/element-hq/dendrite/internal/httputil"               // HTTP 工具函数
	"github.com/element-hq/dendrite/internal/sqlutil"                // SQL 数据库工具
	"github.com/element-hq/dendrite/internal/transactions"           // 事务处理
	"github.com/element-hq/dendrite/mediaapi"                        // 媒体 API 实现
	"github.com/element-hq/dendrite/redpacketapi"                    // 红包 API 实现
	redpacketAPI "github.com/element-hq/dendrite/redpacketapi/api"   // 红包 API 接口
	"github.com/element-hq/dendrite/relayapi"                        // 中继 API 实现
	relayAPI "github.com/element-hq/dendrite/relayapi/api"           // 中继 API 接口
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"    // 房间服务器 API 接口
	"github.com/element-hq/dendrite/setup/config"                    // 配置管理
	"github.com/element-hq/dendrite/setup/jetstream"                 // NATS JetStream 消息队列
	"github.com/element-hq/dendrite/setup/process"                   // 进程管理
	"github.com/element-hq/dendrite/syncapi"                         // 同步 API 实现
	userapi "github.com/element-hq/dendrite/userapi/api"             // 用户 API 接口
	"github.com/matrix-org/gomatrixserverlib"                        // Matrix 服务器库
	"github.com/matrix-org/gomatrixserverlib/fclient"                // 联邦客户端
	"github.com/sirupsen/logrus"                                     // 日志库
)

// Monolith 结构体表示构建 Dendrite 所有组件所需的所有依赖项的实例化，
// 用于单体模式部署。在单体模式下，所有微服务组件都运行在同一个进程中。
type Monolith struct {
	Config    *config.Dendrite           // Dendrite 配置信息
	KeyRing   *gomatrixserverlib.KeyRing // 密钥环，用于验证和签名 Matrix 事件
	Client    *fclient.Client            // 通用 HTTP 客户端
	FedClient fclient.FederationClient   // 联邦客户端，用于与其他 Matrix 服务器通信

	// 核心 API 组件 - 这些是 Dendrite 的主要微服务
	AIServiceAPI  aiserviceAPI.AIServiceInternalAPI   // AI 服务 API，处理 AI 对话功能
	AppserviceAPI appserviceAPI.AppServiceInternalAPI // 应用服务 API，处理与外部应用服务的集成
	FederationAPI federationAPI.FederationInternalAPI // 联邦 API，处理与其他 Matrix 服务器的通信
	RoomserverAPI roomserverAPI.RoomserverInternalAPI // 房间服务器 API，管理房间状态和事件
	UserAPI       userapi.UserInternalAPI             // 用户 API，管理用户账户、设备和密钥
	RelayAPI      relayAPI.RelayInternalAPI           // 中继 API，处理消息中继（可选）
	RedPacketAPI  redpacketAPI.RedPacketServiceInternalAPI // 红包 API，处理红包功能

	// 可选的扩展提供者 - 允许自定义实现
	ExtPublicRoomsProvider   api.ExtraPublicRoomsProvider   // 外部公共房间提供者，用于扩展公共房间列表
	ExtUserDirectoryProvider userapi.QuerySearchProfilesAPI // 外部用户目录提供者，用于扩展用户搜索功能
}

// AddAllPublicRoutes 将所有公共路径附加到给定的路由器
// 这个方法设置了所有 Matrix 客户端和联邦 API 的 HTTP 端点
func (m *Monolith) AddAllPublicRoutes(
	processCtx *process.ProcessContext, // 进程上下文，用于管理组件生命周期
	cfg *config.Dendrite, // Dendrite 配置
	routers httputil.Routers, // HTTP 路由器集合
	cm *sqlutil.Connections, // 数据库连接管理器
	natsInstance *jetstream.NATSInstance, // NATS JetStream 实例
	caches *caching.Caches, // 缓存系统
	enableMetrics bool, // 是否启用 Prometheus 指标
) {
	// 设置用户目录提供者 - 如果没有外部提供者，使用默认的用户 API
	userDirectoryProvider := m.ExtUserDirectoryProvider
	if userDirectoryProvider == nil {
		userDirectoryProvider = m.UserAPI // 使用内置的用户 API 作为用户目录提供者
	}

	// 添加客户端 API 公共路由 - 处理客户端应用程序的请求
	// 包括登录、注册、发送消息、加入房间等功能
	clientapi.AddPublicRoutes(
		processCtx, routers, cfg, natsInstance, m.FedClient, m.RoomserverAPI, m.AppserviceAPI, transactions.New(),
		m.FederationAPI, m.UserAPI, userDirectoryProvider,
		m.ExtPublicRoomsProvider, m.RedPacketAPI, enableMetrics,
	)

	// 添加联邦 API 公共路由 - 处理与其他 Matrix 服务器的通信
	// 包括事件交换、密钥查询、服务器发现等功能
	federationapi.AddPublicRoutes(
		processCtx, routers, cfg, natsInstance, m.UserAPI, m.FedClient, m.KeyRing, m.RoomserverAPI, m.FederationAPI, enableMetrics,
	)

	// 添加媒体 API 公共路由 - 处理文件上传、下载和缩略图生成
	mediaapi.AddPublicRoutes(routers, cm, cfg, m.UserAPI, m.Client, m.FedClient, m.KeyRing)

	// 添加同步 API 公共路由 - 处理客户端的实时同步请求
	// 包括 /sync 端点，用于获取新消息、房间状态变化等
	syncapi.AddPublicRoutes(processCtx, routers, cfg, cm, natsInstance, m.UserAPI, m.RoomserverAPI, caches, enableMetrics)

	// 红包 API 路由现在在客户端 API 中设置，不需要在这里重复设置

	// 如果启用了中继 API，添加中继 API 公共路由
	// 中继 API 用于处理消息中继功能（实验性功能）
	if m.RelayAPI != nil {
		relayapi.AddPublicRoutes(routers, cfg, m.KeyRing, m.RelayAPI)
	}
}

// StartAIService 启动AI服务的消费者
// 这个方法启动AI服务的房间事件消费者，用于监听房间消息并生成AI回复
func (m *Monolith) StartAIService(
	processCtx *process.ProcessContext, // 进程上下文
	cfg *config.Dendrite, // Dendrite 配置
	natsInstance *jetstream.NATSInstance, // NATS JetStream 实例
) {

	cfg.AIService.Enabled = true
	// 只有在AI服务启用时才启动消费者
	if !cfg.AIService.Enabled {
		logrus.Info("AI服务未启用，跳过启动")
		return
	}

	// 启动AI服务的消费者
	go func() {
		if err := aiservice.StartConsumers(
			processCtx,
			&cfg.AIService,
			natsInstance,
			m.AIServiceAPI,
			m.RoomserverAPI,
			m.UserAPI,
			&cfg.ClientAPI,        // 传递客户端API配置
			transactions.New(),    // 创建新的事务缓存
		); err != nil {
			logrus.WithError(err).Error("启动AI服务消费者失败")
		}
	}()
}

// NewRedPacketAPI 创建新的红包服务API实例
// 这个方法初始化红包服务的所有组件
func NewRedPacketAPI(
	cfg *config.Dendrite, // Dendrite 配置
	cm *sqlutil.Connections, // 数据库连接管理器
	rsAPI roomserverAPI.RoomserverInternalAPI, // 房间服务器 API
	userAPI userapi.UserInternalAPI, // 用户 API
) redpacketAPI.RedPacketServiceInternalAPI {
	// 如果红包服务未启用，返回nil
	if !cfg.RedPacket.Enabled {
		logrus.Info("红包服务未启用")
		return nil
	}

	// 创建红包服务API实例
	return redpacketapi.NewInternalAPI(&cfg.RedPacket, cm, rsAPI, userAPI)
}
