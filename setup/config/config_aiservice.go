// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务配置包
package config

// AIService AI服务配置结构
type AIService struct {
	Matrix *Global `yaml:"-"` // 全局配置引用

	// 是否启用AI服务
	Enabled bool `yaml:"enabled"`
	
	// AI用户的Matrix ID
	AIUserID string `yaml:"ai_user_id"`
	
	// AI用户显示名称
	AIUserName string `yaml:"ai_user_name"`
	
	// 触发AI回复的关键词列表
	Triggers []string `yaml:"triggers"`
	
	// 最大回复长度
	MaxLength int `yaml:"max_length"`
	
	// 外部AI API端点（可选，用于集成外部AI服务）
	APIEndpoint string `yaml:"api_endpoint"`
	
	// API密钥（可选）
	APIKey string `yaml:"api_key"`
	
	// 回复延迟（毫秒），模拟真实用户的打字时间
	ReplyDelay int `yaml:"reply_delay"`
	
	// 是否在房间中自动加入AI用户
	AutoJoinRooms bool `yaml:"auto_join_rooms"`
}

// Defaults 设置AI服务的默认配置值
func (c *AIService) Defaults(opts DefaultOpts) {
	c.Enabled = false
	c.AIUserID = "@ai.zhoujiayi:localhost"
	c.AIUserName = "AI Assistant"
	c.Triggers = []string{"@ai", "ai", "助手", "AI"}
	c.MaxLength = 1000
	c.ReplyDelay = 1000 // 1秒延迟
	c.AutoJoinRooms = false
}

// Verify 验证AI服务配置的有效性
func (c *AIService) Verify(configErrs *ConfigErrors) {
	if c.Enabled {
		// 验证AI用户ID格式
		if c.AIUserID == "" {
			configErrs.Add("ai_service.ai_user_id must be specified when AI service is enabled")
		}
		
		// 验证触发词不为空
		if len(c.Triggers) == 0 {
			configErrs.Add("ai_service.triggers must contain at least one trigger word")
		}
		
		// 验证最大长度为正数
		if c.MaxLength <= 0 {
			configErrs.Add("ai_service.max_length must be positive")
		}
		
		// 验证回复延迟为非负数
		if c.ReplyDelay < 0 {
			configErrs.Add("ai_service.reply_delay must be non-negative")
		}
	}
}