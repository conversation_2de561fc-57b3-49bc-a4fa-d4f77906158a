package config

import (
	"fmt"
	"time"

	"github.com/matrix-org/gomatrixserverlib"
	log "github.com/sirupsen/logrus"
)

type RoomServer struct {
	Matrix *Global `yaml:"-"`

	DefaultRoomVersion gomatrixserverlib.RoomVersion `yaml:"default_room_version,omitempty"`

	Database DatabaseOptions `yaml:"database,omitempty"`

	// 阅后即焚消息配置
	ExpiringMessages ExpiringMessagesConfig `yaml:"expiring_messages,omitempty"`
}

// ExpiringMessagesConfig 阅后即焚消息配置
type ExpiringMessagesConfig struct {
	// 检查过期消息的间隔
	CheckInterval time.Duration `yaml:"check_interval"`

	// 每次处理的最大消息数量
	BatchSize int `yaml:"batch_size"`

	// 保留已处理记录的天数
	CleanupDays int `yaml:"cleanup_days"`

	// 最大过期时间限制（天数）
	MaxExpireDays int `yaml:"max_expire_days"`

	// 是否启用阅后即焚功能
	Enabled bool `yaml:"enabled"`

	// Redis 分布式锁配置
	Redis RedisLockConfig `yaml:"redis"`
}

// RedisLockConfig Redis 分布式锁配置
type RedisLockConfig struct {
	// 是否启用 Redis 分布式锁
	Enabled bool `yaml:"enabled"`

	// Redis 服务器地址
	Address string `yaml:"address"`

	// Redis 密码
	Password string `yaml:"password"`

	// Redis 数据库编号
	Database int `yaml:"database"`
}

func (c *RoomServer) Defaults(opts DefaultOpts) {
	c.DefaultRoomVersion = gomatrixserverlib.RoomVersionV10
	if opts.Generate {
		if !opts.SingleDatabase {
			c.Database.ConnectionString = "file:roomserver.db"
		}
	}

	// 设置阅后即焚消息的默认值
	c.ExpiringMessages.Defaults()
}

// Defaults 设置阅后即焚消息配置的默认值
func (c *ExpiringMessagesConfig) Defaults() {
	c.CheckInterval = 30 * time.Second // 默认30秒检查一次
	c.BatchSize = 100                  // 默认每次处理100条消息
	c.CleanupDays = 7                  // 默认保留7天的已处理记录
	c.MaxExpireDays = 30               // 默认最大过期时间30天
	c.Enabled = true                   // 默认启用功能

	// Redis 分布式锁默认配置
	c.Redis.Enabled = false            // 默认不启用 Redis 锁
	c.Redis.Address = "localhost:6379" // 默认 Redis 地址
	c.Redis.Password = ""              // 默认无密码
	c.Redis.Database = 0               // 默认数据库0
}

func (c *RoomServer) Verify(configErrs *ConfigErrors) {
	if c.Matrix.DatabaseOptions.ConnectionString == "" {
		checkNotEmpty(configErrs, "room_server.database.connection_string", string(c.Database.ConnectionString))
	}

	if !gomatrixserverlib.KnownRoomVersion(c.DefaultRoomVersion) {
		configErrs.Add(fmt.Sprintf("invalid value for config key 'room_server.default_room_version': unsupported room version: %q", c.DefaultRoomVersion))
	} else if !gomatrixserverlib.StableRoomVersion(c.DefaultRoomVersion) {
		log.Warnf("WARNING: Provided default room version %q is unstable", c.DefaultRoomVersion)
	}
}
