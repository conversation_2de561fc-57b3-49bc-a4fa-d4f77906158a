// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// config 包 - 负责 Dendrite 配置文件的解析和验证
package config

import (
	"bytes"         // 字节缓冲区操作
	"encoding/pem"  // PEM 编码解析
	"fmt"           // 格式化输出
	"io"            // IO 接口
	"os"            // 操作系统接口
	"path/filepath" // 文件路径操作
	"regexp"        // 正则表达式
	"strings"       // 字符串操作

	"github.com/element-hq/dendrite/clientapi/auth/authtypes" // 认证类型
	"github.com/matrix-org/gomatrixserverlib"                 // Matrix 服务器库
	"github.com/matrix-org/gomatrixserverlib/spec"            // Matrix 规范
	"github.com/sirupsen/logrus"                              // 日志库
	"golang.org/x/crypto/ed25519"                             // Ed25519 加密算法
	"gopkg.in/yaml.v2"                                        // YAML 解析库

	jaegerconfig "github.com/uber/jaeger-client-go/config" // Jaeger 追踪配置
	jaegermetrics "github.com/uber/jaeger-lib/metrics"     // Jaeger 指标
)

// keyIDRegexp 定义密钥 ID 中允许的字符
var keyIDRegexp = regexp.MustCompile("^ed25519:[a-zA-Z0-9_]+$")

// Version 是配置格式的当前版本
// 每当我们对配置格式进行破坏性更改时，这个版本号都会改变
const Version = 2

// Dendrite 包含 dendrite 进程使用的所有配置
// 相对路径相对于当前工作目录解析
type Dendrite struct {
	// 配置文件的版本
	// 如果文件中的版本与当前 dendrite 配置版本不匹配，
	// 那么我们可以给出清晰的错误消息，告诉用户
	// 将其配置文件更新到当前版本
	// 文件的版本只有在配置文件格式发生破坏性更改时才应该不同
	Version int `yaml:"version"`

	// 各个微服务组件的配置
	Global        Global        `yaml:"global"`          // 全局配置
	AppServiceAPI AppServiceAPI `yaml:"app_service_api"` // 应用服务 API 配置
	ClientAPI     ClientAPI     `yaml:"client_api"`      // 客户端 API 配置
	FederationAPI FederationAPI `yaml:"federation_api"`  // 联邦 API 配置
	KeyServer     KeyServer     `yaml:"key_server"`      // 密钥服务器配置
	MediaAPI      MediaAPI      `yaml:"media_api"`       // 媒体 API 配置
	RoomServer    RoomServer    `yaml:"room_server"`     // 房间服务器配置
	SyncAPI       SyncAPI       `yaml:"sync_api"`        // 同步 API 配置
	UserAPI       UserAPI       `yaml:"user_api"`        // 用户 API 配置
	RelayAPI      RelayAPI      `yaml:"relay_api"`       // 中继 API 配置
	AIService     AIService     `yaml:"ai_service"`      // AI 服务配置
	RedPacket     RedPacket     `yaml:"redpacket"`       // 红包服务配置

	MSCs MSCs `yaml:"mscs"` // Matrix 规范变更（MSCs）配置

	// dendrite 服务器的追踪配置
	Tracing struct {
		// 设置为 true 以启用追踪钩子。如果为 false，则不设置追踪
		Enabled bool `yaml:"enabled"`
		// jaeger opentracing 报告器的配置
		Jaeger jaegerconfig.Configuration `yaml:"jaeger"`
	} `yaml:"tracing"`

	// 日志信息的配置。每个钩子都将添加到 logrus
	Logging []LogrusHook `yaml:"logging"`

	// 从配置选项派生的任何信息，供以后使用
	Derived Derived `yaml:"-"`
}

// TODO: 移除 Derived
// Derived 包含从配置文件派生的信息
type Derived struct {
	Registration struct {
		// Flows 是流的切片，表示客户端可以验证请求的一种可能方式
		// http://matrix.org/docs/spec/HEAD/client_server/r0.3.0.html#user-interactive-authentication-api
		// 只要生成的流仅依赖于配置文件选项，
		// 我们就可以在启动时生成它们并存储它们直到需要时
		Flows []authtypes.Flow `json:"flows"`

		// 在注册期间需要返回给客户端的参数，
		// 以便完成注册阶段
		Params map[string]interface{} `json:"params"`
	}

	// 从其配置文件解析的应用服务
	// 其路径在上面的主配置文件中给出
	ApplicationServices []ApplicationService

	// 从所有独占应用服务正则表达式编译的元正则表达式
	//
	// 当用户注册时，我们检查其用户名不匹配任何
	// 独占应用服务命名空间
	ExclusiveApplicationServicesUsernameRegexp *regexp.Regexp
	// 当用户创建房间别名时，我们检查它是否已经
	// 被应用服务保留
	ExclusiveApplicationServicesAliasRegexp *regexp.Regexp
	// 注意：房间 ID 的独占正则表达式不是必需的，因为我们不阻止
	// 服务器在独占应用服务命名空间中创建房间 ID
}

// Path 文件系统上的路径
type Path string

// DataSource 用于使用 lib/pq 打开 postgresql 数据库的数据源
type DataSource string

// IsSQLite 检查数据源是否为 SQLite
func (d DataSource) IsSQLite() bool {
	return strings.HasPrefix(string(d), "file:")
}

// IsPostgres 检查数据源是否为 PostgreSQL
func (d DataSource) IsPostgres() bool {
	// 注释的行可能并不总是正确的？
	// return strings.HasPrefix(string(d), "postgres:")
	return !d.IsSQLite()
}

// Topic kafka 中的主题
type Topic string

// FileSizeBytes 文件大小（以字节为单位）
type FileSizeBytes int64

// ThumbnailSize 包含单个缩略图大小配置
type ThumbnailSize struct {
	// 缩略图图像的最大宽度
	Width int `yaml:"width"`
	// 缩略图图像的最大高度
	Height int `yaml:"height"`
	// ResizeMethod 是 crop 或 scale 之一
	// crop 缩放以填充请求的尺寸并裁剪多余部分
	// scale 缩放以适应请求的尺寸，一个维度可能小于请求的尺寸
	ResizeMethod string `yaml:"method,omitempty"`
}

// LogrusHook 表示单个 logrus 钩子。此时，仅对类型和级别的正确值进行解析和验证
// 参数的有效性/完整性检查在配置 logrus 时完成
type LogrusHook struct {
	// 钩子的类型，目前仅支持 "file"
	Type string `yaml:"type"`

	// 要产生的日志级别。将仅输出此级别及以上级别
	Level string `yaml:"level"`

	// 此钩子的参数
	Params map[string]interface{} `yaml:"params"`
}

// ConfigErrors 存储解析配置文件时遇到的问题
// 它实现了 error 接口
type ConfigErrors []string

// Load 加载用于作为多个进程或单体运行的服务器的 yaml 配置文件
// 检查配置以确保其有效
func Load(configPath string) (*Dendrite, error) {
	configData, err := os.ReadFile(configPath) // 读取配置文件
	if err != nil {
		return nil, err
	}
	basePath, err := filepath.Abs(".") // 获取当前工作目录的绝对路径
	if err != nil {
		return nil, err
	}
	// 传递当前工作目录和 os.ReadFile，以便它们可以在测试中被模拟
	return loadConfig(basePath, configData, os.ReadFile)
}

// loadConfig 内部配置加载函数
func loadConfig(
	basePath string, // 基础路径
	configData []byte, // 配置数据
	readFile func(string) ([]byte, error), // 文件读取函数
) (*Dendrite, error) {
	var c Dendrite
	// 设置默认值
	c.Defaults(DefaultOpts{
		Generate:       false, // 不生成配置
		SingleDatabase: true,  // 使用单一数据库
	})

	var err error
	// 解析 YAML 配置
	if err = yaml.Unmarshal(configData, &c); err != nil {
		return nil, err
	}

	// 检查配置有效性
	if err = c.check(); err != nil {
		return nil, err
	}

	// 加载主私钥
	privateKeyPath := absPath(basePath, c.Global.PrivateKeyPath)
	if c.Global.KeyID, c.Global.PrivateKey, err = LoadMatrixKey(privateKeyPath, readFile); err != nil {
		return nil, fmt.Errorf("failed to load private_key: %w", err)
	}

	// 处理虚拟主机配置
	for _, v := range c.Global.VirtualHosts {
		if v.KeyValidityPeriod == 0 { // 如果未设置密钥有效期，使用全局设置
			v.KeyValidityPeriod = c.Global.KeyValidityPeriod
		}
		if v.PrivateKeyPath == "" || v.PrivateKey == nil || v.KeyID == "" {
			// 如果虚拟主机没有自己的密钥，使用全局密钥
			v.KeyID = c.Global.KeyID
			v.PrivateKey = c.Global.PrivateKey
			continue
		}
		// 加载虚拟主机的私钥
		privateKeyPath := absPath(basePath, v.PrivateKeyPath)
		if v.KeyID, v.PrivateKey, err = LoadMatrixKey(privateKeyPath, readFile); err != nil {
			return nil, fmt.Errorf("failed to load private_key for virtualhost %s: %w", v.ServerName, err)
		}
	}

	// 处理旧的验证密钥
	for _, key := range c.Global.OldVerifyKeys {
		switch {
		case key.PrivateKeyPath != "":
			// 从文件加载旧私钥
			var oldPrivateKeyData []byte
			oldPrivateKeyPath := absPath(basePath, key.PrivateKeyPath)
			oldPrivateKeyData, err = readFile(oldPrivateKeyPath)
			if err != nil {
				return nil, fmt.Errorf("failed to read %q: %w", oldPrivateKeyPath, err)
			}

			// NOTSPEC: 通常我们应该强制执行密钥 ID 格式，但由于
			// 由于 Synapse 中缺乏验证，有许多私钥包含不兼容的符号，
			// 我们不会对旧验证密钥强制执行此操作
			keyID, privateKey, perr := readKeyPEM(oldPrivateKeyPath, oldPrivateKeyData, false)
			if perr != nil {
				return nil, fmt.Errorf("failed to parse %q: %w", oldPrivateKeyPath, perr)
			}

			key.KeyID = keyID
			key.PrivateKey = privateKey
			key.PublicKey = spec.Base64Bytes(privateKey.Public().(ed25519.PublicKey))

		case key.KeyID == "":
			return nil, fmt.Errorf("'key_id' must be specified if 'public_key' is specified")

		case len(key.PublicKey) == ed25519.PublicKeySize:
			continue // 公钥长度正确，继续

		case len(key.PublicKey) > 0:
			return nil, fmt.Errorf("the supplied 'public_key' is the wrong length")

		default:
			return nil, fmt.Errorf("either specify a 'private_key' path or supply both 'public_key' and 'key_id'")
		}
	}

	// 设置媒体 API 的绝对基础路径
	c.MediaAPI.AbsBasePath = Path(absPath(basePath, c.MediaAPI.BasePath))

	// 从配置选项生成数据
	err = c.Derive()
	if err != nil {
		return nil, err
	}

	c.Wiring() // 连接各组件
	return &c, nil
}

// LoadMatrixKey 加载 Matrix 密钥
func LoadMatrixKey(privateKeyPath string, readFile func(string) ([]byte, error)) (gomatrixserverlib.KeyID, ed25519.PrivateKey, error) {
	privateKeyData, err := readFile(privateKeyPath) // 读取私钥文件
	if err != nil {
		return "", nil, err
	}
	return readKeyPEM(privateKeyPath, privateKeyData, true) // 解析 PEM 格式的密钥
}

// Derive 生成从配置文件中提供的各种值派生的数据
func (config *Dendrite) Derive() error {
	// 根据配置值确定注册流程

	config.Derived.Registration.Params = make(map[string]interface{})

	// TODO: 添加电子邮件认证类型
	// TODO: 添加 MSISDN 认证类型

	// 默认添加xxai的两个注册方式

	if config.ClientAPI.RecaptchaEnabled {
		// 如果启用了 reCAPTCHA，添加 reCAPTCHA 流程
		config.Derived.Registration.Params[authtypes.LoginTypeRecaptcha] = map[string]string{"public_key": config.ClientAPI.RecaptchaPublicKey}
		config.Derived.Registration.Flows = append(config.Derived.Registration.Flows,
			authtypes.Flow{Stages: []authtypes.LoginType{authtypes.LoginTypeRecaptcha}},
		)
	} else {
		// 否则添加虚拟认证流程
		config.Derived.Registration.Flows = append(config.Derived.Registration.Flows,
			authtypes.Flow{Stages: []authtypes.LoginType{authtypes.LoginTypeDummy}},
		)
	}
	config.Derived.Registration.Flows = append(config.Derived.Registration.Flows,
		authtypes.Flow{Stages: []authtypes.LoginType{authtypes.LoginTypeUserCenter}},
	)

	// 加载应用服务配置文件 // 没有应用服务
	if err := loadAppServices(&config.AppServiceAPI, &config.Derived); err != nil {
		return err
	}

	return nil
}

// DefaultOpts 默认选项结构体
type DefaultOpts struct {
	Generate       bool // 是否生成配置
	SingleDatabase bool // 是否使用单一数据库
}

// Defaults 如果未明确设置，则设置默认配置值
func (c *Dendrite) Defaults(opts DefaultOpts) {
	c.Version = Version // 设置配置版本

	// 为各个组件设置默认值
	c.Global.Defaults(opts)
	c.ClientAPI.Defaults(opts)
	c.FederationAPI.Defaults(opts)
	c.KeyServer.Defaults(opts)
	c.MediaAPI.Defaults(opts)
	c.RoomServer.Defaults(opts)
	c.SyncAPI.Defaults(opts)
	c.UserAPI.Defaults(opts)
	c.AppServiceAPI.Defaults(opts)
	c.RelayAPI.Defaults(opts)
	c.AIService.Defaults(opts)
	c.RedPacket.Defaults(opts)
	c.MSCs.Defaults(opts)
	c.Wiring() // 连接各组件
}

// Verify 验证配置的有效性
func (c *Dendrite) Verify(configErrs *ConfigErrors) {
	type verifiable interface {
		Verify(configErrs *ConfigErrors)
	}
	// 验证所有可验证的组件
	for _, c := range []verifiable{
		&c.Global, &c.ClientAPI, &c.FederationAPI,
		&c.KeyServer, &c.MediaAPI, &c.RoomServer,
		&c.SyncAPI, &c.UserAPI,
		&c.AppServiceAPI, &c.RelayAPI, &c.AIService, &c.RedPacket, &c.MSCs,
	} {
		c.Verify(configErrs)
	}
}

// Wiring 连接各组件的配置引用
func (c *Dendrite) Wiring() {
	// 将全局配置连接到各个组件
	c.Global.JetStream.Matrix = &c.Global
	c.ClientAPI.Matrix = &c.Global
	c.FederationAPI.Matrix = &c.Global
	c.KeyServer.Matrix = &c.Global
	c.MediaAPI.Matrix = &c.Global
	c.RoomServer.Matrix = &c.Global
	c.SyncAPI.Matrix = &c.Global
	c.UserAPI.Matrix = &c.Global
	c.AppServiceAPI.Matrix = &c.Global
	c.RelayAPI.Matrix = &c.Global
	c.AIService.Matrix = &c.Global
	c.RedPacket.Matrix = &c.Global
	c.MSCs.Matrix = &c.Global

	// 连接派生配置和 MSC 配置
	c.ClientAPI.Derived = &c.Derived
	c.AppServiceAPI.Derived = &c.Derived
	c.ClientAPI.MSCs = &c.MSCs
}

// Error 返回详细说明 configErrors 类型中包含多少错误的字符串
func (errs ConfigErrors) Error() string {
	if len(errs) == 1 {
		return errs[0]
	}
	return fmt.Sprintf(
		"%s (and %d other problems)", errs[0], len(errs)-1,
	)
}

// Add 将错误追加到此 configErrors 中的错误列表
// 它是内置 append 的包装器，并向客户端代码隐藏指针
// 此方法可以安全地用于未初始化的 configErrors，因为
// 如果它为 nil，它将被正确分配
func (errs *ConfigErrors) Add(str string) {
	*errs = append(*errs, str)
}

// checkNotEmpty 验证给定值在配置中不为空
// 如果为空，则向列表添加错误
func checkNotEmpty(configErrs *ConfigErrors, key, value string) {
	if value == "" {
		configErrs.Add(fmt.Sprintf("missing config key %q", key))
	}
}

// checkPositive 验证给定值在配置中为正数（包括零）
// 如果不是，则向列表添加错误
func checkPositive(configErrs *ConfigErrors, key string, value int64) {
	if value < 0 {
		configErrs.Add(fmt.Sprintf("invalid value for config key %q: %d", key, value))
	}
}

// checkLogging 验证参数 logging.* 是否有效
func (config *Dendrite) checkLogging(configErrs *ConfigErrors) {
	for _, logrusHook := range config.Logging {
		checkNotEmpty(configErrs, "logging.type", string(logrusHook.Type))
		checkNotEmpty(configErrs, "logging.level", string(logrusHook.Level))
	}
}

// check 返回包含配置文件中发现的所有错误的错误类型
func (config *Dendrite) check() error { // 单体模式
	var configErrs ConfigErrors

	// 检查配置版本
	if config.Version != Version {
		configErrs.Add(fmt.Sprintf(
			"config version is %q, expected %q - this means that the format of the configuration "+
				"file has changed in some significant way, so please revisit the sample config "+
				"and ensure you are not missing any important options that may have been added "+
				"or changed recently!",
			config.Version, Version,
		))
		return configErrs
	}

	config.checkLogging(&configErrs) // 检查日志配置

	// 由于 Golang 管理其接口类型的方式，此条件不是冗余的
	// 为了获得正确的行为，有必要返回显式的 nil
	// 而不是 nil configErrors
	// 这是因为以下等式成立：
	// error(nil) == nil
	// error(configErrors(nil)) != nil
	if configErrs != nil {
		return configErrs
	}
	return nil
}

// absPath 返回给定相对或绝对路径的绝对路径
func absPath(dir string, path Path) string {
	if filepath.IsAbs(string(path)) {
		// filepath.Join 清理路径，所以我们也应该清理绝对路径以保持一致性
		return filepath.Clean(string(path))
	}
	return filepath.Join(dir, string(path))
}

func readKeyPEM(path string, data []byte, enforceKeyIDFormat bool) (gomatrixserverlib.KeyID, ed25519.PrivateKey, error) {
	for {
		var keyBlock *pem.Block
		keyBlock, data = pem.Decode(data)
		if data == nil {
			return "", nil, fmt.Errorf("no matrix private key PEM data in %q", path)
		}
		if keyBlock == nil {
			return "", nil, fmt.Errorf("keyBlock is nil %q", path)
		}
		if keyBlock.Type == "MATRIX PRIVATE KEY" {
			keyID := keyBlock.Headers["Key-ID"]
			if keyID == "" {
				return "", nil, fmt.Errorf("missing key ID in PEM data in %q", path)
			}
			if !strings.HasPrefix(keyID, "ed25519:") {
				return "", nil, fmt.Errorf("key ID %q doesn't start with \"ed25519:\" in %q", keyID, path)
			}
			if enforceKeyIDFormat && !keyIDRegexp.MatchString(keyID) {
				return "", nil, fmt.Errorf("key ID %q in %q contains illegal characters (use a-z, A-Z, 0-9 and _ only)", keyID, path)
			}
			_, privKey, err := ed25519.GenerateKey(bytes.NewReader(keyBlock.Bytes))
			if err != nil {
				return "", nil, err
			}
			return gomatrixserverlib.KeyID(keyID), privKey, nil
		}
	}
}

// SetupTracing configures the opentracing using the supplied configuration.
func (config *Dendrite) SetupTracing() (closer io.Closer, err error) {
	if !config.Tracing.Enabled {
		return io.NopCloser(bytes.NewReader([]byte{})), nil
	}
	return config.Tracing.Jaeger.InitGlobalTracer(
		"Dendrite",
		jaegerconfig.Logger(logrusLogger{logrus.StandardLogger()}),
		jaegerconfig.Metrics(jaegermetrics.NullFactory),
	)
}

// logrusLogger is a small wrapper that implements jaeger.Logger using logrus.
type logrusLogger struct {
	l *logrus.Logger
}

func (l logrusLogger) Error(msg string) {
	l.l.Error(msg)
}

func (l logrusLogger) Infof(msg string, args ...interface{}) {
	l.l.Infof(msg, args...)
}
