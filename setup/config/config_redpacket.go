// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包服务配置包
package config

// RedPacket 红包服务配置结构
type RedPacket struct {
	Matrix *Global `yaml:"-"` // 全局配置引用

	// 是否启用红包服务
	Enabled bool `yaml:"enabled"`
	
	// 红包最大金额限制
	MaxAmount float64 `yaml:"max_amount"`
	
	// 红包最大个数限制
	MaxCount int `yaml:"max_count"`
	
	// 红包默认过期时间（秒）
	DefaultExpiresIn int `yaml:"default_expires_in"`
	
	// 红包最小金额
	MinAmount float64 `yaml:"min_amount"`
	
	// 是否允许拼手气红包
	AllowLuckyRedPacket bool `yaml:"allow_lucky_redpacket"`
	
	// 数据库配置
	Database DatabaseOptions `yaml:"database"`
}

// Defaults 设置红包服务的默认配置值
func (c *RedPacket) Defaults(opts DefaultOpts) {
	c.Enabled = false
	c.MaxAmount = 200.0      // 最大200元
	c.MaxCount = 100         // 最多100个
	c.DefaultExpiresIn = 86400 // 24小时
	c.MinAmount = 0.01       // 最小0.01元
	c.AllowLuckyRedPacket = true
	
	if opts.Generate {
		if !opts.SingleDatabase {
			c.Database.ConnectionString = "**************************************************************************"
		}
	}
}

// Verify 验证红包服务配置的有效性
func (c *RedPacket) Verify(configErrs *ConfigErrors) {
	if c.Enabled {
		// 验证金额限制
		if c.MaxAmount <= 0 {
			configErrs.Add("redpacket.max_amount must be positive")
		}
		if c.MinAmount <= 0 {
			configErrs.Add("redpacket.min_amount must be positive")
		}
		if c.MinAmount >= c.MaxAmount {
			configErrs.Add("redpacket.min_amount must be less than max_amount")
		}
		
		// 验证个数限制
		if c.MaxCount <= 0 {
			configErrs.Add("redpacket.max_count must be positive")
		}
		
		// 验证过期时间
		if c.DefaultExpiresIn <= 0 {
			configErrs.Add("redpacket.default_expires_in must be positive")
		}
		
		// 验证数据库配置
		c.Database.Verify(configErrs)
	}
}