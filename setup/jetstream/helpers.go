package jetstream

import (
	"context"
	"errors"
	"fmt"

	"github.com/getsentry/sentry-go"
	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
)

// JetStreamConsumer starts a durable consumer on the given subject with the
// given durable name. The function will be called when one or more messages
// is available, up to the maximum batch size specified. If the batch is set to
// 1 then messages will be delivered one at a time. If the function is called,
// the messages array is guaranteed to be at least 1 in size. Any provided NATS
// options will be passed through to the pull subscriber creation. The consumer
// will continue to run until the context expires, at which point it will stop.
func JetStreamConsumer(
	ctx context.Context, js nats.JetStreamContext, subj, durable string, batch int, // 上下文、JetStream实例、主题、持久化名称、批处理大小
	f func(ctx context.Context, msgs []*nats.Msg) bool, // 消息处理函数，返回true表示成功处理
	opts ...nats.SubOpt, // NATS订阅选项
) error {
	defer func(durable string) { // 延迟执行清理函数，清理旧的推送消费者
		// If there are existing consumers from before they were pull
		// consumers, we need to clean up the old push consumers. However,
		// in order to not affect the interest-based policies, we need to
		// do this *after* creating the new pull consumers, which have
		// "Pull" suffixed to their name.
		if _, err := js.ConsumerInfo(subj, durable); err == nil { // 检查是否存在旧的消费者
			if err := js.DeleteConsumer(subj, durable); err != nil { // 删除旧的推送消费者
				logrus.WithContext(ctx).Warnf("Failed to clean up old consumer %q", durable) // 记录清理失败的警告
			}
		}
	}(durable)

	durable = durable + "Pull" // 为拉取消费者添加"Pull"后缀，区分推送和拉取消费者
	sub, err := js.PullSubscribe(subj, durable, opts...) // 创建拉取订阅，使用持久化消费者
	if err != nil {
		sentry.CaptureException(err) // 将错误发送到Sentry错误监控系统
		logrus.WithContext(ctx).WithError(err).Warnf("Failed to configure durable %q", durable) // 记录配置失败的警告
		return err // 返回错误
	}
	go jetStreamConsumerWorker(ctx, sub, subj, batch, f) // 启动后台工作协程处理消息
	return nil // 返回成功
}

func jetStreamConsumerWorker(
	ctx context.Context, sub *nats.Subscription, subj string, batch int, // 上下文、订阅对象、主题、批处理大小
	f func(ctx context.Context, msgs []*nats.Msg) bool, // 消息处理函数
) {
	for { // 无限循环处理消息
		// If the parent context has given up then there's no point in
		// carrying on doing anything, so stop the listener.
		select {
		case <-ctx.Done(): // 检查上下文是否已取消
			return // 如果上下文取消，退出工作协程
		default:
		}
		// The context behaviour here is surprising — we supply a context
		// so that we can interrupt the fetch if we want, but NATS will still
		// enforce its own deadline (roughly 5 seconds by default). Therefore
		// it is our responsibility to check whether our context expired or
		// not when a context error is returned. Footguns. Footguns everywhere.
		msgs, err := sub.Fetch(batch, nats.Context(ctx)) // 从JetStream拉取指定数量的消息
		if err != nil {
			if err == context.Canceled || err == context.DeadlineExceeded { // 检查是否为上下文相关错误
				// Work out whether it was the JetStream context that expired
				// or whether it was our supplied context.
				select {
				case <-ctx.Done(): // 检查是我们的上下文过期了
					// The supplied context expired, so we want to stop the
					// consumer altogether.
					return // 我们的上下文过期，停止消费者
				default:
					// The JetStream context expired, so the fetch probably
					// just timed out and we should try again.
					continue // JetStream上下文过期（超时），继续重试
				}
			} else if errors.Is(err, nats.ErrTimeout) { // 检查是否为超时错误
				// Pull request was invalidated, try again.
				continue // 拉取请求超时，重试
			} else if errors.Is(err, nats.ErrConsumerLeadershipChanged) { // 检查是否为领导权变更错误
				// Leadership changed so pending pull requests became invalidated,
				// just try again.
				continue // 集群领导权变更，待处理的拉取请求失效，重试
			} else if err.Error() == "nats: Server Shutdown" { // 检查是否为服务器关闭错误
				// The server is shutting down, but we'll rely on reconnect
				// behaviour to try and either connect us to another node (if
				// clustered) or to reconnect when the server comes back up.
				continue // 服务器关闭，依赖重连机制连接到其他节点或等待服务器重启
			} else {
				// Something else went wrong.
				logrus.WithContext(ctx).WithField("subject", subj).WithError(err).Warn("Error on pull subscriber fetch") // 记录其他类型的错误
				return // 其他错误，退出工作协程
			}
		}
		if len(msgs) < 1 { // 检查是否获取到消息
			continue // 没有消息，继续下一次循环
		}
		for _, msg := range msgs { // 遍历获取到的消息
			if err = msg.InProgress(nats.Context(ctx)); err != nil { // 标记消息为处理中状态，防止重复投递
				logrus.WithContext(ctx).WithField("subject", subj).Warn(fmt.Errorf("msg.InProgress: %w", err)) // 记录标记失败的警告
				sentry.CaptureException(err) // 发送错误到Sentry
				continue // 跳过这条消息，处理下一条
			}
		}
		if f(ctx, msgs) { // 调用用户提供的消息处理函数
			for _, msg := range msgs { // 如果处理成功，确认所有消息
				if err = msg.AckSync(nats.Context(ctx)); err != nil { // 同步确认消息已处理
					logrus.WithContext(ctx).WithField("subject", subj).Warn(fmt.Errorf("msg.AckSync: %w", err)) // 记录确认失败的警告
					sentry.CaptureException(err) // 发送错误到Sentry
				}
			}
		} else { // 如果处理失败
			for _, msg := range msgs { // 否定确认所有消息，让JetStream重新投递
				if err = msg.Nak(nats.Context(ctx)); err != nil { // 发送NAK（否定确认），消息将被重新投递
					logrus.WithContext(ctx).WithField("subject", subj).Warn(fmt.Errorf("msg.Nak: %w", err)) // 记录NAK失败的警告
					sentry.CaptureException(err) // 发送错误到Sentry
				}
			}
		}
	}
}
