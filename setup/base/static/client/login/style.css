html {
    height: 100%;
}

body {
    height: 100%;
    font-family: "Myriad Pro", "Myriad", Helvetica, Arial, sans-serif;
    font-size: 12pt;
    margin: 0px;
}

h1 {
    font-size: 20pt;
}

a:link    { color: #666; }
a:visited { color: #666; }
a:hover   { color: #000; }
a:active  { color: #000; }

input {
   margin: 5px;
}

textbox, input[type="text"], input[type="password"] {
   width: 90%;
}

form {
    text-align: center;
    margin: 10px 0 0 0;
}

ul.radiobuttons {
    text-align: left;
    list-style: none;
}

/*
 * Add some padding to the viewport.
 */
#container {
    padding: 10px;
}
/*
 * Center all direct children of the main form.
 */
#container > * {
    display: block;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
}

/*
 * A wrapper around each login flow.
 */
.login_flow {
    width: 300px;
    text-align: left;
    padding: 10px;
    margin-bottom: 40px;

    border-radius: 10px;
    box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.15);

    background-color: #f8f8f8;
    border: 1px #ccc solid;
}

/*
 * Used to show error content.
 */
#feedback {
    /* Red text. */
    color: #ff0000;
    /* A little space to not overlap the box-shadow. */
    margin-bottom: 20px;
}
