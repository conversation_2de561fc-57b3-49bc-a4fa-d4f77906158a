# Matrix协议测试用例列表（带中文注释）

# 注册相关测试 (Registration Tests)
reg GET /register yields a set of flows                                    # 获取注册流程列表
reg POST /register can create a user                                       # 可以创建用户
reg POST /register downcases capitals in usernames                         # 用户名中的大写字母会被转换为小写
reg POST /register returns the same device_id as that in the request       # 返回请求中相同的设备ID
reg POST /register rejects registration of usernames with '!'              # 拒绝包含'!'的用户名注册
reg POST /register rejects registration of usernames with '"'              # 拒绝包含'"'的用户名注册
reg POST /register rejects registration of usernames with ':'              # 拒绝包含':'的用户名注册
reg POST /register rejects registration of usernames with '?'              # 拒绝包含'?'的用户名注册
reg POST /register rejects registration of usernames with '\'              # 拒绝包含'\'的用户名注册
reg POST /register rejects registration of usernames with '@'              # 拒绝包含'@'的用户名注册
reg POST /register rejects registration of usernames with '['              # 拒绝包含'['的用户名注册
reg POST /register rejects registration of usernames with ']'              # 拒绝包含']'的用户名注册
reg POST /register rejects registration of usernames with '{'              # 拒绝包含'{'的用户名注册
reg POST /register rejects registration of usernames with '|'              # 拒绝包含'|'的用户名注册
reg POST /register rejects registration of usernames with '}'              # 拒绝包含'}'的用户名注册
reg POST /register rejects registration of usernames with '£'              # 拒绝包含'£'的用户名注册
reg POST /register rejects registration of usernames with 'é'              # 拒绝包含'é'的用户名注册
reg POST /register rejects registration of usernames with '\n'             # 拒绝包含换行符的用户名注册
reg POST /register rejects registration of usernames with '''              # 拒绝包含单引号的用户名注册
reg POST /register allows registration of usernames with 'q'               # 允许包含'q'的用户名注册
reg POST /register allows registration of usernames with '3'               # 允许包含'3'的用户名注册
reg POST /register allows registration of usernames with '.'               # 允许包含'.'的用户名注册
reg POST /register allows registration of usernames with '_'               # 允许包含'_'的用户名注册
reg POST /register allows registration of usernames with '='               # 允许包含'='的用户名注册
reg POST /register allows registration of usernames with '-'               # 允许包含'-'的用户名注册
reg POST /register allows registration of usernames with '/'               # 允许包含'/'的用户名注册
reg POST /r0/admin/register with shared secret                             # 使用共享密钥进行管理员注册
reg POST /r0/admin/register admin with shared secret                       # 使用共享密钥注册管理员用户
reg POST /r0/admin/register with shared secret downcases capitals          # 共享密钥注册时大写字母转小写
reg POST /r0/admin/register with shared secret disallows symbols           # 共享密钥注册时禁止特殊符号
reg POST rejects invalid utf-8 in JSON                                     # 拒绝JSON中的无效UTF-8字符

# 登录相关测试 (Login Tests)
log GET /login yields a set of flows                                       # 获取登录流程列表
log POST /login can log in as a user                                       # 可以以用户身份登录
log POST /login returns the same device_id as that in the request          # 返回请求中相同的设备ID
log POST /login can log in as a user with just the local part of the id    # 可以仅使用用户ID的本地部分登录
log POST /login as non-existing user is rejected                           # 拒绝不存在用户的登录
log POST /login wrong password is rejected                                 # 拒绝错误密码的登录
log Interactive authentication types include SSO                           # 交互式认证类型包括SSO
log Can perform interactive authentication with SSO                        # 可以使用SSO进行交互式认证
log The user must be consistent through an interactive authentication session with SSO  # SSO交互式认证会话中用户必须保持一致
log The operation must be consistent through an interactive authentication session      # 交互式认证会话中操作必须保持一致

# v1同步相关测试 (v1 Sync Tests)
v1s GET /events initially                                                  # 初始获取事件
v1s GET /initialSync initially                                             # 初始同步

# 版本相关测试 (Version Tests)
csa Version responds 200 OK with valid structure                           # 版本接口返回200和有效结构

# 用户资料相关测试 (Profile Tests)
pro PUT /profile/:user_id/displayname sets my name                         # 设置用户显示名称
pro GET /profile/:user_id/displayname publicly accessible                  # 公开访问用户显示名称
pro PUT /profile/:user_id/avatar_url sets my avatar                        # 设置用户头像URL
pro GET /profile/:user_id/avatar_url publicly accessible                   # 公开访问用户头像URL

# 设备相关测试 (Device Tests)
dev GET /device/{deviceId}                                                 # 获取设备信息
dev GET /device/{deviceId} gives a 404 for unknown devices                 # 未知设备返回404
dev GET /devices                                                           # 获取设备列表
dev PUT /device/{deviceId} updates device fields                           # 更新设备字段
dev PUT /device/{deviceId} gives a 404 for unknown devices                 # 未知设备更新返回404
dev DELETE /device/{deviceId}                                              # 删除设备
dev DELETE /device/{deviceId} requires UI auth user to match device owner  # 删除设备需要UI认证用户匹配设备所有者
dev DELETE /device/{deviceId} with no body gives a 401                     # 无请求体删除设备返回401
dev The deleted device must be consistent through an interactive auth session  # 删除的设备在交互式认证会话中必须保持一致
dev Users receive device_list updates for their own devices                # 用户接收自己设备的设备列表更新

# 在线状态相关测试 (Presence Tests)
pre GET /presence/:user_id/status fetches initial status                   # 获取初始在线状态
pre PUT /presence/:user_id/status updates my presence                      # 更新我的在线状态

# 创建房间相关测试 (Create Room Tests)
crm POST /createRoom makes a public room                                   # 创建公共房间
crm POST /createRoom makes a private room                                  # 创建私有房间
crm POST /createRoom makes a private room with invites                     # 创建带邀请的私有房间
crm POST /createRoom makes a room with a name                              # 创建带名称的房间
crm POST /createRoom makes a room with a topic                             # 创建带主题的房间

# 同步相关测试 (Sync Tests)
syn Can /sync newly created room                                           # 可以同步新创建的房间
crm POST /createRoom creates a room with the given version                 # 创建指定版本的房间
crm POST /createRoom rejects attempts to create rooms with numeric versions # 拒绝创建数字版本的房间
crm POST /createRoom rejects attempts to create rooms with unknown versions # 拒绝创建未知版本的房间
crm POST /createRoom ignores attempts to set the room version via creation_content # 忽略通过creation_content设置房间版本的尝试

# 成员相关测试 (Membership Tests)
mem GET /rooms/:room_id/state/m.room.member/:user_id fetches my membership # 获取我的成员身份
mem GET /rooms/:room_id/state/m.room.member/:user_id?format=event fetches my membership event # 获取我的成员身份事件

# 房间状态相关测试 (Room State Tests)
rst GET /rooms/:room_id/state/m.room.power_levels fetches powerlevels      # 获取权限级别
mem GET /rooms/:room_id/joined_members fetches my membership               # 获取我的成员身份
v1s GET /rooms/:room_id/initialSync fetches initial sync state             # 获取初始同步状态

# 公共房间相关测试 (Public Rooms Tests)
pub GET /publicRooms lists newly-created room                              # 列出新创建的房间

# 别名相关测试 (Alias Tests)
ali GET /directory/room/:room_alias yields room ID                         # 通过房间别名获取房间ID
mem GET /joined_rooms lists newly-created room                             # 列出新创建的房间
rst POST /rooms/:room_id/state/m.room.name sets name                       # 设置房间名称
rst GET /rooms/:room_id/state/m.room.name gets name                        # 获取房间名称
rst POST /rooms/:room_id/state/m.room.topic sets topic                     # 设置房间主题
rst GET /rooms/:room_id/state/m.room.topic gets topic                      # 获取房间主题
rst GET /rooms/:room_id/state fetches entire room state                    # 获取整个房间状态
crm POST /createRoom with creation content                                 # 使用创建内容创建房间
ali PUT /directory/room/:room_alias creates alias                          # 创建房间别名
ali GET /rooms/:room_id/aliases lists aliases                              # 列出房间别名

# 加入房间相关测试 (Join Room Tests)
jon POST /rooms/:room_id/join can join a room                              # 可以加入房间
jon POST /join/:room_alias can join a room                                 # 可以通过别名加入房间
jon POST /join/:room_id can join a room                                    # 可以通过房间ID加入房间
jon POST /join/:room_id can join a room with custom content                # 可以使用自定义内容加入房间
jon POST /join/:room_alias can join a room with custom content             # 可以使用自定义内容通过别名加入房间

# 离开房间相关测试 (Leave Room Tests)
lev POST /rooms/:room_id/leave can leave a room                            # 可以离开房间

# 邀请相关测试 (Invite Tests)
inv POST /rooms/:room_id/invite can send an invite                         # 可以发送邀请

# 封禁相关测试 (Ban Tests)
ban POST /rooms/:room_id/ban can ban a user                                # 可以封禁用户

# 发送消息相关测试 (Send Message Tests)
snd POST /rooms/:room_id/send/:event_type sends a message                  # 发送消息
snd PUT /rooms/:room_id/send/:event_type/:txn_id sends a message           # 使用事务ID发送消息
snd PUT /rooms/:room_id/send/:event_type/:txn_id deduplicates the same txn id # 相同事务ID去重

# 获取消息相关测试 (Get Message Tests)
get GET /rooms/:room_id/messages returns a message                         # 返回消息
get GET /rooms/:room_id/messages lazy loads members correctly              # 正确懒加载成员

# 打字通知相关测试 (Typing Tests)
typ PUT /rooms/:room_id/typing/:user_id sets typing notification           # 设置打字通知
typ Typing notifications don't leak (3 subtests)                           # 打字通知不泄露（3个子测试）

# 房间状态相关测试 (Room State Tests)
rst GET /rooms/:room_id/state/m.room.power_levels can fetch levels         # 可以获取权限级别
rst PUT /rooms/:room_id/state/m.room.power_levels can set levels           # 可以设置权限级别
rst PUT power_levels should not explode if the old power levels were empty # 如果旧权限级别为空，设置权限级别不应崩溃
rst Users cannot set notifications powerlevel higher than their own (2 subtests) # 用户不能设置比自己更高的通知权限级别（2个子测试）
rst Both GET and PUT work                                                  # GET和PUT都工作

# 已读回执相关测试 (Receipt Tests)
rct POST /rooms/:room_id/receipt can create receipts                       # 可以创建已读回执

# 已读标记相关测试 (Read Marker Tests)
red POST /rooms/:room_id/read_markers can create read marker               # 可以创建已读标记

# 媒体相关测试 (Media Tests)
med POST /media/v3/upload can create an upload                             # 可以创建上传
med POST /media/r0/upload can create an upload                             # 可以创建上传（r0版本）
med GET /media/v3/download can fetch the value again                       # 可以再次获取值
med GET /media/r0/download can fetch the value again                       # 可以再次获取值（r0版本）

# 能力相关测试 (Capabilities Tests)
cap GET /capabilities is present and well formed for registered user       # 注册用户的能力接口存在且格式正确
cap GET /r0/capabilities is not public                                     # r0能力接口不是公开的
cap GET /v3/capabilities is not public                                     # v3能力接口不是公开的

# 注册相关测试（续）
reg Register with a recaptcha                                              # 使用验证码注册
reg registration is idempotent, without username specified                 # 注册是幂等的，未指定用户名
reg registration is idempotent, with username specified                    # 注册是幂等的，指定用户名
reg registration remembers parameters                                      # 注册记住参数
reg registration accepts non-ascii passwords                               # 注册接受非ASCII密码
reg registration with inhibit_login inhibits login                         # 使用inhibit_login的注册禁止登录
reg User signups are forbidden from starting with '_'                      # 禁止用户注册以'_'开头的用户名
reg Can register using an email address                                    # 可以使用邮箱地址注册

# 登录相关测试（续）
log Can login with 3pid and password using m.login.password                # 可以使用3PID和密码通过m.login.password登录
log login types include SSO                                                # 登录类型包括SSO
log /login/cas/redirect redirects if the old m.login.cas login type is listed # 如果列出旧的m.login.cas登录类型，/login/cas/redirect会重定向
log Can login with new user via CAS                                        # 可以通过CAS使用新用户登录

# 登出相关测试 (Logout Tests)
lox Can logout current device                                              # 可以登出当前设备
lox Can logout all devices                                                 # 可以登出所有设备
lox Request to logout with invalid an access token is rejected             # 使用无效访问令牌的登出请求被拒绝
lox Request to logout without an access token is rejected                  # 没有访问令牌的登出请求被拒绝

# 密码相关测试 (Password Tests)
log After changing password, can't log in with old password                # 更改密码后，无法使用旧密码登录
log After changing password, can log in with new password                  # 更改密码后，可以使用新密码登录
log After changing password, existing session still works                  # 更改密码后，现有会话仍然有效
log After changing password, a different session no longer works by default # 更改密码后，默认情况下不同会话不再有效
log After changing password, different sessions can optionally be kept     # 更改密码后，可以选择保留不同会话

# 推送相关测试 (Push Tests)
psh Pushers created with a different access token are deleted on password change # 使用不同访问令牌创建的推送器在密码更改时被删除
psh Pushers created with a the same access token are not deleted on password change # 使用相同访问令牌创建的推送器在密码更改时不被删除

# 账户相关测试 (Account Tests)
acc Can deactivate account                                                 # 可以停用账户
acc Can't deactivate account with wrong password                           # 无法使用错误密码停用账户
acc After deactivating account, can't log in with password                 # 停用账户后，无法使用密码登录
acc After deactivating account, can't log in with an email                 # 停用账户后，无法使用邮箱登录

# v1同步相关测试（续）
v1s initialSync sees my presence status                                    # 初始同步看到我的在线状态

# 在线状态相关测试（续）
pre Presence change reports an event to myself                             # 在线状态更改向自己报告事件
pre Friends presence changes reports events                                # 朋友在线状态更改报告事件

# 创建房间相关测试（续）
crm Room creation reports m.room.create to myself                          # 房间创建向自己报告m.room.create
crm Room creation reports m.room.member to myself                          # 房间创建向自己报告m.room.member

# 房间状态相关测试（续）
rst Setting room topic reports m.room.topic to myself                      # 设置房间主题向自己报告m.room.topic

# v1同步相关测试（续）
v1s Global initialSync                                                     # 全局初始同步
v1s Global initialSync with limit=0 gives no messages                      # limit=0的全局初始同步不返回消息
v1s Room initialSync                                                       # 房间初始同步
v1s Room initialSync with limit=0 gives no messages                        # limit=0的房间初始同步不返回消息

# 房间状态相关测试（续）
rst Setting state twice is idempotent                                      # 设置状态两次是幂等的

# 加入房间相关测试（续）
jon Joining room twice is idempotent                                       # 加入房间两次是幂等的

# 同步相关测试（续）
syn New room members see their own join event                              # 新房间成员看到自己的加入事件

# v1同步相关测试（续）
v1s New room members see existing users' presence in room initialSync      # 新房间成员在房间初始同步中看到现有用户的在线状态

# 同步相关测试（续）
syn Existing members see new members' join events                          # 现有成员看到新成员的加入事件

# 在线状态相关测试（续）
pre Existing members see new members' presence                             # 现有成员看到新成员的在线状态

# v1同步相关测试（续）
v1s All room members see all room members' presence in global initialSync  # 所有房间成员在全局初始同步中看到所有房间成员的在线状态

# 联邦加入房间测试 (Federation Join Tests)
f,jon Remote users can join room by alias                                  # 远程用户可以通过别名加入房间

# 同步相关测试（续）
syn New room members see their own join event                              # 新房间成员看到自己的加入事件

# v1同步相关测试（续）
v1s New room members see existing members' presence in room initialSync    # 新房间成员在房间初始同步中看到现有成员的在线状态

# 同步相关测试（续）
syn Existing members see new members' join events                          # 现有成员看到新成员的加入事件

# 在线状态相关测试（续）
pre Existing members see new member's presence                             # 现有成员看到新成员的在线状态

# v1同步相关测试（续）
v1s New room members see first user's profile information in global initialSync # 新房间成员在全局初始同步中看到第一个用户的资料信息
v1s New room members see first user's profile information in per-room initialSync # 新房间成员在每房间初始同步中看到第一个用户的资料信息

# 联邦加入房间测试（续）
f,jon Remote users may not join unfederated rooms                          # 远程用户不能加入非联邦房间

# 同步相关测试（续）
syn Local room members see posted message events                           # 本地房间成员看到发布的消息事件

# v1同步相关测试（续）
v1s Fetching eventstream a second time doesn't yield the message again     # 第二次获取事件流不会再次产生消息

# 同步相关测试（续）
syn Local non-members don't see posted message events                      # 本地非成员不看到发布的消息事件

# 获取消息相关测试（续）
get Local room members can get room messages                               # 本地房间成员可以获取房间消息

# 联邦同步测试 (Federation Sync Tests)
f,syn Remote room members also see posted message events                   # 远程房间成员也看到发布的消息事件

# 联邦获取消息测试 (Federation Get Message Tests)
f,get Remote room members can get room messages                            # 远程房间成员可以获取房间消息

# 获取消息相关测试（续）
get Message history can be paginated                                       # 消息历史可以分页

# 联邦获取消息测试（续）
f,get Message history can be paginated over federation                     # 消息历史可以通过联邦分页

# 临时消息测试 (Ephemeral Message Tests)
eph Ephemeral messages received from clients are correctly expired         # 从客户端接收的临时消息正确过期

# 别名相关测试（续）
ali Room aliases can contain Unicode                                       # 房间别名可以包含Unicode

# 联邦别名测试 (Federation Alias Tests)
f,ali Remote room alias queries can handle Unicode                         # 远程房间别名查询可以处理Unicode

# 别名相关测试（续）
ali Canonical alias can be set                                             # 可以设置规范别名
ali Canonical alias can be set (3 subtests)                               # 可以设置规范别名（3个子测试）
ali Canonical alias can include alt_aliases                                # 规范别名可以包含备用别名
ali Canonical alias can include alt_aliases (4 subtests)                   # 规范别名可以包含备用别名（4个子测试）
ali Regular users can add and delete aliases in the default room configuration # 普通用户可以在默认房间配置中添加和删除别名
ali Regular users can add and delete aliases when m.room.aliases is restricted # 当m.room.aliases受限时，普通用户可以添加和删除别名
ali Deleting a non-existent alias should return a 404                      # 删除不存在的别名应返回404
ali Users can't delete other's aliases                                     # 用户不能删除其他人的别名
ali Users with sufficient power-level can delete other's aliases           # 具有足够权限级别的用户可以删除其他人的别名
ali Can delete canonical alias                                             # 可以删除规范别名
ali Alias creators can delete alias with no ops                            # 别名创建者可以在没有操作员的情况下删除别名
ali Alias creators can delete canonical alias with no ops                  # 别名创建者可以在没有操作员的情况下删除规范别名

# MSC相关测试 (MSC Tests)
msc Only room members can list aliases of a room                           # 只有房间成员可以列出房间的别名

# 邀请相关测试（续）
inv Can invite users to invite-only rooms                                  # 可以邀请用户到仅邀请房间
inv Uninvited users cannot join the room                                   # 未受邀用户无法加入房间
inv Invited user can reject invite                                         # 受邀用户可以拒绝邀请

# 联邦邀请测试 (Federation Invite Tests)
f,inv Invited user can reject invite over federation                       # 受邀用户可以通过联邦拒绝邀请
f,inv Invited user can reject invite over federation several times         # 受邀用户可以通过联邦多次拒绝邀请

# 邀请相关测试（续）
inv Invited user can reject invite for empty room                          # 受邀用户可以拒绝空房间的邀请

# 联邦邀请测试（续）
f,inv Invited user can reject invite over federation for empty room        # 受邀用户可以通过联邦拒绝空房间的邀请

# 邀请相关测试（续）
inv Invited user can reject local invite after originator leaves           # 受邀用户可以在发起者离开后拒绝本地邀请
inv Invited user can see room metadata                                     # 受邀用户可以看到房间元数据

# 联邦邀请测试（续）
f,inv Remote invited user can see room metadata                            # 远程受邀用户可以看到房间元数据

# 邀请相关测试（续）
inv Users cannot invite themselves to a room                               # 用户不能邀请自己到房间
inv Users cannot invite a user that is already in the room                 # 用户不能邀请已经在房间中的用户

# 封禁相关测试（续）
ban Banned user is kicked and may not rejoin until unbanned                # 被封禁用户被踢出且在解封前不能重新加入

# 联邦封禁测试 (Federation Ban Tests)
f,ban Remote banned user is kicked and may not rejoin until unbanned       # 远程被封禁用户被踢出且在解封前不能重新加入

# 封禁相关测试（续）
ban 'ban' event respects room powerlevel                                   # 'ban'事件遵守房间权限级别

# 权限级别测试 (Power Level Tests)
plv setting 'm.room.name' respects room powerlevel                         # 设置'm.room.name'遵守房间权限级别
plv setting 'm.room.power_levels' respects room powerlevel (2 subtests)    # 设置'm.room.power_levels'遵守房间权限级别（2个子测试）
plv Unprivileged users can set m.room.topic if it only needs level 0       # 如果只需要级别0，无特权用户可以设置m.room.topic
plv Users cannot set ban powerlevel higher than their own (2 subtests)     # 用户不能设置比自己更高的封禁权限级别（2个子测试）
plv Users cannot set kick powerlevel higher than their own (2 subtests)    # 用户不能设置比自己更高的踢出权限级别（2个子测试）
plv Users cannot set redact powerlevel higher than their own (2 subtests)  # 用户不能设置比自己更高的编辑权限级别（2个子测试）

# v1同步相关测试（续）
v1s Check that event streams started after a client joined a room work (SYT-1) # 检查客户端加入房间后启动的事件流是否工作（SYT-1）
v1s Event stream catches up fully after many messages                      # 事件流在许多消息后完全追赶

# 编辑相关测试 (Redaction Tests)
xxx PUT /rooms/:room_id/redact/:event_id/:txn_id as power user redacts message # 作为权力用户编辑消息
xxx PUT /rooms/:room_id/redact/:event_id/:txn_id as original message sender redacts message # 作为原始消息发送者编辑消息
xxx PUT /rooms/:room_id/redact/:event_id/:txn_id as random user does not redact message # 作为随机用户不编辑消息
xxx PUT /redact disallows redaction of event in different room              # 不允许编辑不同房间中的事件
xxx Redaction of a redaction redacts the redaction reason                   # 编辑的编辑会编辑编辑原因
xxx PUT /rooms/:room_id/redact/:event_id/:txn_id is idempotent              # 编辑是幂等的

# v1同步相关测试（续）
v1s A departed room is still included in /initialSync (SPEC-216)           # 离开的房间仍包含在/initialSync中（SPEC-216）
v1s Can get rooms/{roomId}/initialSync for a departed room (SPEC-216)      # 可以获取离开房间的rooms/{roomId}/initialSync（SPEC-216）

# 房间状态相关测试（续）
rst Can get rooms/{roomId}/state for a departed room (SPEC-216)            # 可以获取离开房间的rooms/{roomId}/state（SPEC-216）

# 成员相关测试（续）
mem Can get rooms/{roomId}/members for a departed room (SPEC-216)          # 可以获取离开房间的rooms/{roomId}/members（SPEC-216）

# 获取消息相关测试（续）
get Can get rooms/{roomId}/messages for a departed room (SPEC-216)         # 可以获取离开房间的rooms/{roomId}/messages（SPEC-216）

# 房间状态相关测试（续）
rst Can get 'm.room.name' state for a departed room (SPEC-216)             # 可以获取离开房间的'm.room.name'状态（SPEC-216）

# 同步相关测试（续）
syn Getting messages going forward is limited for a departed room (SPEC-216) # 获取离开房间的前向消息是有限的（SPEC-216）

# 第三方ID测试 (Third Party ID Tests)
3pd Can invite existing 3pid                                               # 可以邀请现有的3PID
3pd Can invite existing 3pid with no ops into a private room               # 可以在没有操作员的情况下邀请现有3PID到私有房间
3pd Can invite existing 3pid in createRoom                                 # 可以在createRoom中邀请现有3PID
3pd Can invite unbound 3pid                                                # 可以邀请未绑定的3PID

# 联邦第三方ID测试 (Federation Third Party ID Tests)
f,3pd Can invite unbound 3pid over federation                              # 可以通过联邦邀请未绑定的3PID

# 第三方ID测试（续）
3pd Can invite unbound 3pid with no ops into a private room                # 可以在没有操作员的情况下邀请未绑定3PID到私有房间

# 联邦第三方ID测试（续）
f,3pd Can invite unbound 3pid over federation with no ops into a private room # 可以通过联邦在没有操作员的情况下邀请未绑定3PID到私有房间
f,3pd Can invite unbound 3pid over federation with users from both servers # 可以通过联邦邀请来自两个服务器的用户的未绑定3PID

# 第三方ID测试（续）
3pd Can accept unbound 3pid invite after inviter leaves                    # 可以在邀请者离开后接受未绑定3PID邀请
3pd Can accept third party invite with /join                               # 可以使用/join接受第三方邀请
3pd 3pid invite join with wrong but valid signature are rejected           # 使用错误但有效签名的3PID邀请加入被拒绝
3pd 3pid invite join valid signature but revoked keys are rejected         # 有效签名但撤销密钥的3PID邀请加入被拒绝
3pd 3pid invite join valid signature but unreachable ID server are rejected # 有效签名但无法访问ID服务器的3PID邀请加入被拒绝

# 访客相关测试 (Guest Tests)
gst Guest user cannot call /events globally                                # 访客用户无法全局调用/events
gst Guest users can join guest_access rooms                                # 访客用户可以加入guest_access房间
gst Guest users can send messages to guest_access rooms if joined          # 访客用户如果已加入可以向guest_access房间发送消息
gst Guest user calling /events doesn't tightloop                           # 访客用户调用/events不会紧密循环
gst Guest users are kicked from guest_access rooms on revocation of guest_access # 撤销guest_access时访客用户被踢出guest_access房间
gst Guest user can set display names                                       # 访客用户可以设置显示名称
gst Guest users are kicked from guest_access rooms on revocation of guest_access over federation # 通过联邦撤销guest_access时访客用户被踢出guest_access房间
gst Guest user can upgrade to fully featured user                          # 访客用户可以升级为完整功能用户
gst Guest user cannot upgrade other users                                  # 访客用户无法升级其他用户

# 公共房间相关测试（续）
pub GET /publicRooms lists rooms                                           # GET /publicRooms列出房间
pub GET /publicRooms includes avatar URLs                                  # GET /publicRooms包含头像URL

# 访客相关测试（续）
gst Guest users can accept invites to private rooms over federation        # 访客用户可以通过联邦接受私有房间邀请
gst Guest users denied access over federation if guest access prohibited   # 如果禁止访客访问，访客用户通过联邦被拒绝访问

# 成员相关测试（续）
mem Room members can override their displayname on a room-specific basis   # 房间成员可以在特定房间基础上覆盖其显示名称
mem Room members can join a room with an overridden displayname            # 房间成员可以使用覆盖的显示名称加入房间
mem Users cannot kick users from a room they are not in                    # 用户无法从他们不在的房间中踢出用户
mem Users cannot kick users who have already left a room                   # 用户无法踢出已经离开房间的用户

# 打字通知相关测试（续）
typ Typing notification sent to local room members                         # 打字通知发送给本地房间成员

# 联邦打字通知测试 (Federation Typing Tests)
f,typ Typing notifications also sent to remote room members                # 打字通知也发送给远程房间成员

# 打字通知相关测试（续）
typ Typing can be explicitly stopped                                       # 打字可以明确停止

# 已读回执相关测试（续）
rct Read receipts are visible to /initialSync                              # 已读回执对/initialSync可见
rct Read receipts are sent as events                                       # 已读回执作为事件发送
rct Receipts must be m.read                                                # 回执必须是m.read

# 用户资料相关测试（续）
pro displayname updates affect room member events                          # 显示名称更新影响房间成员事件
pro avatar_url updates affect room member events                           # 头像URL更新影响房间成员事件

# 访客相关测试（续）
gst m.room.history_visibility == "world_readable" allows/forbids appropriately for Guest users # m.room.history_visibility == "world_readable"对访客用户适当允许/禁止
gst m.room.history_visibility == "shared" allows/forbids appropriately for Guest users # m.room.history_visibility == "shared"对访客用户适当允许/禁止
gst m.room.history_visibility == "invited" allows/forbids appropriately for Guest users # m.room.history_visibility == "invited"对访客用户适当允许/禁止
gst m.room.history_visibility == "joined" allows/forbids appropriately for Guest users # m.room.history_visibility == "joined"对访客用户适当允许/禁止
gst m.room.history_visibility == "default" allows/forbids appropriately for Guest users # m.room.history_visibility == "default"对访客用户适当允许/禁止
gst Guest non-joined user cannot call /events on shared room               # 访客非加入用户无法在共享房间调用/events
gst Guest non-joined user cannot call /events on invited room              # 访客非加入用户无法在邀请房间调用/events
gst Guest non-joined user cannot call /events on joined room               # 访客非加入用户无法在加入房间调用/events
gst Guest non-joined user cannot call /events on default room              # 访客非加入用户无法在默认房间调用/events
gst Guest non-joined user can call /events on world_readable room          # 访客非加入用户可以在world_readable房间调用/events
gst Guest non-joined users can get state for world_readable rooms          # 访客非加入用户可以获取world_readable房间的状态
gst Guest non-joined users can get individual state for world_readable rooms # 访客非加入用户可以获取world_readable房间的单个状态
gst Guest non-joined users cannot room initalSync for non-world_readable rooms # 访客非加入用户无法对非world_readable房间进行房间初始同步
gst Guest non-joined users can room initialSync for world_readable rooms   # 访客非加入用户可以对world_readable房间进行房间初始同步
gst Guest non-joined users can get individual state for world_readable rooms after leaving # 访客非加入用户在离开后可以获取world_readable房间的单个状态
gst Guest non-joined users cannot send messages to guest_access rooms if not joined # 访客非加入用户如果未加入无法向guest_access房间发送消息
gst Guest users can sync from world_readable guest_access rooms if joined  # 访客用户如果已加入可以从world_readable guest_access房间同步
gst Guest users can sync from shared guest_access rooms if joined          # 访客用户如果已加入可以从shared guest_access房间同步
gst Guest users can sync from invited guest_access rooms if joined         # 访客用户如果已加入可以从invited guest_access房间同步
gst Guest users can sync from joined guest_access rooms if joined          # 访客用户如果已加入可以从joined guest_access房间同步
gst Guest users can sync from default guest_access rooms if joined         # 访客用户如果已加入可以从default guest_access房间同步

# 认证相关测试 (Authentication Tests)
ath m.room.history_visibility == "world_readable" allows/forbids appropriately for Real users # m.room.history_visibility == "world_readable"对真实用户适当允许/禁止
ath m.room.history_visibility == "shared" allows/forbids appropriately for Real users # m.room.history_visibility == "shared"对真实用户适当允许/禁止
ath m.room.history_visibility == "invited" allows/forbids appropriately for Real users # m.room.history_visibility == "invited"对真实用户适当允许/禁止
ath m.room.history_visibility == "joined" allows/forbids appropriately for Real users # m.room.history_visibility == "joined"对真实用户适当允许/禁止
ath m.room.history_visibility == "default" allows/forbids appropriately for Real users # m.room.history_visibility == "default"对真实用户适当允许/禁止
ath Real non-joined user cannot call /events on shared room                # 真实非加入用户无法在共享房间调用/events
ath Real non-joined user cannot call /events on invited room               # 真实非加入用户无法在邀请房间调用/events
ath Real non-joined user cannot call /events on joined room                # 真实非加入用户无法在加入房间调用/events
ath Real non-joined user cannot call /events on default room               # 真实非加入用户无法在默认房间调用/events
ath Real non-joined user can call /events on world_readable room           # 真实非加入用户可以在world_readable房间调用/events
ath Real non-joined users can get state for world_readable rooms           # 真实非加入用户可以获取world_readable房间的状态
ath Real non-joined users can get individual state for world_readable rooms # 真实非加入用户可以获取world_readable房间的单个状态
ath Real non-joined users cannot room initalSync for non-world_readable rooms # 真实非加入用户无法对非world_readable房间进行房间初始同步
ath Real non-joined users can room initialSync for world_readable rooms    # 真实非加入用户可以对world_readable房间进行房间初始同步
ath Real non-joined users can get individual state for world_readable rooms after leaving # 真实非加入用户在离开后可以获取world_readable房间的单个状态
ath Real non-joined users cannot send messages to guest_access rooms if not joined # 真实非加入用户如果未加入无法向guest_access房间发送消息
ath Real users can sync from world_readable guest_access rooms if joined   # 真实用户如果已加入可以从world_readable guest_access房间同步
ath Real users can sync from shared guest_access rooms if joined           # 真实用户如果已加入可以从shared guest_access房间同步
ath Real users can sync from invited guest_access rooms if joined          # 真实用户如果已加入可以从invited guest_access房间同步
ath Real users can sync from joined guest_access rooms if joined           # 真实用户如果已加入可以从joined guest_access房间同步
ath Real users can sync from default guest_access rooms if joined          # 真实用户如果已加入可以从default guest_access房间同步
ath Only see history_visibility changes on boundaries                      # 只在边界看到history_visibility更改

# 联邦认证测试 (Federation Authentication Tests)
f,ath Backfill works correctly with history visibility set to joined       # 在history visibility设置为joined时回填正确工作

# 遗忘相关测试 (Forget Tests)
fgt Forgotten room messages cannot be paginated                            # 遗忘房间消息无法分页
fgt Forgetting room does not show up in v2 /sync                           # 遗忘房间不在v2 /sync中显示
fgt Can forget room you've been kicked from                                # 可以遗忘被踢出的房间
fgt Can't forget room you're still in                                      # 无法遗忘仍在其中的房间
fgt Can re-join room if re-invited                                         # 如果重新邀请可以重新加入房间

# 认证相关测试（续）
ath Only original members of the room can see messages from erased users   # 只有房间的原始成员可以看到被删除用户的消息

# 成员相关测试（续）
mem /joined_rooms returns only joined rooms                                # /joined_rooms只返回已加入的房间
mem /joined_members return joined members                                  # /joined_members返回已加入的成员

# 上下文相关测试 (Context Tests)
ctx /context/ on joined room works                                         # 在已加入房间上的/context/工作
ctx /context/ on non world readable room does not work                     # 在非world readable房间上的/context/不工作
ctx /context/ returns correct number of events                             # /context/返回正确数量的事件
ctx /context/ with lazy_load_members filter works                          # 带lazy_load_members过滤器的/context/工作

# 获取事件相关测试 (Get Event Tests)
get /event/ on joined room works                                           # 在已加入房间上的/event/工作
get /event/ on non world readable room does not work                       # 在非world readable房间上的/event/不工作
get /event/ does not allow access to events before the user joined         # /event/不允许访问用户加入前的事件

# 成员相关测试（续）
mem Can get rooms/{roomId}/members                                         # 可以获取rooms/{roomId}/members
mem Can get rooms/{roomId}/members at a given point                        # 可以在给定时间点获取rooms/{roomId}/members
mem Can filter rooms/{roomId}/members                                      # 可以过滤rooms/{roomId}/members

# 升级相关测试 (Upgrade Tests)
upg /upgrade creates a new room                                            # /upgrade创建新房间
upg /upgrade should preserve room visibility for public rooms              # /upgrade应该保留公共房间的房间可见性
upg /upgrade should preserve room visibility for private rooms             # /upgrade应该保留私有房间的房间可见性
upg /upgrade copies >100 power levels to the new room                      # /upgrade复制>100个权限级别到新房间
upg /upgrade copies the power levels to the new room                       # /upgrade复制权限级别到新房间
upg /upgrade preserves the power level of the upgrading user in old and new rooms # /upgrade在旧房间和新房间中保留升级用户的权限级别
upg /upgrade copies important state to the new room                        # /upgrade复制重要状态到新房间
upg /upgrade copies ban events to the new room                             # /upgrade复制封禁事件到新房间
upg local user has push rules copied to upgraded room                      # 本地用户的推送规则被复制到升级房间

# 联邦升级测试 (Federation Upgrade Tests)
f,upg remote user has push rules copied to upgraded room                   # 远程用户的推送规则被复制到升级房间

# 升级相关测试（续）
upg /upgrade moves aliases to the new room                                 # /upgrade移动别名到新房间
upg /upgrade moves remote aliases to the new room                          # /upgrade移动远程别名到新房间
upg /upgrade preserves direct room state                                   # /upgrade保留直接房间状态
upg /upgrade preserves room federation ability                             # /upgrade保留房间联邦能力
upg /upgrade restricts power levels in the old room                        # /upgrade限制旧房间中的权限级别
upg /upgrade restricts power levels in the old room when the old PLs are unusual # 当旧权限级别不寻常时，/upgrade限制旧房间中的权限级别
upg /upgrade to an unknown version is rejected                             # 升级到未知版本被拒绝
upg /upgrade is rejected if the user can't send state events               # 如果用户无法发送状态事件，升级被拒绝
upg /upgrade of a bogus room fails gracefully                              # 虚假房间的升级优雅失败
upg Cannot send tombstone event that points to the same room               # 无法发送指向同一房间的tombstone事件

# 联邦升级测试（续）
f,upg Local and remote users' homeservers remove a room from their public directory on upgrade # 本地和远程用户的主服务器在升级时从其公共目录中删除房间

# 房间状态相关测试（续）
rst Name/topic keys are correct                                            # 名称/主题键正确

# 联邦公共房间测试 (Federation Public Rooms Tests)
f,pub Can get remote public room list                                      # 可以获取远程公共房间列表

# 公共房间相关测试（续）
pub Can paginate public room list                                          # 可以分页公共房间列表
pub Can search public room list                                            # 可以搜索公共房间列表

# 同步相关测试（续）
syn Can create filter                                                      # 可以创建过滤器
syn Can download filter                                                    # 可以下载过滤器
syn Can sync                                                               # 可以同步
syn Can sync a joined room                                                 # 可以同步已加入的房间
syn Full state sync includes joined rooms                                  # 完整状态同步包括已加入的房间
syn Newly joined room is included in an incremental sync                   # 新加入的房间包含在增量同步中
syn Newly joined room has correct timeline in incremental sync             # 新加入的房间在增量同步中有正确的时间线

# 在线状态相关测试（续）
pre Newly joined room includes presence in incremental sync                # 新加入的房间在增量同步中包括在线状态
pre Get presence for newly joined members in incremental sync              # 在增量同步中获取新加入成员的在线状态

# 同步相关测试（续）
syn Can sync a room with a single message                                  # 可以同步有单个消息的房间
syn Can sync a room with a message with a transaction id                   # 可以同步有带事务ID消息的房间
syn A message sent after an initial sync appears in the timeline of an incremental sync. # 初始同步后发送的消息出现在增量同步的时间线中
syn A filtered timeline reaches its limit                                  # 过滤的时间线达到其限制
syn Syncing a new room with a large timeline limit isn't limited           # 同步有大时间线限制的新房间不受限制
syn A full_state incremental update returns only recent timeline           # full_state增量更新只返回最近的时间线
syn A prev_batch token can be used in the v1 messages API                  # prev_batch令牌可以在v1消息API中使用
syn A next_batch token can be used in the v1 messages API                  # next_batch令牌可以在v1消息API中使用
syn A prev_batch token from incremental sync can be used in the v1 messages API # 来自增量同步的prev_batch令牌可以在v1消息API中使用

# 在线状态相关测试（续）
pre User sees their own presence in a sync                                 # 用户在同步中看到自己的在线状态
pre User is offline if they set_presence=offline in their sync             # 如果在同步中设置set_presence=offline，用户离线
pre User sees updates to presence from other users in the incremental sync. # 用户在增量同步中看到其他用户的在线状态更新

# 同步相关测试（续）
syn State is included in the timeline in the initial sync                  # 状态包含在初始同步的时间线中

# 联邦同步测试（续）
f,syn State from remote users is included in the state in the initial sync # 来自远程用户的状态包含在初始同步的状态中

# 同步相关测试（续）
syn Changes to state are included in an incremental sync                   # 状态更改包含在增量同步中
syn Changes to state are included in an gapped incremental sync            # 状态更改包含在有间隙的增量同步中

# 联邦同步测试（续）
f,syn State from remote users is included in the timeline in an incremental sync # 来自远程用户的状态包含在增量同步的时间线中

# 同步相关测试（续）
syn A full_state incremental update returns all state                      # full_state增量更新返回所有状态
syn When user joins a room the state is included in the next sync          # 当用户加入房间时，状态包含在下一次同步中
syn A change to displayname should not result in a full state sync         # 显示名称的更改不应导致完整状态同步
syn A change to displayname should appear in incremental /sync             # 显示名称的更改应出现在增量/sync中
syn When user joins a room the state is included in a gapped sync          # 当用户加入房间时，状态包含在有间隙的同步中
syn When user joins and leaves a room in the same batch, the full state is still included in the next sync # 当用户在同一批次中加入和离开房间时，完整状态仍包含在下一次同步中
syn Current state appears in timeline in private history                   # 当前状态出现在私有历史的时间线中
syn Current state appears in timeline in private history with many messages before # 当前状态出现在有许多消息之前的私有历史时间线中
syn Current state appears in timeline in private history with many messages after # 当前状态出现在有许多消息之后的私有历史时间线中
syn Rooms a user is invited to appear in an initial sync                   # 用户被邀请的房间出现在初始同步中
syn Rooms a user is invited to appear in an incremental sync               # 用户被邀请的房间出现在增量同步中
syn Newly joined room is included in an incremental sync after invite      # 新加入的房间在邀请后包含在增量同步中
syn Sync can be polled for updates                                         # 同步可以轮询更新
syn Sync is woken up for leaves                                            # 同步因离开而唤醒
syn Left rooms appear in the leave section of sync                         # 离开的房间出现在同步的离开部分
syn Newly left rooms appear in the leave section of incremental sync       # 新离开的房间出现在增量同步的离开部分
syn We should see our own leave event, even if history_visibility is restricted (SYN-662) # 我们应该看到自己的离开事件，即使history_visibility受限（SYN-662）
syn We should see our own leave event when rejecting an invite, even if history_visibility is restricted (riot-web/3462) # 拒绝邀请时我们应该看到自己的离开事件，即使history_visibility受限（riot-web/3462）
syn Newly left rooms appear in the leave section of gapped sync            # 新离开的房间出现在有间隙同步的离开部分
syn Previously left rooms don't appear in the leave section of sync        # 之前离开的房间不出现在同步的离开部分
syn Left rooms appear in the leave section of full state sync              # 离开的房间出现在完整状态同步的离开部分
syn Archived rooms only contain history from before the user left          # 归档房间只包含用户离开前的历史
syn Banned rooms appear in the leave section of sync                       # 被封禁的房间出现在同步的离开部分
syn Newly banned rooms appear in the leave section of incremental sync     # 新被封禁的房间出现在增量同步的离开部分
syn Newly banned rooms appear in the leave section of incremental sync     # 新被封禁的房间出现在增量同步的离开部分
syn Typing events appear in initial sync                                   # 打字事件出现在初始同步中
syn Typing events appear in incremental sync                               # 打字事件出现在增量同步中
syn Typing events appear in gapped sync                                    # 打字事件出现在有间隙的同步中
syn Read receipts appear in initial v2 /sync                               # 已读回执出现在初始v2 /sync中
syn New read receipts appear in incremental v2 /sync                       # 新已读回执出现在增量v2 /sync中
syn Can pass a JSON filter as a query parameter                            # 可以将JSON过滤器作为查询参数传递
syn Can request federation format via the filter                           # 可以通过过滤器请求联邦格式
syn Read markers appear in incremental v2 /sync                            # 已读标记出现在增量v2 /sync中
syn Read markers appear in initial v2 /sync                                # 已读标记出现在初始v2 /sync中
syn Read markers can be updated                                            # 已读标记可以更新
syn Lazy loading parameters in the filter are strictly boolean             # 过滤器中的懒加载参数严格为布尔值
syn The only membership state included in an initial sync is for all the senders in the timeline # 初始同步中包含的唯一成员状态是时间线中所有发送者的
syn The only membership state included in an incremental sync is for senders in the timeline # 增量同步中包含的唯一成员状态是时间线中发送者的
syn The only membership state included in a gapped incremental sync is for senders in the timeline # 有间隙增量同步中包含的唯一成员状态是时间线中发送者的
syn Gapped incremental syncs include all state changes                     # 有间隙的增量同步包括所有状态更改
syn Old leaves are present in gapped incremental syncs                     # 旧离开在有间隙的增量同步中存在
syn Leaves are present in non-gapped incremental syncs                     # 离开在非间隙增量同步中存在
syn Old members are included in gappy incr LL sync if they start speaking  # 如果旧成员开始说话，他们包含在有间隙的增量LL同步中
syn Members from the gap are included in gappy incr LL sync                # 来自间隙的成员包含在有间隙的增量LL同步中
syn We don't send redundant membership state across incremental syncs by default # 默认情况下我们不在增量同步中发送冗余成员状态
syn We do send redundant membership state across incremental syncs if asked # 如果要求，我们确实在增量同步中发送冗余成员状态
syn Unnamed room comes with a name summary                                 # 未命名房间带有名称摘要
syn Named room comes with just joined member count summary                 # 命名房间只带有已加入成员计数摘要
syn Room summary only has 5 heroes                                         # 房间摘要只有5个英雄
syn Room summary counts change when membership changes                     # 成员变化时房间摘要计数改变

# 房间版本相关测试 (Room Version Tests)
rmv User can create and send/receive messages in a room with version 1     # 用户可以在版本1房间中创建和发送/接收消息
rmv User can create and send/receive messages in a room with version 1 (2 subtests) # 用户可以在版本1房间中创建和发送/接收消息（2个子测试）
rmv local user can join room with version 1                                # 本地用户可以加入版本1房间
rmv User can invite local user to room with version 1                      # 用户可以邀请本地用户到版本1房间
rmv remote user can join room with version 1                               # 远程用户可以加入版本1房间
rmv User can invite remote user to room with version 1                     # 用户可以邀请远程用户到版本1房间
rmv Remote user can backfill in a room with version 1                      # 远程用户可以在版本1房间中回填
rmv Can reject invites over federation for rooms with version 1            # 可以通过联邦拒绝版本1房间的邀请
rmv Can receive redactions from regular users over federation in room version 1 # 可以在版本1房间中通过联邦接收来自普通用户的编辑

# 版本2-11房间的类似测试...
rmv User can create and send/receive messages in a room with version 2     # 用户可以在版本2房间中创建和发送/接收消息
rmv User can create and send/receive messages in a room with version 2 (2 subtests) # 用户可以在版本2房间中创建和发送/接收消息（2个子测试）
rmv local user can join room with version 2                                # 本地用户可以加入版本2房间
rmv User can invite local user to room with version 2                      # 用户可以邀请本地用户到版本2房间
rmv remote user can join room with version 2                               # 远程用户可以加入版本2房间
rmv User can invite remote user to room with version 2                     # 用户可以邀请远程用户到版本2房间
rmv Remote user can backfill in a room with version 2                      # 远程用户可以在版本2房间中回填
rmv Can reject invites over federation for rooms with version 2            # 可以通过联邦拒绝版本2房间的邀请
rmv Can receive redactions from regular users over federation in room version 2 # 可以在版本2房间中通过联邦接收来自普通用户的编辑

# [版本3-11的类似测试模式继续...]

# 在线状态相关测试（续）
pre Presence changes are reported to local room members                    # 在线状态更改报告给本地房间成员

# 联邦在线状态测试 (Federation Presence Tests)
f,pre Presence changes are also reported to remote room members            # 在线状态更改也报告给远程房间成员

# 在线状态相关测试（续）
pre Presence changes to UNAVAILABLE are reported to local room members     # 在线状态更改为UNAVAILABLE报告给本地房间成员

# 联邦在线状态测试（续）
f,pre Presence changes to UNAVAILABLE are reported to remote room members  # 在线状态更改为UNAVAILABLE报告给远程房间成员

# v1同步相关测试（续）
v1s Newly created users see their own presence in /initialSync (SYT-34)    # 新创建的用户在/initialSync中看到自己的在线状态（SYT-34）

# 设备密钥相关测试 (Device Keys Tests)
dvk Can upload device keys                                                 # 可以上传设备密钥
dvk Should reject keys claiming to belong to a different user              # 应该拒绝声称属于不同用户的密钥
dvk Can query device keys using POST                                       # 可以使用POST查询设备密钥
dvk Can query specific device keys using POST                              # 可以使用POST查询特定设备密钥
dvk query for user with no keys returns empty key dict                     # 查询没有密钥的用户返回空密钥字典
dvk Can claim one time key using POST                                      # 可以使用POST声明一次性密钥

# 联邦设备密钥测试 (Federation Device Keys Tests)
f,dvk Can query remote device keys using POST                              # 可以使用POST查询远程设备密钥
f,dvk Can claim remote one time key using POST                             # 可以使用POST声明远程一次性密钥

# 设备密钥相关测试（续）
dvk Local device key changes appear in v2 /sync                            # 本地设备密钥更改出现在v2 /sync中
dvk Local new device changes appear in v2 /sync                            # 本地新设备更改出现在v2 /sync中
dvk Local delete device changes appear in v2 /sync                         # 本地删除设备更改出现在v2 /sync中
dvk Local update device changes appear in v2 /sync                         # 本地更新设备更改出现在v2 /sync中
dvk Can query remote device keys using POST after notification             # 通知后可以使用POST查询远程设备密钥

# 联邦设备测试 (Federation Device Tests)
f,dev Device deletion propagates over federation                           # 设备删除通过联邦传播
f,dev If remote user leaves room, changes device and rejoins we see update in sync # 如果远程用户离开房间，更改设备并重新加入，我们在同步中看到更新
f,dev If remote user leaves room we no longer receive device updates       # 如果远程用户离开房间，我们不再接收设备更新

# 设备密钥相关测试（续）
dvk Local device key changes appear in /keys/changes                       # 本地设备密钥更改出现在/keys/changes中
dvk New users appear in /keys/changes                                      # 新用户出现在/keys/changes中

# 联邦设备密钥测试（续）
f,dvk If remote user leaves room, changes device and rejoins we see update in /keys/changes # 如果远程用户离开房间，更改设备并重新加入，我们在/keys/changes中看到更新

# 设备密钥相关测试（续）
dvk Get left notifs in sync and /keys/changes when other user leaves       # 当其他用户离开时在sync和/keys/changes中获取离开通知
dvk Get left notifs for other users in sync and /keys/changes when user leaves # 当用户离开时在sync和/keys/changes中获取其他用户的离开通知

# 联邦设备密钥测试（续）
f,dvk If user leaves room, remote user changes device and rejoins we see update in /sync and /keys/changes # 如果用户离开房间，远程用户更改设备并重新加入，我们在/sync和/keys/changes中看到更新

# 设备密钥备份相关测试 (Device Key Backup Tests)
dkb Can create backup version                                              # 可以创建备份版本
dkb Can update backup version                                              # 可以更新备份版本
dkb Responds correctly when backup is empty                                # 当备份为空时正确响应
dkb Can backup keys                                                        # 可以备份密钥
dkb Can update keys with better versions                                   # 可以使用更好的版本更新密钥
dkb Will not update keys with worse versions                               # 不会使用更差的版本更新密钥
dkb Will not back up to an old backup version                              # 不会备份到旧的备份版本
dkb Can delete backup                                                      # 可以删除备份
dkb Deleted & recreated backups are empty                                  # 删除和重新创建的备份为空
dkb Can create more than 10 backup versions                                # 可以创建超过10个备份版本

# 交叉签名密钥相关测试 (Cross-Signing Keys Tests)
xsk Can upload self-signing keys                                           # 可以上传自签名密钥
xsk Fails to upload self-signing keys with no auth                         # 没有认证时上传自签名密钥失败
xsk Fails to upload self-signing key without master key                    # 没有主密钥时上传自签名密钥失败
xsk Changing master key notifies local users                               # 更改主密钥通知本地用户
xsk Changing user-signing key notifies local users                         # 更改用户签名密钥通知本地用户

# 联邦交叉签名测试 (Federation Cross-Signing Tests)
f,xsk can fetch self-signing keys over federation                          # 可以通过联邦获取自签名密钥
f,xsk uploading self-signing key notifies over federation                  # 上传自签名密钥通过联邦通知
f,xsk uploading signed devices gets propagated over federation             # 上传签名设备通过联邦传播

# 标签相关测试 (Tag Tests)
tag Can add tag                                                            # 可以添加标签
tag Can remove tag                                                         # 可以删除标签
tag Can list tags for a room                                               # 可以列出房间的标签

# v1同步相关测试（续）
v1s Tags appear in the v1 /events stream                                   # 标签出现在v1 /events流中
v1s Tags appear in the v1 /initalSync                                      # 标签出现在v1 /initalSync中
v1s Tags appear in the v1 room initial sync                                # 标签出现在v1房间初始同步中

# 标签相关测试（续）
tag Tags appear in an initial v2 /sync                                     # 标签出现在初始v2 /sync中
tag Newly updated tags appear in an incremental v2 /sync                   # 新更新的标签出现在增量v2 /sync中
tag Deleted tags appear in an incremental v2 /sync                         # 删除的标签出现在增量v2 /sync中
tag local user has tags copied to the new room                             # 本地用户的标签被复制到新房间

# 联邦标签测试 (Federation Tag Tests)
f,tag remote user has tags copied to the new room                          # 远程用户的标签被复制到新房间

# 搜索相关测试 (Search Tests)
sch Can search for an event by body                                        # 可以按正文搜索事件
sch Can get context around search results                                  # 可以获取搜索结果周围的上下文
sch Can back-paginate search results                                       # 可以向后分页搜索结果
sch Search works across an upgraded room and its predecessor               # 搜索在升级房间及其前身中工作
sch Search results with rank ordering do not include redacted events       # 按排名排序的搜索结果不包括编辑的事件
sch Search results with recent ordering do not include redacted events     # 按最近排序的搜索结果不包括编辑的事件

# 账户数据相关测试 (Account Data Tests)
acc Can add account data                                                   # 可以添加账户数据
acc Can add account data to room                                           # 可以向房间添加账户数据
acc Can get account data without syncing                                   # 可以在不同步的情况下获取账户数据
acc Can get room account data without syncing                              # 可以在不同步的情况下获取房间账户数据

# v1同步相关测试（续）
v1s Latest account data comes down in /initialSync                         # 最新账户数据在/initialSync中下来
v1s Latest account data comes down in room initialSync                     # 最新账户数据在房间初始同步中下来
v1s Account data appears in v1 /events stream                              # 账户数据出现在v1 /events流中
v1s Room account data appears in v1 /events stream                         # 房间账户数据出现在v1 /events流中

# 账户数据相关测试（续）
acc Latest account data appears in v2 /sync                                # 最新账户数据出现在v2 /sync中
acc New account data appears in incremental v2 /sync                       # 新账户数据出现在增量v2 /sync中

# OpenID相关测试 (OpenID Tests)
oid Can generate a openid access_token that can be exchanged for information about a user # 可以生成可以交换用户信息的openid访问令牌
oid Invalid openid access tokens are rejected                              # 无效的openid访问令牌被拒绝
oid Requests to userinfo without access tokens are rejected                # 没有访问令牌的用户信息请求被拒绝

# 发送到设备相关测试 (Send To Device Tests)
std Can send a message directly to a device using PUT /sendToDevice        # 可以使用PUT /sendToDevice直接向设备发送消息
std Can recv a device message using /sync                                  # 可以使用/sync接收设备消息
std Can recv device messages until they are acknowledged                   # 可以接收设备消息直到它们被确认
std Device messages with the same txn_id are deduplicated                  # 具有相同txn_id的设备消息被去重
std Device messages wake up /sync                                          # 设备消息唤醒/sync
std Can recv device messages over federation                               # 可以通过联邦接收设备消息

# 联邦发送到设备测试 (Federation Send To Device Tests)
fsd Device messages over federation wake up /sync                          # 通过联邦的设备消息唤醒/sync

# 发送到设备相关测试（续）
std Can send messages with a wildcard device id                            # 可以使用通配符设备ID发送消息
std Can send messages with a wildcard device id to two devices             # 可以使用通配符设备ID向两个设备发送消息
std Wildcard device messages wake up /sync                                 # 通配符设备消息唤醒/sync

# 联邦发送到设备测试（续）
fsd Wildcard device messages over federation wake up /sync                 # 通过联邦的通配符设备消息唤醒/sync

# 管理员相关测试 (Admin Tests)
adm /whois                                                                 # /whois

# 命名空间相关测试 (Namespace Tests)
nsp /purge_history                                                         # /purge_history
nsp /purge_history by ts                                                   # 按时间戳/purge_history
nsp Can backfill purged history                                            # 可以回填清除的历史
nsp Shutdown room                                                          # 关闭房间

# 忽略相关测试 (Ignore Tests)
ign Ignore user in existing room                                           # 在现有房间中忽略用户
ign Ignore invite in full sync                                             # 在完整同步中忽略邀请
ign Ignore invite in incremental sync                                      # 在增量同步中忽略邀请

# 联邦密钥相关测试 (Federation Key Tests)
fky Checking local federation server                                       # 检查本地联邦服务器
fky Federation key API allows unsigned requests for keys                   # 联邦密钥API允许对密钥的未签名请求
fky Federation key API can act as a notary server via a GET request        # 联邦密钥API可以通过GET请求充当公证服务器
fky Federation key API can act as a notary server via a POST request       # 联邦密钥API可以通过POST请求充当公证服务器
fky Key notary server should return an expired key if it can't find any others # 如果找不到其他密钥，密钥公证服务器应返回过期密钥
fky Key notary server must not overwrite a valid key with a spurious result from the origin server # 密钥公证服务器不得用来自源服务器的虚假结果覆盖有效密钥

# 联邦查询相关测试 (Federation Query Tests)
fqu Non-numeric ports in server names are rejected                         # 服务器名称中的非数字端口被拒绝
fqu Outbound federation can query profile data                             # 出站联邦可以查询资料数据
fqu Inbound federation can query profile data                              # 入站联邦可以查询资料数据
fqu Outbound federation can query room alias directory                     # 出站联邦可以查询房间别名目录
fqu Inbound federation can query room alias directory                      # 入站联邦可以查询房间别名目录

# 联邦发送加入相关测试 (Federation Send Join Tests)
fsj Membership event with an invalid displayname in the send_join response should not cause room join to fail # send_join响应中具有无效显示名称的成员事件不应导致房间加入失败
fsj Outbound federation can query v1 /send_join                            # 出站联邦可以查询v1 /send_join
fsj Outbound federation can query v2 /send_join                            # 出站联邦可以查询v2 /send_join

# 联邦制作加入相关测试 (Federation Make Join Tests)
fmj Outbound federation passes make_join failures through to the client    # 出站联邦将make_join失败传递给客户端

# 联邦发送加入相关测试（续）
fsj Inbound federation can receive v1 /send_join                           # 入站联邦可以接收v1 /send_join
fsj Inbound federation can receive v2 /send_join                           # 入站联邦可以接收v2 /send_join

# 联邦制作加入相关测试（续）
fmj Inbound /v1/make_join rejects remote attempts to join local users to rooms # 入站/v1/make_join拒绝远程尝试将本地用户加入房间

# 联邦发送加入相关测试（续）
fsj Inbound /v1/send_join rejects incorrectly-signed joins                 # 入站/v1/send_join拒绝签名不正确的加入
fsj Inbound /v1/send_join rejects joins from other servers                 # 入站/v1/send_join拒绝来自其他服务器的加入

# 联邦授权相关测试 (Federation Authorization Tests)
fau Inbound federation rejects remote attempts to kick local users to rooms # 入站联邦拒绝远程尝试将本地用户踢出房间

# 联邦房间版本相关测试 (Federation Room Version Tests)
frv Inbound federation rejects attempts to join v1 rooms from servers without v1 support # 入站联邦拒绝来自不支持v1的服务器加入v1房间的尝试
frv Inbound federation rejects attempts to join v2 rooms from servers lacking version support # 入站联邦拒绝来自缺乏版本支持的服务器加入v2房间的尝试
frv Inbound federation rejects attempts to join v2 rooms from servers only supporting v1 # 入站联邦拒绝来自仅支持v1的服务器加入v2房间的尝试
frv Inbound federation accepts attempts to join v2 rooms from servers with support # 入站联邦接受来自支持的服务器加入v2房间的尝试
frv Outbound federation correctly handles unsupported room versions        # 出站联邦正确处理不支持的房间版本
frv A pair of servers can establish a join in a v2 room                    # 一对服务器可以在v2房间中建立加入

# 联邦发送加入相关测试（续）
fsj Outbound federation rejects send_join responses with no m.room.create event # 出站联邦拒绝没有m.room.create事件的send_join响应

# 联邦房间版本相关测试（续）
frv Outbound federation rejects m.room.create events with an unknown room version # 出站联邦拒绝具有未知房间版本的m.room.create事件

# 联邦发送加入相关测试（续）
fsj Event with an invalid signature in the send_join response should not cause room join to fail # send_join响应中具有无效签名的事件不应导致房间加入失败
fsj Inbound: send_join rejects invalid JSON for room version 6             # 入站：send_join拒绝房间版本6的无效JSON

# 联邦相关测试 (Federation Tests)
fed Outbound federation can send events                                    # 出站联邦可以发送事件
fed Inbound federation can receive events                                  # 入站联邦可以接收事件
fed Inbound federation can receive redacted events                         # 入站联邦可以接收编辑的事件

# MSC相关测试（续）
msc Ephemeral messages received from servers are correctly expired         # 从服务器接收的临时消息正确过期

# 联邦相关测试（续）
fed Events whose auth_events are in the wrong room do not mess up the room state # auth_events在错误房间中的事件不会搞乱房间状态
fed Inbound federation can return events                                   # 入站联邦可以返回事件
fed Inbound federation redacts events from erased users                    # 入站联邦编辑来自被删除用户的事件

# 联邦缺失事件相关测试 (Federation Missing Events Tests)
fme Outbound federation can request missing events                         # 出站联邦可以请求缺失事件
fme Inbound federation can return missing events for world_readable visibility # 入站联邦可以为world_readable可见性返回缺失事件
fme Inbound federation can return missing events for shared visibility     # 入站联邦可以为shared可见性返回缺失事件
fme Inbound federation can return missing events for invited visibility    # 入站联邦可以为invited可见性返回缺失事件
fme Inbound federation can return missing events for joined visibility     # 入站联邦可以为joined可见性返回缺失事件
fme outliers whose auth_events are in a different room are correctly rejected # auth_events在不同房间中的异常值被正确拒绝

# 联邦回填相关测试 (Federation Backfill Tests)
fbk Outbound federation can backfill events                                # 出站联邦可以回填事件
fbk Inbound federation can backfill events                                 # 入站联邦可以回填事件
fbk Backfill checks the events requested belong to the room                # 回填检查请求的事件属于房间
fbk Backfilled events whose prev_events are in a different room do not allow cross-room back-pagination # prev_events在不同房间中的回填事件不允许跨房间向后分页

# 联邦邀请相关测试（续）
fiv Outbound federation can send invites via v1 API                        # 出站联邦可以通过v1 API发送邀请
fiv Outbound federation can send invites via v2 API                        # 出站联邦可以通过v2 API发送邀请
fiv Inbound federation can receive invites via v1 API                      # 入站联邦可以通过v1 API接收邀请
fiv Inbound federation can receive invites via v2 API                      # 入站联邦可以通过v2 API接收邀请
fiv Inbound federation can receive invite and reject when remote replies with a 403 # 入站联邦可以接收邀请并在远程回复403时拒绝
fiv Inbound federation can receive invite and reject when remote replies with a 500 # 入站联邦可以接收邀请并在远程回复500时拒绝
fiv Inbound federation can receive invite and reject when remote is unreachable # 入站联邦可以接收邀请并在远程不可达时拒绝
fiv Inbound federation rejects invites which are not signed by the sender  # 入站联邦拒绝未由发送者签名的邀请
fiv Inbound federation can receive invite rejections                       # 入站联邦可以接收邀请拒绝
fiv Inbound federation rejects incorrectly-signed invite rejections        # 入站联邦拒绝签名不正确的邀请拒绝

# 联邦发送离开相关测试 (Federation Send Leave Tests)
fsl Inbound /v1/send_leave rejects leaves from other servers                # 入站/v1/send_leave拒绝来自其他服务器的离开

# 联邦状态相关测试 (Federation State Tests)
fst Inbound federation can get state for a room                            # 入站联邦可以获取房间状态
fst Inbound federation of state requires event_id as a mandatory paramater # 入站联邦状态需要event_id作为强制参数
fst Inbound federation can get state_ids for a room                        # 入站联邦可以获取房间的state_ids
fst Inbound federation of state_ids requires event_id as a mandatory paramater # 入站联邦state_ids需要event_id作为强制参数
fst Federation rejects inbound events where the prev_events cannot be found # 联邦拒绝无法找到prev_events的入站事件
fst Room state at a rejected message event is the same as its predecessor  # 被拒绝消息事件的房间状态与其前身相同
fst Room state at a rejected state event is the same as its predecessor    # 被拒绝状态事件的房间状态与其前身相同
fst Outbound federation requests missing prev_events and then asks for /state_ids and resolves the state # 出站联邦请求缺失的prev_events，然后请求/state_ids并解析状态
fst Federation handles empty auth_events in state_ids sanely               # 联邦合理处理state_ids中的空auth_events
fst Getting state checks the events requested belong to the room            # 获取状态检查请求的事件属于房间
fst Getting state IDs checks the events requested belong to the room        # 获取状态ID检查请求的事件属于房间
fst Should not be able to take over the room by pretending there is no PL event # 不应该能够通过假装没有PL事件来接管房间

# 联邦公共房间相关测试（续）
fpb Inbound federation can get public room list                            # 入站联邦可以获取公共房间列表

# 联邦相关测试（续）
fed Outbound federation sends receipts                                     # 出站联邦发送回执
fed Inbound federation rejects receipts from wrong remote                  # 入站联邦拒绝来自错误远程的回执
fed Inbound federation ignores redactions from invalid servers room > v3   # 入站联邦忽略来自无效服务器的编辑房间 > v3
fed An event which redacts an event in a different room should be ignored  # 编辑不同房间中事件的事件应被忽略
fed An event which redacts itself should be ignored                        # 编辑自身的事件应被忽略
fed A pair of events which redact each other should be ignored             # 相互编辑的一对事件应被忽略

# 联邦设备密钥相关测试（续）
fdk Local device key changes get to remote servers                         # 本地设备密钥更改到达远程服务器
fdk Server correctly handles incoming m.device_list_update                 # 服务器正确处理传入的m.device_list_update
fdk Server correctly resyncs when client query keys and there is no remote cache # 当客户端查询密钥且没有远程缓存时，服务器正确重新同步
fdk Server correctly resyncs when server leaves and rejoins a room         # 当服务器离开并重新加入房间时，服务器正确重新同步
fdk Local device key changes get to remote servers with correct prev_id    # 本地设备密钥更改以正确的prev_id到达远程服务器
fdk Device list doesn't change if remote server is down                    # 如果远程服务器宕机，设备列表不会更改
fdk If a device list update goes missing, the server resyncs on the next one # 如果设备列表更新丢失，服务器在下一个更新时重新同步

# 联邦状态相关测试（续）
fst Name/topic keys are correct                                            # 名称/主题键正确

# 联邦授权相关测试（续）
fau Remote servers cannot set power levels in rooms without existing powerlevels # 远程服务器无法在没有现有权限级别的房间中设置权限级别
fau Remote servers should reject attempts by non-creators to set the power levels # 远程服务器应拒绝非创建者设置权限级别的尝试
fau Inbound federation rejects typing notifications from wrong remote       # 入站联邦拒绝来自错误远程的打字通知
fau Users cannot set notifications powerlevel higher than their own        # 用户无法设置比自己更高的通知权限级别

# 联邦相关测试（续）
fed Forward extremities remain so even after the next events are populated as outliers # 前向极值即使在下一个事件作为异常值填充后仍保持如此

# 联邦授权相关测试（续）
fau Banned servers cannot send events                                      # 被封禁的服务器无法发送事件
fau Banned servers cannot /make_join                                       # 被封禁的服务器无法/make_join
fau Banned servers cannot /send_join                                       # 被封禁的服务器无法/send_join
fau Banned servers cannot /make_leave                                      # 被封禁的服务器无法/make_leave
fau Banned servers cannot /send_leave                                      # 被封禁的服务器无法/send_leave
fau Banned servers cannot /invite                                          # 被封禁的服务器无法/invite
fau Banned servers cannot get room state                                   # 被封禁的服务器无法获取房间状态
fau Banned servers cannot get room state ids                               # 被封禁的服务器无法获取房间状态ID
fau Banned servers cannot backfill                                         # 被封禁的服务器无法回填
fau Banned servers cannot /event_auth                                      # 被封禁的服务器无法/event_auth
fau Banned servers cannot get missing events                               # 被封禁的服务器无法获取缺失事件
fau Server correctly handles transactions that break edu limits            # 服务器正确处理破坏edu限制的事务
fau Inbound federation correctly soft fails events                         # 入站联邦正确软失败事件
fau Inbound federation accepts a second soft-failed event                  # 入站联邦接受第二个软失败事件
fau Inbound federation correctly handles soft failed events as extremities # 入站联邦正确处理软失败事件作为极值

# 媒体相关测试（续）
med Can upload with Unicode file name                                      # 可以使用Unicode文件名上传
med Can download with Unicode file name locally                            # 可以在本地使用Unicode文件名下载

