// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// Dendrite 密钥生成工具 - 用于生成 Dendrite 运行所需的各种密钥文件
// 包括 Matrix 签名密钥和 TLS 证书/私钥
package main

import (
	"flag"  // 命令行参数解析
	"fmt"   // 格式化输出
	"log"   // 日志记录
	"os"    // 操作系统接口

	"github.com/element-hq/dendrite/test"  // Dendrite 测试工具包
)

// 使用说明文本
const usage = `使用方法: %s

生成 Dendrite 运行所需的密钥文件。

参数:

`

// 命令行参数定义
var (
	// TLS 相关参数
	tlsCertFile       = flag.String("tls-cert", "", "要生成的用于 TLS 的 X509 证书文件路径")
	tlsKeyFile        = flag.String("tls-key", "", "要生成的用于 TLS 的 RSA 私钥文件路径")
	
	// Matrix 签名密钥
	privateKeyFile    = flag.String("private-key", "", "要生成的用于对象签名的 Ed25519 私钥文件路径")
	
	// CA 证书相关（用于集成测试）
	authorityCertFile = flag.String("tls-authority-cert", "", "可选：基于此 CA 证书创建 TLS 证书/密钥。用于集成测试。")
	authorityKeyFile  = flag.String("tls-authority-key", "", "可选：基于此 CA 私钥创建 TLS 证书/密钥。用于集成测试。")
	
	// 服务器配置
	serverName        = flag.String("server", "", "可选：使用此域名创建 TLS 证书/密钥。用于集成测试。")
	keySize           = flag.Int("keysize", 4096, "可选：创建指定密钥大小的 TLS RSA 私钥")
)

// main 函数 - 密钥生成工具的主入口点
func main() {
	// 设置自定义的使用说明函数
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, usage, os.Args[0])  // 打印使用说明
		flag.PrintDefaults()                       // 打印所有参数的默认值
	}

	// 解析命令行参数
	flag.Parse()

	// 检查是否至少指定了一个要生成的密钥类型
	if *tlsCertFile == "" && *tlsKeyFile == "" && *privateKeyFile == "" {
		flag.Usage()  // 如果没有指定任何密钥，显示使用说明
		return
	}

	// 处理 TLS 证书和密钥生成
	if *tlsCertFile != "" || *tlsKeyFile != "" {
		// 验证 TLS 参数：证书和密钥必须同时指定
		if *tlsCertFile == "" || *tlsKeyFile == "" {
			log.Fatal("--tls-key 和 --tls-cert 必须同时提供或都不提供")
		}
		
		// 检查是否使用 CA 证书
		if *authorityCertFile == "" && *authorityKeyFile == "" {
			// 生成自签名 TLS 证书和密钥
			if err := test.NewTLSKey(*tlsKeyFile, *tlsCertFile, *keySize); err != nil {
				panic(err)
			}
		} else {
			// 基于给定的 CA 证书生成 TLS 证书/密钥
			if err := test.NewTLSKeyWithAuthority(*serverName, *tlsKeyFile, *tlsCertFile, *authorityKeyFile, *authorityCertFile, *keySize); err != nil {
				panic(err)
			}
		}
		fmt.Printf("已创建 TLS 证书文件:    %s\n", *tlsCertFile)
		fmt.Printf("已创建 TLS 密钥文件:     %s\n", *tlsKeyFile)
	}

	// 处理 Matrix 签名私钥生成
	if *privateKeyFile != "" {
		// 生成 Ed25519 私钥用于 Matrix 事件签名
		if err := test.NewMatrixKey(*privateKeyFile); err != nil {
			panic(err)
		}
		fmt.Printf("已创建私钥文件: %s\n", *privateKeyFile)
	}
}
