// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// Dendrite 用户账户创建工具 - 用于在 Dendrite 服务器上创建新的用户账户
// 支持多种密码输入方式和管理员账户创建
package main

import (
	"bytes"         // 字节缓冲区操作
	"crypto/hmac"   // HMAC 加密
	"crypto/sha1"   // SHA1 哈希
	"encoding/hex"  // 十六进制编码
	"encoding/json" // JSON 编解码
	"flag"          // 命令行参数解析
	"fmt"           // 格式化输出
	"io"            // 输入输出接口
	"net/http"      // HTTP 客户端
	"os"            // 操作系统接口
	"strings"       // 字符串操作
	"time"          // 时间操作

	"github.com/element-hq/dendrite/internal" // Dendrite 内部工具
	"github.com/tidwall/gjson"                // JSON 解析库

	"github.com/sirupsen/logrus" // 日志库
	"golang.org/x/term"          // 终端操作（用于安全密码输入）

	"github.com/element-hq/dendrite/setup" // Dendrite 设置包
)

// 使用说明文本
const usage = `使用方法: %s

在家庭服务器上创建新的用户账户。

示例:

	# 通过参数提供密码（默认共享密钥方式）
  	%s --config dendrite.yaml -username alice -password foobarbaz
	# 使用用户中心登录类型
  	%s --config dendrite.yaml -username alice -password foobarbaz -login-type user-center
	# 使用机器人登录类型
  	%s --config dendrite.yaml -username alice -password foobarbaz -login-type bot
	# 从文件读取密码
  	%s --config dendrite.yaml -username alice -passwordfile my.pass
	# 交互式输入密码
	%s --config dendrite.yaml -username alice
	# 从标准输入读取密码
	%s --config dendrite.yaml -username alice -passwordstdin < my.pass
	cat my.pass | %s --config dendrite.yaml -username alice -passwordstdin

参数:

`

// 命令行参数定义
var (
	username      = flag.String("username", "", "要注册的账户用户名（仅指定本地部分，例如 '@alice:domain.com' 中的 'alice'）")
	password      = flag.String("password", "", "与账户关联的密码")
	pwdFile       = flag.String("passwordfile", "", "用于密码的文件路径（例如用于自动化账户创建）")
	pwdStdin      = flag.Bool("passwordstdin", false, "从标准输入读取密码")
	isAdmin       = flag.Bool("admin", false, "创建管理员账户")
	resetPassword = flag.Bool("reset-password", false, "已弃用的参数")
	serverURL     = flag.String("url", "http://localhost:8008", "要连接的服务器 URL")
	timeout       = flag.Duration("timeout", time.Second*30, "连接服务器时 HTTP 客户端的超时时间")
	// 新增参数：选择登录类型
	loginType = flag.String("login-type", "shared-secret", "登录类型: shared-secret, user-center, bot")
)

// HTTP 客户端配置 - 用于与 Dendrite 服务器通信
var cl = http.Client{
	Timeout:   time.Second * 30,      // 30秒超时
	Transport: http.DefaultTransport, // 使用默认传输
}

// main 函数 - 用户账户创建工具的主入口点
func main() {
	// 获取程序名称用于使用说明
	name := os.Args[0]

	// 设置自定义的使用说明函数
	flag.Usage = func() {
		_, _ = fmt.Fprintf(os.Stderr, usage, name, name, name, name, name, name, name, name)
		flag.PrintDefaults()
	}

	// 解析配置文件和命令行参数
	cfg := setup.ParseFlags(true)

	// 检查已弃用的参数
	if *resetPassword {
		logrus.Fatalf("reset-password 参数已被 POST /_dendrite/admin/resetPassword/{localpart} 管理 API 替代。")
	}

	// 验证共享密钥注册是否已启用
	if cfg.ClientAPI.RegistrationSharedSecret == "" {
		logrus.Fatalln("共享密钥注册未启用，请在配置中设置共享密钥: 'client_api.registration_shared_secret'")
	}

	// 验证用户名是否已提供
	if *username == "" {
		flag.Usage()
		os.Exit(1)
	}

	// 验证用户名格式是否有效
	if err := internal.ValidateUsername(*username, cfg.Global.ServerName); err != nil {
		logrus.WithError(err).Error("指定的用户名无效")
		os.Exit(1)
	}

	// 获取密码（支持多种输入方式）
	pass, err := getPassword(*password, *pwdFile, *pwdStdin, os.Stdin)
	if err != nil {
		logrus.Fatalln(err)
	}

	// 验证密码是否符合要求
	if err = internal.ValidatePassword(pass); err != nil {
		logrus.WithError(err).Error("指定的密码无效")
		os.Exit(1)
	}

	// 设置 HTTP 客户端超时
	cl.Timeout = *timeout

	// 根据登录类型选择不同的注册方法
	var accessToken string
	switch *loginType {
	case "shared-secret":
		// 使用原有的共享密钥注册方法
		accessToken, err = sharedSecretRegister(cfg.ClientAPI.RegistrationSharedSecret, *serverURL, *username, pass, *isAdmin)
	case "user-center":
		// 使用用户中心登录类型注册
		accessToken, err = userCenterRegister(cfg.ClientAPI.RegistrationSharedSecret, *serverURL, *username, pass, *isAdmin)
	case "bot":
		// 使用机器人登录类型注册
		accessToken, err = botRegister(cfg.ClientAPI.RegistrationSharedSecret, *serverURL, *username, pass, *isAdmin)
	default:
		logrus.Fatalf("不支持的登录类型: %s", *loginType)
	}

	if err != nil {
		logrus.Fatalln("创建账户失败:", err.Error())
	}

	// 输出成功信息
	logrus.Infof("账户创建成功: %s (访问令牌: %s)", *username, accessToken)
}

// 共享密钥注册请求结构体 - 用于与服务器通信的 JSON 数据结构
type sharedSecretRegistrationRequest struct {
	User     string `json:"username"` // 用户名
	Password string `json:"password"` // 密码
	Nonce    string `json:"nonce"`    // 随机数（防重放攻击）
	MacStr   string `json:"mac"`      // 消息认证码（验证请求合法性）
	Admin    bool   `json:"admin"`    // 是否为管理员账户
}

// sharedSecretRegister 使用共享密钥注册新用户
// 参数:
//   - sharedSecret: 服务器配置的共享密钥
//   - serverURL: Dendrite 服务器 URL
//   - localpart: 用户名的本地部分
//   - password: 用户密码
//   - admin: 是否创建管理员账户
//
// 返回: 访问令牌和可能的错误
func sharedSecretRegister(sharedSecret, serverURL, localpart, password string, admin bool) (accessToken string, err error) {
	// 构建注册 API 端点 URL（使用 Synapse 兼容的端点）
	registerURL := fmt.Sprintf("%s/_synapse/admin/v1/register", strings.Trim(serverURL, "/"))

	// 第一步：获取 nonce（随机数）
	nonceReq, err := http.NewRequest(http.MethodGet, registerURL, nil)
	if err != nil {
		return "", fmt.Errorf("无法创建 HTTP 请求: %w", err)
	}

	// 发送获取 nonce 的请求
	nonceResp, err := cl.Do(nonceReq)
	if err != nil {
		return "", fmt.Errorf("无法获取 nonce: %w", err)
	}

	// 读取响应体
	body, err := io.ReadAll(nonceResp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败: %w", err)
	}
	defer nonceResp.Body.Close() // 确保关闭响应体

	// 从 JSON 响应中提取 nonce
	nonce := gjson.GetBytes(body, "nonce").Str

	// 设置管理员标识字符串（用于 MAC 计算）
	adminStr := "notadmin"
	if admin {
		adminStr = "admin"
	}

	// 构建注册请求数据
	reg := sharedSecretRegistrationRequest{
		User:     localpart, // 用户名本地部分
		Password: password,  // 密码
		Nonce:    nonce,     // 从服务器获取的随机数
		Admin:    admin,     // 管理员标志
	}

	// 计算消息认证码（MAC）以验证请求的合法性
	macStr, err := getRegisterMac(sharedSecret, nonce, localpart, password, adminStr)
	if err != nil {
		return "", err
	}
	reg.MacStr = macStr

	// 将请求数据序列化为 JSON
	js, err := json.Marshal(reg)
	if err != nil {
		return "", fmt.Errorf("无法序列化 JSON: %w", err)
	}

	// 第二步：发送注册请求
	registerReq, err := http.NewRequest(http.MethodPost, registerURL, bytes.NewBuffer(js))
	if err != nil {
		return "", fmt.Errorf("无法创建 HTTP 请求: %w", err)
	}

	// 发送注册请求
	regResp, err := cl.Do(registerReq)
	if err != nil {
		return "", fmt.Errorf("无法创建账户: %w", err)
	}
	defer regResp.Body.Close() // 确保关闭响应体

	// 检查 HTTP 状态码
	if regResp.StatusCode < 200 || regResp.StatusCode >= 300 {
		body, _ = io.ReadAll(regResp.Body)
		return "", fmt.Errorf("服务器返回 HTTP %d 错误: %s", regResp.StatusCode, string(body))
	}

	// 读取成功响应
	r, err := io.ReadAll(regResp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败 (HTTP %d): %w", regResp.StatusCode, err)
	}

	// 从响应中提取访问令牌
	return gjson.GetBytes(r, "access_token").Str, nil
}

// getRegisterMac 计算注册请求的消息认证码（MAC）
// 使用 HMAC-SHA1 算法确保请求的完整性和真实性
// 参数:
//   - sharedSecret: 服务器配置的共享密钥
//   - nonce: 服务器提供的随机数
//   - localpart: 用户名的本地部分
//   - password: 用户密码
//   - adminStr: 管理员标识字符串（"admin" 或 "notadmin"）
//
// 返回: 十六进制编码的 MAC 字符串和可能的错误
func getRegisterMac(sharedSecret, nonce, localpart, password, adminStr string) (string, error) {
	// 将所有参数用空字节连接（这是 Synapse 的标准格式）
	joined := strings.Join([]string{nonce, localpart, password, adminStr}, "\x00")

	// 创建 HMAC-SHA1 哈希器
	mac := hmac.New(sha1.New, []byte(sharedSecret))

	// 写入要签名的数据
	_, err := mac.Write([]byte(joined))
	if err != nil {
		return "", fmt.Errorf("无法构造 MAC: %w", err)
	}

	// 计算最终的 MAC 值
	regMac := mac.Sum(nil)

	// 返回十六进制编码的 MAC
	return hex.EncodeToString(regMac), nil
}

// getPassword 从多种来源获取密码
// 支持命令行参数、文件、标准输入和交互式输入
// 参数:
//   - password: 命令行提供的密码
//   - pwdFile: 密码文件路径
//   - pwdStdin: 是否从标准输入读取
//   - r: 输入流读取器
//
// 返回: 密码字符串和可能的错误
func getPassword(password, pwdFile string, pwdStdin bool, r io.Reader) (string, error) {
	// 从文件读取密码
	if pwdFile != "" {
		pw, err := os.ReadFile(pwdFile)
		if err != nil {
			return "", fmt.Errorf("无法从文件读取密码: %v", err)
		}
		return strings.TrimSpace(string(pw)), nil
	}

	// 从标准输入读取密码
	if pwdStdin {
		data, err := io.ReadAll(r)
		if err != nil {
			return "", fmt.Errorf("无法从标准输入读取密码: %v", err)
		}
		return strings.TrimSpace(string(data)), nil
	}

	// 如果没有设置参数，要求用户交互式输入密码
	if password == "" {
		fmt.Print("输入密码: ")
		// 使用安全的密码输入（不显示字符）
		bytePassword, err := term.ReadPassword(int(os.Stdin.Fd()))
		if err != nil {
			return "", fmt.Errorf("无法读取密码: %v", err)
		}
		fmt.Println()

		fmt.Print("确认密码: ")
		// 再次输入密码进行确认
		bytePassword2, err := term.ReadPassword(int(os.Stdin.Fd()))
		if err != nil {
			return "", fmt.Errorf("无法读取密码: %v", err)
		}
		fmt.Println()

		// 验证两次输入的密码是否一致
		if strings.TrimSpace(string(bytePassword)) != strings.TrimSpace(string(bytePassword2)) {
			return "", fmt.Errorf("输入的密码不匹配")
		}
		return strings.TrimSpace(string(bytePassword)), nil
	}

	// 直接返回命令行提供的密码
	return password, nil
}

// userCenterRegister 使用用户中心登录类型注册新用户
// 参数:
//   - sharedSecret: 服务器配置的共享密钥
//   - serverURL: Dendrite 服务器 URL
//   - localpart: 用户名的本地部分
//   - password: 用户密码
//   - admin: 是否创建管理员账户
//
// 返回: 访问令牌和可能的错误
func userCenterRegister(sharedSecret, serverURL, localpart, password string, admin bool) (accessToken string, err error) {
	// 构建注册 API 端点 URL
	registerURL := fmt.Sprintf("%s/_matrix/client/v3/register", strings.Trim(serverURL, "/"))

	// 生成随机 nonce
	nonce := generateNonce()

	// 设置管理员标识字符串（用于 MAC 计算）
	adminStr := "notadmin"
	if admin {
		adminStr = "admin"
	}

	// 计算消息认证码（MAC）
	macStr, err := getRegisterMac(sharedSecret, nonce, localpart, password, adminStr)
	if err != nil {
		return "", err
	}

	// 生成会话ID
	sessionID := generateSessionID()

	// 生成设备显示名称
	deviceDisplayName := fmt.Sprintf("create-account-tool: %s", localpart)

	// 构建注册请求数据
	regData := map[string]interface{}{
		"username":                    localpart,
		"password":                    password,
		"initial_device_display_name": deviceDisplayName,
		"type":                        "m.login.xxai-usercenter", // 根级别的type字段
		"auth": map[string]interface{}{
			"session": sessionID,
			"type":    "m.login.xxai-usercenter",
			"nonce":   nonce,
			"mac":     macStr,
		},
		"token": "**********************************", // 可不用
	}

	// 将请求数据序列化为 JSON
	js, err := json.Marshal(regData)
	if err != nil {
		return "", fmt.Errorf("无法序列化 JSON: %w", err)
	}

	// 发送注册请求
	registerReq, err := http.NewRequest(http.MethodPost, registerURL, bytes.NewBuffer(js))
	if err != nil {
		return "", fmt.Errorf("无法创建 HTTP 请求: %w", err)
	}
	registerReq.Header.Set("Content-Type", "application/json")

	regResp, err := cl.Do(registerReq)
	if err != nil {
		return "", fmt.Errorf("无法创建账户: %w", err)
	}
	defer regResp.Body.Close()

	// 检查 HTTP 状态码
	if regResp.StatusCode < 200 || regResp.StatusCode >= 300 {
		body, _ := io.ReadAll(regResp.Body)
		return "", fmt.Errorf("服务器返回 HTTP %d 错误: %s", regResp.StatusCode, string(body))
	}

	// 读取成功响应
	r, err := io.ReadAll(regResp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败 (HTTP %d): %w", regResp.StatusCode, err)
	}

	// 从响应中提取访问令牌
	return gjson.GetBytes(r, "access_token").Str, nil
}

// botRegister 使用机器人登录类型注册新用户
// 参数:
//   - sharedSecret: 服务器配置的共享密钥
//   - serverURL: Dendrite 服务器 URL
//   - localpart: 用户名的本地部分
//   - password: 用户密码
//   - admin: 是否创建管理员账户
//
// 返回: 访问令牌和可能的错误
func botRegister(sharedSecret, serverURL, localpart, password string, admin bool) (accessToken string, err error) {
	// 构建注册 API 端点 URL
	registerURL := fmt.Sprintf("%s/_matrix/client/v3/register", strings.Trim(serverURL, "/"))

	// 生成随机 nonce
	nonce := generateNonce()

	// 设置管理员标识字符串（用于 MAC 计算）
	adminStr := "notadmin"
	if admin {
		adminStr = "admin"
	}

	// 计算消息认证码（MAC）
	macStr, err := getRegisterMac(sharedSecret, nonce, localpart, password, adminStr)
	if err != nil {
		return "", err
	}

	// 生成会话ID
	sessionID := generateSessionID()

	// 生成设备显示名称
	deviceDisplayName := fmt.Sprintf("create-account-tool: %s", localpart)

	// 构建注册请求数据
	regData := map[string]interface{}{
		"username":                    localpart,
		"password":                    password,
		"initial_device_display_name": deviceDisplayName,
		"auth": map[string]interface{}{
			"session": sessionID,
			"type":    "m.login.xxai-bot",
			"nonce":   nonce,
			"mac":     macStr,
		},
	}

	// 将请求数据序列化为 JSON
	js, err := json.Marshal(regData)
	if err != nil {
		return "", fmt.Errorf("无法序列化 JSON: %w", err)
	}

	// 发送注册请求
	registerReq, err := http.NewRequest(http.MethodPost, registerURL, bytes.NewBuffer(js))
	if err != nil {
		return "", fmt.Errorf("无法创建 HTTP 请求: %w", err)
	}
	registerReq.Header.Set("Content-Type", "application/json")

	regResp, err := cl.Do(registerReq)
	if err != nil {
		return "", fmt.Errorf("无法创建账户: %w", err)
	}
	defer regResp.Body.Close()

	// 检查 HTTP 状态码
	if regResp.StatusCode < 200 || regResp.StatusCode >= 300 {
		body, _ := io.ReadAll(regResp.Body)
		return "", fmt.Errorf("服务器返回 HTTP %d 错误: %s", regResp.StatusCode, string(body))
	}

	// 读取成功响应
	r, err := io.ReadAll(regResp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应体失败 (HTTP %d): %w", regResp.StatusCode, err)
	}

	// 从响应中提取访问令牌
	return gjson.GetBytes(r, "access_token").Str, nil
}

// generateNonce 生成随机 nonce
func generateNonce() string {
	// 使用时间戳和简单随机算法生成16字符的十六进制字符串
	now := time.Now().UnixNano()
	result := make([]byte, 16)
	chars := "0123456789abcdef"

	for i := 0; i < 16; i++ {
		now = now*1103515245 + 12345    // 线性同余生成器
		result[i] = chars[(now>>16)&15] // 使用位运算确保索引在范围内
	}

	return string(result)
}

// generateSessionID 生成会话ID
func generateSessionID() string {
	// 生成23字节的随机数据（类似于 t8bhB33Q0p0WilvNAtf7RKO4 的长度）
	bytes := make([]byte, 23)
	chars := "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
	for i := range bytes {
		bytes[i] = chars[time.Now().UnixNano()%int64(len(chars))]
	}
	return string(bytes)
}
