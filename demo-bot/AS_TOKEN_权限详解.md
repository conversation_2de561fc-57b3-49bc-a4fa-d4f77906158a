# AS Token 权限详解：一个Token管理多个机器人

## 🔑 核心概念

### sender_localpart ≠ 唯一用户

**重要理解**：`sender_localpart` 只是AS的**主要发送者**，不是唯一用户！

```yaml
# AS配置文件
sender_localpart: demo-bot  # 主要机器人：@demo-bot:localhost 

namespaces:
  users:
    - exclusive: true
      regex: "@demo-.*:localhost"    # 可管理所有demo-*用户
    - exclusive: true  
      regex: "@bot-.*:localhost"     # 可管理所有bot-*用户
    - exclusive: true
      regex: "@ai-.*:localhost"      # 可管理所有ai-*用户
```

## 🤖 一个AS Token可以管理的用户示例

### 实际可创建和管理的用户：

| 用户ID | 功能 | 状态 |
|--------|------|------|
| `@demo-bot:localhost` | 主要管理机器人 | ✅ 自动创建 |
| `@demo-assistant:localhost` | 智能助手 | ✅ 可创建 |
| `@demo-translator:localhost` | 翻译服务 | ✅ 可创建 |
| `@bot-weather:localhost` | 天气查询 | ✅ 可创建 |
| `@bot-news:localhost` | 新闻推送 | ✅ 可创建 |
| `@ai-coder:localhost` | 代码助手 | ✅ 可创建 |
| `@ai-chat:localhost` | 聊天机器人 | ✅ 可创建 |

## 🔐 AS Token的多用户权限

### 1. **用户创建权限**

```bash
# 使用同一个AS Token创建不同的机器人用户

# 创建AI助手
curl -X POST "http://localhost:8008/_matrix/client/r0/register" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "type": "m.login.application_service",
    "username": "ai-assistant"
  }'

# 创建翻译机器人
curl -X POST "http://localhost:8008/_matrix/client/r0/register" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "type": "m.login.application_service", 
    "username": "bot-translator"
  }'

# 创建天气机器人
curl -X POST "http://localhost:8008/_matrix/client/r0/register" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "type": "m.login.application_service",
    "username": "demo-weather"
  }'
```

### 2. **代表不同用户发送消息**

```bash
# AI助手发送消息
curl -X PUT "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/send/m.room.message/txn1?user_id=@ai-assistant:localhost" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "msgtype": "m.text",
    "body": "我是AI助手，有什么可以帮您的吗？"
  }'

# 翻译机器人发送消息  
curl -X PUT "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/send/m.room.message/txn2?user_id=@bot-translator:localhost" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "msgtype": "m.text", 
    "body": "Translation: Hello World! -> 你好世界！"
  }'

# 天气机器人发送消息
curl -X PUT "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/send/m.room.message/txn3?user_id=@demo-weather:localhost" \
  -H "Authorization: Bearer $AS_TOKEN" \
  -d '{
    "msgtype": "m.text",
    "body": "今天北京天气：晴，22°C"
  }'
```

### 3. **代表不同用户加入房间**

```bash
# AI助手加入房间
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/join?user_id=@ai-assistant:localhost" \
  -H "Authorization: Bearer $AS_TOKEN"

# 翻译机器人加入房间
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/join?user_id=@bot-translator:localhost" \
  -H "Authorization: Bearer $AS_TOKEN"
```

## 🏗️ 代码实现示例

### 多机器人管理器

```go
type MultiBotManager struct {
    asToken     string                    // 一个AS Token
    bots        map[string]*BotInfo       // 管理多个机器人
    dendriteURL string
}

// 使用同一个AS Token代表不同机器人发送消息
func (m *MultiBotManager) SendMessageAsBot(botUserID, roomID, message string) error {
    url := fmt.Sprintf("%s/_matrix/client/r0/rooms/%s/send/m.room.message/%s?user_id=%s",
        m.dendriteURL, roomID, txnID, botUserID)  // 关键：user_id参数
    
    req.Header.Set("Authorization", "Bearer "+m.asToken)  // 同一个AS Token
    
    // 发送请求...
}

// 智能选择机器人
func (m *MultiBotManager) SelectBotForMessage(message string) *BotInfo {
    if strings.Contains(message, "translate") {
        return m.bots["@bot-translator:localhost"]
    }
    if strings.Contains(message, "weather") {
        return m.bots["@demo-weather:localhost"] 
    }
    if strings.Contains(message, "ai") {
        return m.bots["@ai-assistant:localhost"]
    }
    return m.bots["@demo-bot:localhost"]  // 默认主机器人
}
```

## 🎯 实际应用场景

### 1. **智能客服系统**
```
一个AS Token管理：
├── @service-manager:localhost     (主管理机器人)
├── @ai-chat:localhost            (智能对话)
├── @bot-order:localhost          (订单查询)
├── @bot-support:localhost        (技术支持)
└── @notify-system:localhost      (系统通知)
```

### 2. **开发工具集成**
```
一个AS Token管理：
├── @dev-manager:localhost        (开发管理)
├── @bot-ci:localhost            (CI/CD通知)
├── @bot-deploy:localhost        (部署状态)
├── @ai-code-review:localhost    (代码审查)
└── @tool-monitor:localhost      (监控告警)
```

### 3. **教育平台机器人**
```
一个AS Token管理：
├── @edu-admin:localhost         (教务管理)
├── @ai-tutor:localhost         (AI导师)
├── @bot-quiz:localhost         (在线测验)
├── @bot-homework:localhost     (作业提醒)
└── @notify-class:localhost     (课程通知)
```

## ⚠️ 重要限制

### 1. **命名空间限制**
```yaml
# 只能管理配置的命名空间内的用户
namespaces:
  users:
    - exclusive: true
      regex: "@demo-.*:localhost"  # ✅ 可以管理
    - exclusive: true  
      regex: "@bot-.*:localhost"   # ✅ 可以管理

# ❌ 无法管理命名空间外的用户
# @normal-user:localhost  - 不在命名空间内
# @other-bot:localhost    - 不在命名空间内
```

### 2. **权限验证流程**
```
用户请求 → 验证AS Token → 检查用户是否在命名空间 → 执行操作
    ↓           ↓              ↓                    ↓
  有效Token    Token匹配       在命名空间内           成功
    ↓           ↓              ↓                    ↓
  无效Token → 401错误      不在命名空间 → 403错误    失败
```

## 📊 总结

| 特性 | 说明 |
|------|------|
| **一个AS Token** | 可以管理多个机器人用户 |
| **sender_localpart** | 只是主要发送者，不是唯一用户 |
| **命名空间** | 定义可管理的用户范围 |
| **user_id参数** | 指定代表哪个用户执行操作 |
| **权限验证** | 基于命名空间正则表达式 |

**结论**：一个AS Token可以管理命名空间内的所有用户，`sender_localpart` 只是其中的主要机器人，不是限制！
