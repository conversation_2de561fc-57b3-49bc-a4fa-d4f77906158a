#!/bin/bash

# NATS Matrix Bot Demo 启动脚本
set -e

echo "🚀 NATS Matrix Bot Demo 启动脚本"
echo "================================"

# 检查依赖
echo "📋 检查依赖..."

if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21+"
    exit 1
fi

if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

echo "✅ 依赖检查通过"

# 启动NATS服务器
echo ""
echo "📡 启动NATS服务器..."

# 检查NATS容器是否已运行
if docker ps | grep -q "nats-server"; then
    echo "✅ NATS服务器已在运行"
else
    echo "🔄 启动新的NATS服务器..."
    docker run -d \
        --name nats-server \
        -p 4222:4222 \
        -p 8222:8222 \
        nats:latest \
        --http_port 8222 \
        --name matrix-nats-server
    
    # 等待NATS启动
    echo "⏳ 等待NATS服务器启动..."
    sleep 3
    
    # 检查NATS是否正常运行
    if curl -s http://localhost:8222/varz > /dev/null; then
        echo "✅ NATS服务器启动成功"
    else
        echo "❌ NATS服务器启动失败"
        exit 1
    fi
fi

# 显示NATS信息
echo ""
echo "📊 NATS服务器信息:"
echo "• 客户端端口: 4222"
echo "• 监控端口: 8222"
echo "• 监控界面: http://localhost:8222"

# 安装Go依赖
echo ""
echo "📦 安装Go依赖..."

# 创建临时的go.mod用于NATS demo
cat > go-temp.mod << EOF
module matrix-nats-bot-demo

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/nats-io/nats.go v1.31.0
)
EOF

# 使用临时模块文件
export GOMOD=go-temp.mod
go mod download

echo "✅ 依赖安装完成"

# 创建简化的NATS demo
echo ""
echo "📝 创建简化的NATS demo..."

cat > nats-simple-demo.go << 'EOF'
package main

import (
    "encoding/json"
    "fmt"
    "log"
    "time"
    "github.com/nats-io/nats.go"
)

func main() {
    // 连接NATS
    nc, err := nats.Connect("nats://localhost:4222")
    if err != nil {
        log.Fatal("连接NATS失败:", err)
    }
    defer nc.Close()
    
    log.Println("✅ 已连接到NATS服务器")
    
    // 订阅Matrix事件
    _, err = nc.Subscribe("matrix.events.*", func(msg *nats.Msg) {
        log.Printf("📨 收到消息: %s -> %s", msg.Subject, string(msg.Data))
    })
    if err != nil {
        log.Fatal("订阅失败:", err)
    }
    
    log.Println("📡 已订阅 matrix.events.*")
    
    // 模拟发布Matrix事件
    go func() {
        for i := 0; i < 10; i++ {
            event := map[string]interface{}{
                "type": "m.room.message",
                "room_id": "!test:localhost",
                "sender": "@user:localhost",
                "content": map[string]interface{}{
                    "msgtype": "m.text",
                    "body": fmt.Sprintf("测试消息 #%d", i+1),
                },
                "timestamp": time.Now().Unix(),
            }
            
            data, _ := json.Marshal(event)
            nc.Publish("matrix.events.room.message", data)
            log.Printf("📤 发布消息 #%d", i+1)
            
            time.Sleep(2 * time.Second)
        }
    }()
    
    log.Println("🎯 Demo运行中，按Ctrl+C停止...")
    
    // 保持运行
    select {}
}
EOF

echo "✅ 简化demo创建完成"

# 启动demo
echo ""
echo "🎮 启动NATS Matrix Bot Demo..."
echo "• NATS监控: http://localhost:8222"
echo "• 按Ctrl+C停止demo"
echo ""

# 使用临时模块运行
GOMOD=go-temp.mod go run nats-simple-demo.go

# 清理
echo ""
echo "🧹 清理临时文件..."
rm -f go-temp.mod nats-simple-demo.go

echo "✅ Demo运行完成"
