# Matrix Bot Demo Application Service Registration
# 这个文件需要放在Dendrite配置目录中，并在dendrite.yaml中引用

# 唯一的Application Service ID
id: demo-bot-service

# Bot服务的URL（Dendrite会向这个URL发送事件）
url: http://localhost:8080

# Application Service Token（Bot用来向Dendrite认证）
# 在生产环境中请使用安全的随机字符串
as_token: "demo_as_token_replace_with_real_token"

# Homeserver Token（Dendrite用来向Bot认证）
# 在生产环境中请使用安全的随机字符串
hs_token: "demo_hs_token_replace_with_real_token"

# Bot的发送者用户名（不包含@和域名）
sender_localpart: demo-bot

# 命名空间配置 - 定义Bot可以管理的用户、房间别名等
namespaces:
  # 用户命名空间 - Bot可以创建和管理的用户
  users:
    - exclusive: true           # 独占模式，只有这个AS可以管理这些用户
      regex: "@demo-.*:localhost"  # 可以管理所有demo-开头的用户
    - exclusive: true
      regex: "@bot-.*:localhost"   # 可以管理所有bot-开头的用户
      
  # 房间别名命名空间 - Bot可以创建和管理的房间别名
  aliases:
    - exclusive: true
      regex: "#demo-.*:localhost"  # 可以管理所有demo-开头的房间别名
    - exclusive: false           # 非独占模式，其他服务也可以管理
      regex: "#bot-.*:localhost"   # 可以管理所有bot-开头的房间别名
      
  # 房间命名空间 - 通常为空，除非需要管理特定房间
  rooms: []

# 支持的协议 - 通常为空，除非实现了特定协议桥接
protocols: []

# 是否启用速率限制
rate_limited: false

# 是否推送临时事件（如输入指示器、已读回执等）
push_ephemeral: true

# 是否启用事务去重
de_duplicating_transactions: true
