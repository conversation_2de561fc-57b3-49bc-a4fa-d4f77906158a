// 多机器人管理示例
// 展示如何在一个AS中管理多个不同功能的机器人
package main

import (
	"fmt"
	"strings"
	"time"
)

// 机器人类型定义
type BotType string

const (
	BotTypeManager    BotType = "manager"    // 管理机器人
	BotTypeAI         BotType = "ai"         // AI助手
	BotTypeTranslator BotType = "translator" // 翻译机器人
	BotTypeNotify     BotType = "notify"     // 通知机器人
	BotTypeGame       BotType = "game"       // 游戏机器人
	BotTypeTool       BotType = "tool"       // 工具机器人
)

// 机器人信息结构体
type BotInfo struct {
	UserID      string  `json:"user_id"`
	Type        BotType `json:"type"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Active      bool    `json:"active"`
	LastSeen    int64   `json:"last_seen"`
}

// 多机器人管理器
type MultiBotManager struct {
	bots        map[string]*BotInfo // userID -> BotInfo
	mainBotID   string              // 主要机器人ID
	asToken     string              // AS Token
	dendriteURL string              // Dendrite URL
}

// 创建多机器人管理器
func NewMultiBotManager(mainBotID, asToken, dendriteURL string) *MultiBotManager {
	manager := &MultiBotManager{
		bots:        make(map[string]*BotInfo),
		mainBotID:   mainBotID,
		asToken:     asToken,
		dendriteURL: dendriteURL,
	}

	// 注册预定义的机器人
	manager.registerPredefinedBots()
	return manager
}

// 注册预定义的机器人
func (m *MultiBotManager) registerPredefinedBots() {
	bots := []*BotInfo{
		{
			UserID:      "@bot-manager:localhost",
			Type:        BotTypeManager,
			Name:        "管理机器人",
			Description: "负责AS管理和监控",
			Active:      true,
		},
		{
			UserID:      "@ai-assistant:localhost",
			Type:        BotTypeAI,
			Name:        "AI助手",
			Description: "智能对话和问答",
			Active:      true,
		},
		{
			UserID:      "@ai-coder:localhost",
			Type:        BotTypeAI,
			Name:        "代码助手",
			Description: "编程相关的帮助",
			Active:      true,
		},
		{
			UserID:      "@translator-en:localhost",
			Type:        BotTypeTranslator,
			Name:        "英语翻译",
			Description: "中英文翻译服务",
			Active:      true,
		},
		{
			UserID:      "@translator-zh:localhost",
			Type:        BotTypeTranslator,
			Name:        "中文翻译",
			Description: "英中文翻译服务",
			Active:      true,
		},
		{
			UserID:      "@notify-system:localhost",
			Type:        BotTypeNotify,
			Name:        "系统通知",
			Description: "系统状态和通知",
			Active:      true,
		},
		{
			UserID:      "@game-quiz:localhost",
			Type:        BotTypeGame,
			Name:        "问答游戏",
			Description: "知识问答和竞猜",
			Active:      false, // 默认不激活
		},
		{
			UserID:      "@tool-weather:localhost",
			Type:        BotTypeTool,
			Name:        "天气查询",
			Description: "天气信息查询",
			Active:      true,
		},
	}

	for _, bot := range bots {
		m.bots[bot.UserID] = bot
	}
}

// 根据消息内容选择合适的机器人
func (m *MultiBotManager) SelectBotForMessage(message string) *BotInfo {
	message = strings.ToLower(message)

	// AI相关关键词
	if strings.Contains(message, "ai") || strings.Contains(message, "智能") ||
		strings.Contains(message, "问答") || strings.Contains(message, "聊天") {
		return m.bots["@ai-assistant:localhost"]
	}

	// 编程相关关键词
	if strings.Contains(message, "code") || strings.Contains(message, "编程") ||
		strings.Contains(message, "代码") || strings.Contains(message, "bug") {
		return m.bots["@ai-coder:localhost"]
	}

	// 翻译相关关键词
	if strings.Contains(message, "translate") || strings.Contains(message, "翻译") ||
		strings.Contains(message, "english") || strings.Contains(message, "中文") {
		if strings.Contains(message, "english") || strings.Contains(message, "英语") {
			return m.bots["@translator-en:localhost"]
		}
		return m.bots["@translator-zh:localhost"]
	}

	// 天气相关关键词
	if strings.Contains(message, "weather") || strings.Contains(message, "天气") ||
		strings.Contains(message, "温度") || strings.Contains(message, "下雨") {
		return m.bots["@tool-weather:localhost"]
	}

	// 游戏相关关键词
	if strings.Contains(message, "game") || strings.Contains(message, "游戏") ||
		strings.Contains(message, "quiz") || strings.Contains(message, "问答") {
		// 激活游戏机器人
		m.bots["@game-quiz:localhost"].Active = true
		return m.bots["@game-quiz:localhost"]
	}

	// 管理相关关键词
	if strings.Contains(message, "status") || strings.Contains(message, "状态") ||
		strings.Contains(message, "help") || strings.Contains(message, "帮助") {
		return m.bots["@bot-manager:localhost"]
	}

	// 默认使用AI助手
	return m.bots["@ai-assistant:localhost"]
}

// 生成不同机器人的回复
func (m *MultiBotManager) GenerateResponse(bot *BotInfo, message, sender string) string {
	// 更新机器人活跃时间
	bot.LastSeen = time.Now().Unix()

	switch bot.Type {
	case BotTypeManager:
		return m.generateManagerResponse(message, sender)
	case BotTypeAI:
		return m.generateAIResponse(bot, message, sender)
	case BotTypeTranslator:
		return m.generateTranslatorResponse(bot, message, sender)
	case BotTypeNotify:
		return m.generateNotifyResponse(message, sender)
	case BotTypeGame:
		return m.generateGameResponse(message, sender)
	case BotTypeTool:
		return m.generateToolResponse(bot, message, sender)
	default:
		return fmt.Sprintf("🤖 我是 %s，暂时无法处理这个请求。", bot.Name)
	}
}

// 管理机器人回复
func (m *MultiBotManager) generateManagerResponse(message, sender string) string {
	message = strings.ToLower(message)

	if strings.Contains(message, "status") || strings.Contains(message, "状态") {
		activeCount := 0
		for _, bot := range m.bots {
			if bot.Active {
				activeCount++
			}
		}
		return fmt.Sprintf("🔧 系统状态：\n• 总机器人数：%d\n• 活跃机器人：%d\n• 服务正常运行", 
			len(m.bots), activeCount)
	}

	if strings.Contains(message, "help") || strings.Contains(message, "帮助") {
		return `🆘 多机器人服务帮助：

🤖 可用机器人：
• @ai-assistant - AI智能助手
• @ai-coder - 编程代码助手  
• @translator-en - 英语翻译
• @translator-zh - 中文翻译
• @tool-weather - 天气查询
• @game-quiz - 问答游戏
• @notify-system - 系统通知

💡 使用方法：
直接发送相关关键词，系统会自动选择合适的机器人回复。

例如：
• "天气怎么样" → 天气机器人
• "translate hello" → 翻译机器人
• "写个代码" → 代码助手`
	}

	return fmt.Sprintf("👋 你好 %s！我是管理机器人，输入 'help' 查看可用服务。", sender)
}

// AI机器人回复
func (m *MultiBotManager) generateAIResponse(bot *BotInfo, message, sender string) string {
	if bot.UserID == "@ai-coder:localhost" {
		return fmt.Sprintf("💻 %s，我是代码助手！关于编程问题：\n\n%s\n\n需要具体的代码示例吗？", 
			sender, "这是一个很好的编程问题，让我来帮你分析...")
	}

	return fmt.Sprintf("🧠 %s，我是AI助手！关于你的问题：\n\n%s", 
		sender, "这是一个有趣的问题，让我思考一下...")
}

// 翻译机器人回复
func (m *MultiBotManager) generateTranslatorResponse(bot *BotInfo, message, sender string) string {
	if bot.UserID == "@translator-en:localhost" {
		return fmt.Sprintf("🌍 %s，英语翻译服务：\n\n原文：%s\n译文：%s", 
			sender, message, "Hello, this is a translation example.")
	}

	return fmt.Sprintf("🌏 %s，中文翻译服务：\n\n原文：%s\n译文：%s", 
		sender, message, "你好，这是一个翻译示例。")
}

// 通知机器人回复
func (m *MultiBotManager) generateNotifyResponse(message, sender string) string {
	return fmt.Sprintf("📢 %s，系统通知：\n\n当前时间：%s\n系统状态：正常", 
		sender, time.Now().Format("2006-01-02 15:04:05"))
}

// 游戏机器人回复
func (m *MultiBotManager) generateGameResponse(message, sender string) string {
	questions := []string{
		"🎯 问题：Go语言的创始人是谁？",
		"🎯 问题：Matrix协议的默认端口是多少？",
		"🎯 问题：REST API中GET方法是做什么的？",
	}
	
	question := questions[time.Now().Unix()%int64(len(questions))]
	return fmt.Sprintf("🎮 %s，欢迎来到问答游戏！\n\n%s\n\n回复答案开始游戏！", sender, question)
}

// 工具机器人回复
func (m *MultiBotManager) generateToolResponse(bot *BotInfo, message, sender string) string {
	if bot.UserID == "@tool-weather:localhost" {
		return fmt.Sprintf("🌤️ %s，天气查询服务：\n\n📍 位置：北京\n🌡️ 温度：22°C\n☁️ 天气：多云\n💨 风速：3级", sender)
	}

	return fmt.Sprintf("🔧 %s，工具服务暂时不可用。", sender)
}

// 获取所有机器人信息
func (m *MultiBotManager) GetAllBots() map[string]*BotInfo {
	return m.bots
}

// 获取活跃机器人
func (m *MultiBotManager) GetActiveBots() []*BotInfo {
	var active []*BotInfo
	for _, bot := range m.bots {
		if bot.Active {
			active = append(active, bot)
		}
	}
	return active
}

// 激活/停用机器人
func (m *MultiBotManager) SetBotActive(userID string, active bool) bool {
	if bot, exists := m.bots[userID]; exists {
		bot.Active = active
		return true
	}
	return false
}
