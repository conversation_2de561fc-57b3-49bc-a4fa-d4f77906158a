#!/bin/bash

# Matrix Bot Demo 安装和配置脚本
# 这个脚本会自动生成令牌、配置文件并启动服务

set -e

echo "🤖 Matrix Bot Demo 安装脚本"
echo "================================"

# 检查依赖
echo "📋 检查依赖..."

if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go 1.21+"
    exit 1
fi

if ! command -v openssl &> /dev/null; then
    echo "❌ OpenSSL未安装，请先安装OpenSSL"
    exit 1
fi

echo "✅ 依赖检查通过"

# 生成安全令牌
echo ""
echo "🔐 生成安全令牌..."

AS_TOKEN=$(openssl rand -hex 32)
HS_TOKEN=$(openssl rand -hex 32)

echo "AS Token: $AS_TOKEN"
echo "HS Token: $HS_TOKEN"

# 创建配置文件
echo ""
echo "📝 创建配置文件..."

# 更新AS注册文件
sed -i.bak "s/demo_as_token_replace_with_real_token/$AS_TOKEN/g" appservice-registration.yaml
sed -i.bak "s/demo_hs_token_replace_with_real_token/$HS_TOKEN/g" appservice-registration.yaml

# 更新main.go中的配置
sed -i.bak "s/demo_as_token_replace_with_real_token/$AS_TOKEN/g" main.go
sed -i.bak "s/demo_hs_token_replace_with_real_token/$HS_TOKEN/g" main.go

echo "✅ 配置文件已更新"

# 安装Go依赖
echo ""
echo "📦 安装Go依赖..."
go mod tidy

# 创建启动脚本
cat > start-bot.sh << EOF
#!/bin/bash
echo "🚀 启动Matrix Bot Demo..."
echo "管理界面: http://localhost:8080"
echo "按Ctrl+C停止服务"
echo ""
go run main.go
EOF

chmod +x start-bot.sh

# 创建Dendrite配置示例
cat > dendrite-config-example.yaml << EOF
# 在你的dendrite.yaml中添加以下配置

app_service_api:
  # 启用Application Service API
  listen: http://0.0.0.0:7777
  bind: 0.0.0.0:7777
  
  # AS配置文件路径（请修改为实际路径）
  config_files:
    - $(pwd)/appservice-registration.yaml
    
  # 可选：禁用TLS验证（仅用于开发）
  disable_tls_validation: true
EOF

# 创建测试脚本
cat > test-bot.sh << EOF
#!/bin/bash

echo "🧪 测试Matrix Bot Demo"
echo "====================="

# 等待服务启动
sleep 2

echo "1. 测试状态API..."
curl -s http://localhost:8080/status | jq . || echo "状态API测试失败"

echo ""
echo "2. 测试房间列表API..."
curl -s http://localhost:8080/rooms | jq . || echo "房间列表API测试失败"

echo ""
echo "3. 测试用户列表API..."
curl -s http://localhost:8080/users | jq . || echo "用户列表API测试失败"

echo ""
echo "✅ API测试完成"
echo "💡 请在Matrix客户端中测试聊天功能"
EOF

chmod +x test-bot.sh

# 显示下一步操作
echo ""
echo "🎉 安装完成！"
echo "=============="
echo ""
echo "📋 下一步操作："
echo ""
echo "1. 配置Dendrite服务器："
echo "   - 编辑你的dendrite.yaml文件"
echo "   - 添加以下配置到app_service_api部分："
echo "     config_files:"
echo "       - $(pwd)/appservice-registration.yaml"
echo ""
echo "2. 重启Dendrite服务器以加载AS配置"
echo ""
echo "3. 启动Bot服务："
echo "   ./start-bot.sh"
echo ""
echo "4. 访问管理界面："
echo "   http://localhost:8080"
echo ""
echo "5. 测试API："
echo "   ./test-bot.sh"
echo ""
echo "📚 详细说明请查看README.md文件"
echo ""
echo "🔑 生成的令牌："
echo "AS Token: $AS_TOKEN"
echo "HS Token: $HS_TOKEN"
echo ""
echo "⚠️  请妥善保存这些令牌！"
