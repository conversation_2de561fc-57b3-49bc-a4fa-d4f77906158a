# 多机器人AS配置示例
# 展示如何在一个AS中管理多个机器人用户

id: multi-bot-service
url: http://localhost:8080

as_token: "your_as_token_here"
hs_token: "your_hs_token_here"

# 主要发送者 - AS的"管理员"机器人
sender_localpart: bot-manager

# 命名空间配置 - 可以管理多种类型的机器人
namespaces:
  users:
    # 管理机器人 - 负责AS管理和监控
    - exclusive: true
      regex: "@bot-manager:localhost"
    
    # AI助手机器人 - 负责智能对话
    - exclusive: true
      regex: "@ai-.*:localhost"
    
    # 翻译机器人 - 负责语言翻译
    - exclusive: true
      regex: "@translator-.*:localhost"
    
    # 通知机器人 - 负责系统通知
    - exclusive: true
      regex: "@notify-.*:localhost"
    
    # 游戏机器人 - 负责娱乐功能
    - exclusive: true
      regex: "@game-.*:localhost"
    
    # 工具机器人 - 负责实用工具
    - exclusive: true
      regex: "@tool-.*:localhost"

  aliases:
    # 机器人相关的房间别名
    - exclusive: true
      regex: "#bot-.*:localhost"
    - exclusive: true
      regex: "#ai-.*:localhost"
    - exclusive: true
      regex: "#service-.*:localhost"

  rooms: []

protocols: []
rate_limited: false
push_ephemeral: true
de_duplicating_transactions: true

# 这个AS可以创建和管理的用户示例：
# @bot-manager:localhost     - 主要管理机器人
# @ai-assistant:localhost    - AI对话助手
# @ai-coder:localhost        - 代码助手
# @translator-en:localhost   - 英语翻译机器人
# @translator-zh:localhost   - 中文翻译机器人
# @notify-system:localhost   - 系统通知机器人
# @notify-alert:localhost    - 告警通知机器人
# @game-quiz:localhost       - 问答游戏机器人
# @game-dice:localhost       - 骰子游戏机器人
# @tool-weather:localhost    - 天气查询机器人
# @tool-calc:localhost       - 计算器机器人
