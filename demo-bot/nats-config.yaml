# NATS配置文件
# 用于Matrix Bot的NATS消息队列配置

# NATS服务器配置
nats:
  # NATS服务器URL
  url: "nats://localhost:4222"
  
  # 集群配置（可选）
  # cluster:
  #   - "nats://nats1:4222"
  #   - "nats://nats2:4222"
  #   - "nats://nats3:4222"
  
  # 认证配置（可选）
  # auth:
  #   username: "matrix_user"
  #   password: "matrix_pass"
  #   token: "your_nats_token"
  
  # TLS配置（可选）
  # tls:
  #   cert_file: "/path/to/client.crt"
  #   key_file: "/path/to/client.key"
  #   ca_file: "/path/to/ca.crt"
  
  # 连接配置
  connection:
    # 连接超时
    timeout: "5s"
    # 重连等待时间
    reconnect_wait: "2s"
    # 最大重连次数 (-1 表示无限重连)
    max_reconnects: -1
    # 连接名称
    name: "matrix-bot-service"

# Matrix事件主题配置
subjects:
  # 基础主题前缀
  prefix: "matrix.events"
  
  # 具体事件主题
  events:
    # 所有事件
    all: "matrix.events.*"
    # 房间消息
    room_message: "matrix.events.room.message"
    # 成员变更
    room_member: "matrix.events.room.member"
    # 房间创建
    room_create: "matrix.events.room.create"
    # 房间状态
    room_state: "matrix.events.room.state"
    # 邀请事件
    room_invite: "matrix.events.room.invite"
    
  # 机器人回复主题
  bot_responses:
    # 机器人回复
    response: "matrix.events.bot.response"
    # 机器人状态
    status: "matrix.events.bot.status"
    # 机器人错误
    error: "matrix.events.bot.error"

# 消息处理配置
processing:
  # 消息确认模式
  ack_mode: "auto"  # auto, manual
  
  # 批处理配置
  batch:
    enabled: false
    size: 10
    timeout: "1s"
  
  # 重试配置
  retry:
    enabled: true
    max_attempts: 3
    backoff: "exponential"  # linear, exponential
    initial_delay: "1s"
    max_delay: "30s"

# 监控配置
monitoring:
  # 启用监控
  enabled: true
  
  # 监控端口
  port: 8222
  
  # 健康检查
  health_check:
    enabled: true
    interval: "30s"
  
  # 指标收集
  metrics:
    enabled: true
    path: "/metrics"
    
# 日志配置
logging:
  # 日志级别: debug, info, warn, error
  level: "info"
  
  # 日志格式: text, json
  format: "text"
  
  # 日志输出
  output: "stdout"  # stdout, stderr, file
  
  # 日志文件（当output为file时）
  # file: "/var/log/matrix-bot.log"

# 性能配置
performance:
  # 工作协程数
  workers: 4
  
  # 消息缓冲区大小
  buffer_size: 1000
  
  # 连接池大小
  connection_pool: 10
