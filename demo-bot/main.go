// Matrix Application Service Bot Demo
// 这是一个完整的AS机器人示例，展示如何创建、配置和操作Matrix机器人
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 配置结构体
type BotConfig struct {
	// Application Service 配置
	ASToken     string `json:"as_token"`     // AS令牌
	HSToken     string `json:"hs_token"`     // Homeserver令牌
	BotUserID   string `json:"bot_user_id"`  // 机器人用户ID
	DendriteURL string `json:"dendrite_url"` // Dendrite服务器URL
	ListenPort  string `json:"listen_port"`  // 监听端口
	ServerName  string `json:"server_name"`  // 服务器名称
}

// Matrix事件结构体
type MatrixEvent struct {
	Type      string                 `json:"type"`
	EventID   string                 `json:"event_id"`
	RoomID    string                 `json:"room_id"`
	Sender    string                 `json:"sender"`
	Content   map[string]interface{} `json:"content"`
	Timestamp int64                  `json:"origin_server_ts"`
}

// Matrix事务结构体
type MatrixTransaction struct {
	Events []MatrixEvent `json:"events"`
}

// 消息内容结构体
type MessageContent struct {
	MsgType string `json:"msgtype"`
	Body    string `json:"body"`
}

// 机器人服务结构体
type BotService struct {
	config     *BotConfig
	httpClient *http.Client
	// 简单的内存存储，实际应用中应使用数据库
	rooms    map[string]bool   // 已加入的房间
	users    map[string]string // 用户信息
	lastSeen map[string]int64  // 最后活跃时间
}

// 创建新的机器人服务
func NewBotService(config *BotConfig) *BotService {
	return &BotService{
		config:     config,
		httpClient: &http.Client{Timeout: 30 * time.Second},
		rooms:      make(map[string]bool),
		users:      make(map[string]string),
		lastSeen:   make(map[string]int64),
	}
}

// 启动机器人服务
func (bs *BotService) Start() {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// Matrix AS端点 - 接收来自Dendrite的事务
	r.PUT("/transactions/:txnId", bs.handleTransaction)
	r.PUT("/_matrix/app/v1/transactions/:txnId", bs.handleTransaction)

	// 管理API端点
	r.GET("/status", bs.getStatus)
	r.POST("/send-message", bs.sendMessage)
	r.POST("/join-room", bs.joinRoom)
	r.GET("/rooms", bs.listRooms)
	r.GET("/users", bs.listUsers)

	// 启动Web界面
	r.Static("/static", "./static")
	r.GET("/", func(c *gin.Context) {
		c.Header("Content-Type", "text/html")
		c.String(200, getWebInterface())
	})

	log.Printf("🤖 Matrix Bot Demo 启动在端口 %s", bs.config.ListenPort)
	log.Printf("📊 管理界面: http://localhost%s", bs.config.ListenPort)
	log.Printf("🔗 Dendrite URL: %s", bs.config.DendriteURL)
	log.Printf("👤 Bot User ID: %s", bs.config.BotUserID)

	if err := r.Run(bs.config.ListenPort); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}

// 处理来自Dendrite的事务
func (bs *BotService) handleTransaction(c *gin.Context) {
	txnID := c.Param("txnId")

	// 验证Homeserver令牌
	authHeader := c.GetHeader("Authorization")
	if !strings.HasPrefix(authHeader, "Bearer ") ||
		strings.TrimPrefix(authHeader, "Bearer ") != bs.config.HSToken {
		log.Printf("❌ 无效的HS令牌: %s", authHeader)
		c.JSON(401, gin.H{"error": "Invalid homeserver token"})
		return
	}

	var transaction MatrixTransaction
	if err := c.ShouldBindJSON(&transaction); err != nil {
		log.Printf("❌ 解析事务失败: %v", err)
		c.JSON(400, gin.H{"error": "Invalid transaction format"})
		return
	}

	log.Printf("📨 收到事务 %s，包含 %d 个事件", txnID, len(transaction.Events))

	// 处理每个事件
	for _, event := range transaction.Events {
		bs.handleEvent(event)
	}

	c.JSON(200, gin.H{})
}

// 处理单个Matrix事件
func (bs *BotService) handleEvent(event MatrixEvent) {
	log.Printf("🔔 处理事件: %s 在房间 %s 来自 %s", event.Type, event.RoomID, event.Sender)

	// 更新用户最后活跃时间
	bs.lastSeen[event.Sender] = event.Timestamp

	switch event.Type {
	case "m.room.message":
		bs.handleMessage(event)
	case "m.room.member":
		bs.handleMembership(event)
	case "m.room.create":
		bs.handleRoomCreate(event)
	default:
		log.Printf("📝 忽略事件类型: %s", event.Type)
	}
}

// 处理消息事件
func (bs *BotService) handleMessage(event MatrixEvent) {
	// 忽略机器人自己的消息
	if event.Sender == bs.config.BotUserID {
		return
	}

	content, ok := event.Content["body"].(string)
	if !ok {
		return
	}

	log.Printf("💬 收到消息: %s", content)

	// 简单的命令处理
	response := bs.generateResponse(content, event.Sender)
	if response != "" {
		go bs.sendReply(event.RoomID, response)
	}
}

// 生成回复内容
func (bs *BotService) generateResponse(message, sender string) string {
	message = strings.ToLower(strings.TrimSpace(message))

	switch {
	case strings.Contains(message, "hello") || strings.Contains(message, "hi"):
		return fmt.Sprintf("Hello %s! 我是Matrix机器人助手 🤖", sender)
	case strings.Contains(message, "help"):
		return `🆘 可用命令:
• hello/hi - 打招呼
• help - 显示帮助
• time - 显示当前时间
• status - 显示机器人状态
• joke - 讲个笑话`
	case strings.Contains(message, "time"):
		return fmt.Sprintf("🕐 当前时间: %s", time.Now().Format("2006-01-02 15:04:05"))
	case strings.Contains(message, "status"):
		return fmt.Sprintf("✅ 机器人运行正常\n📊 已加入 %d 个房间\n👥 见过 %d 个用户",
			len(bs.rooms), len(bs.users))
	case strings.Contains(message, "joke"):
		jokes := []string{
			"为什么程序员喜欢黑暗？因为光明会产生bug！",
			"有10种人：懂二进制的和不懂二进制的。",
			"程序员的三大美德：懒惰、急躁和傲慢。",
		}
		return jokes[time.Now().Unix()%int64(len(jokes))]
	default:
		if strings.Contains(message, "bot") || strings.Contains(message, "机器人") {
			return "🤖 我在这里！有什么可以帮助您的吗？输入 'help' 查看可用命令。"
		}
	}

	return ""
}

// 发送回复消息
func (bs *BotService) sendReply(roomID, message string) {
	if err := bs.SendMessage(roomID, message); err != nil {
		log.Printf("❌ 发送回复失败: %v", err)
	} else {
		log.Printf("✅ 已发送回复到房间 %s: %s", roomID, message)
	}
}

// 处理成员变更事件
func (bs *BotService) handleMembership(event MatrixEvent) {
	membership, ok := event.Content["membership"].(string)
	if !ok {
		return
	}

	log.Printf("👥 成员变更: %s -> %s 在房间 %s", event.Sender, membership, event.RoomID)

	// 记录房间信息
	if membership == "join" {
		bs.rooms[event.RoomID] = true
		bs.users[event.Sender] = membership
	}
}

// 处理房间创建事件
func (bs *BotService) handleRoomCreate(event MatrixEvent) {
	log.Printf("🏠 房间创建: %s", event.RoomID)
	bs.rooms[event.RoomID] = true
}

// SendMessage 发送消息到指定房间
func (bs *BotService) SendMessage(roomID, message string) error {
	content := MessageContent{
		MsgType: "m.text",
		Body:    message,
	}

	// 生成事务ID
	txnID := fmt.Sprintf("%d", time.Now().UnixNano())
	url := fmt.Sprintf("%s/_matrix/client/r0/rooms/%s/send/m.room.message/%s",
		bs.config.DendriteURL, roomID, txnID)

	payload, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bs.config.ASToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := bs.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("发送消息失败: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// JoinRoom 加入指定房间
func (bs *BotService) JoinRoom(roomID string) error {
	url := fmt.Sprintf("%s/_matrix/client/r0/rooms/%s/join", bs.config.DendriteURL, roomID)

	req, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte("{}")))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+bs.config.ASToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := bs.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("加入房间失败: %d - %s", resp.StatusCode, string(body))
	}

	bs.rooms[roomID] = true
	log.Printf("✅ 成功加入房间: %s", roomID)
	return nil
}

// API处理函数

// 获取机器人状态
func (bs *BotService) getStatus(c *gin.Context) {
	status := gin.H{
		"bot_user_id":  bs.config.BotUserID,
		"dendrite_url": bs.config.DendriteURL,
		"rooms_count":  len(bs.rooms),
		"users_count":  len(bs.users),
		"uptime":       time.Now().Format("2006-01-02 15:04:05"),
		"rooms":        bs.rooms,
		"recent_users": bs.getRecentUsers(),
	}
	c.JSON(200, status)
}

// 发送消息API
func (bs *BotService) sendMessage(c *gin.Context) {
	var req struct {
		RoomID  string `json:"room_id" binding:"required"`
		Message string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	if err := bs.SendMessage(req.RoomID, req.Message); err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "消息发送成功"})
}

// 加入房间API
func (bs *BotService) joinRoom(c *gin.Context) {
	var req struct {
		RoomID string `json:"room_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	if err := bs.JoinRoom(req.RoomID); err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "成功加入房间"})
}

// 列出房间
func (bs *BotService) listRooms(c *gin.Context) {
	rooms := make([]string, 0, len(bs.rooms))
	for roomID := range bs.rooms {
		rooms = append(rooms, roomID)
	}
	c.JSON(200, gin.H{"rooms": rooms})
}

// 列出用户
func (bs *BotService) listUsers(c *gin.Context) {
	c.JSON(200, gin.H{
		"users":     bs.users,
		"last_seen": bs.lastSeen,
		"recent":    bs.getRecentUsers(),
	})
}

// 获取最近活跃用户
func (bs *BotService) getRecentUsers() []string {
	recent := make([]string, 0)
	cutoff := time.Now().Add(-24*time.Hour).Unix() * 1000 // 24小时内

	for userID, lastSeen := range bs.lastSeen {
		if lastSeen > cutoff {
			recent = append(recent, userID)
		}
	}
	return recent
}

// Web界面HTML
func getWebInterface() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matrix Bot Demo 管理界面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; }
        .status { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .stat-item { text-align: center; padding: 15px; background: #e3f2fd; border-radius: 6px; }
        .controls { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        input, textarea, button { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #2196f3; color: white; cursor: pointer; border: none; }
        button:hover { background: #1976d2; }
        .log { height: 300px; overflow-y: auto; background: #f8f8f8; padding: 10px; font-family: monospace; font-size: 12px; }
        .room-list, .user-list { max-height: 200px; overflow-y: auto; }
        .success { color: #4caf50; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1 class="header">🤖 Matrix Bot Demo 管理界面</h1>
        </div>

        <div class="card">
            <h2>📊 机器人状态</h2>
            <div id="status" class="status">
                <div class="stat-item">
                    <h3>机器人ID</h3>
                    <p id="bot-id">加载中...</p>
                </div>
                <div class="stat-item">
                    <h3>已加入房间</h3>
                    <p id="rooms-count">0</p>
                </div>
                <div class="stat-item">
                    <h3>活跃用户</h3>
                    <p id="users-count">0</p>
                </div>
                <div class="stat-item">
                    <h3>运行时间</h3>
                    <p id="uptime">--</p>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="card">
                <h2>💬 发送消息</h2>
                <input type="text" id="room-id" placeholder="房间ID (例: !abc123:localhost)">
                <textarea id="message" placeholder="输入消息内容..." rows="3"></textarea>
                <button onclick="sendMessage()">发送消息</button>
                <div id="send-result"></div>
            </div>

            <div class="card">
                <h2>🚪 加入房间</h2>
                <input type="text" id="join-room-id" placeholder="房间ID (例: !abc123:localhost)">
                <button onclick="joinRoom()">加入房间</button>
                <div id="join-result"></div>
            </div>
        </div>

        <div class="controls">
            <div class="card">
                <h2>🏠 已加入房间</h2>
                <div id="rooms-list" class="room-list">加载中...</div>
                <button onclick="refreshRooms()">刷新房间列表</button>
            </div>

            <div class="card">
                <h2>👥 用户列表</h2>
                <div id="users-list" class="user-list">加载中...</div>
                <button onclick="refreshUsers()">刷新用户列表</button>
            </div>
        </div>

        <div class="card">
            <h2>📝 操作日志</h2>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logContainer.innerHTML += '<div class="' + className + '">[' + timestamp + '] ' + message + '</div>';
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        async function sendMessage() {
            const roomId = document.getElementById('room-id').value;
            const message = document.getElementById('message').value;

            if (!roomId || !message) {
                log('请填写房间ID和消息内容', 'error');
                return;
            }

            try {
                const response = await fetch('/send-message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ room_id: roomId, message: message })
                });

                const result = await response.json();
                if (response.ok) {
                    log('消息发送成功: ' + message, 'success');
                    document.getElementById('message').value = '';
                    document.getElementById('send-result').innerHTML = '<span class="success">✅ 发送成功</span>';
                } else {
                    log('发送失败: ' + result.error, 'error');
                    document.getElementById('send-result').innerHTML = '<span class="error">❌ ' + result.error + '</span>';
                }
            } catch (error) {
                log('发送请求失败: ' + error.message, 'error');
                document.getElementById('send-result').innerHTML = '<span class="error">❌ 网络错误</span>';
            }
        }

        async function joinRoom() {
            const roomId = document.getElementById('join-room-id').value;

            if (!roomId) {
                log('请填写房间ID', 'error');
                return;
            }

            try {
                const response = await fetch('/join-room', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ room_id: roomId })
                });

                const result = await response.json();
                if (response.ok) {
                    log('成功加入房间: ' + roomId, 'success');
                    document.getElementById('join-room-id').value = '';
                    document.getElementById('join-result').innerHTML = '<span class="success">✅ 加入成功</span>';
                    refreshRooms();
                } else {
                    log('加入房间失败: ' + result.error, 'error');
                    document.getElementById('join-result').innerHTML = '<span class="error">❌ ' + result.error + '</span>';
                }
            } catch (error) {
                log('加入房间请求失败: ' + error.message, 'error');
                document.getElementById('join-result').innerHTML = '<span class="error">❌ 网络错误</span>';
            }
        }

        async function refreshStatus() {
            try {
                const response = await fetch('/status');
                const status = await response.json();

                document.getElementById('bot-id').textContent = status.bot_user_id;
                document.getElementById('rooms-count').textContent = status.rooms_count;
                document.getElementById('users-count').textContent = status.users_count;
                document.getElementById('uptime').textContent = status.uptime;
            } catch (error) {
                log('获取状态失败: ' + error.message, 'error');
            }
        }

        async function refreshRooms() {
            try {
                const response = await fetch('/rooms');
                const data = await response.json();
                const roomsList = document.getElementById('rooms-list');

                if (data.rooms && data.rooms.length > 0) {
                    roomsList.innerHTML = data.rooms.map(room =>
                        '<div style="padding: 5px; border-bottom: 1px solid #eee;">' + room + '</div>'
                    ).join('');
                } else {
                    roomsList.innerHTML = '<div style="color: #666;">暂无已加入的房间</div>';
                }
            } catch (error) {
                log('获取房间列表失败: ' + error.message, 'error');
            }
        }

        async function refreshUsers() {
            try {
                const response = await fetch('/users');
                const data = await response.json();
                const usersList = document.getElementById('users-list');

                if (data.recent && data.recent.length > 0) {
                    usersList.innerHTML = data.recent.map(user =>
                        '<div style="padding: 5px; border-bottom: 1px solid #eee;">' + user + '</div>'
                    ).join('');
                } else {
                    usersList.innerHTML = '<div style="color: #666;">暂无活跃用户</div>';
                }
            } catch (error) {
                log('获取用户列表失败: ' + error.message, 'error');
            }
        }

        // 初始化和定时刷新
        document.addEventListener('DOMContentLoaded', function() {
            log('Matrix Bot Demo 管理界面已加载', 'success');
            refreshStatus();
            refreshRooms();
            refreshUsers();

            // 每30秒刷新一次状态
            setInterval(refreshStatus, 30000);
        });
    </script>
</body>
</html>`
}

// main函数
func main() {
	// 默认配置
	config := &BotConfig{
		ASToken:     "demo_as_token_replace_with_real_token",
		HSToken:     "demo_hs_token_replace_with_real_token",
		BotUserID:   "@demo-bot:localhost",
		DendriteURL: "http://localhost:8008",
		ListenPort:  ":8080",
		ServerName:  "localhost",
	}

	log.Println("🚀 启动Matrix Bot Demo...")
	log.Printf("⚠️  请确保已配置正确的AS Token和HS Token")
	log.Printf("⚠️  请确保Dendrite服务器运行在: %s", config.DendriteURL)

	// 创建并启动机器人服务
	botService := NewBotService(config)
	botService.Start()
}
