// NATS集成的Matrix Bot示例
// 展示如何通过NATS消息队列接收Matrix事件
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nats-io/nats.go"
)

// NATS配置
type NATSConfig struct {
	URL      string `json:"url"`
	Subject  string `json:"subject"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

// NATS机器人服务
type NATSBotService struct {
	config     *BotConfig
	natsConfig *NATSConfig
	natsConn   *nats.Conn
	botManager *MultiBotManager
	httpClient *http.Client

	// 数据存储
	rooms    map[string]bool
	users    map[string]string
	lastSeen map[string]int64
}

// Matrix事件通过NATS传输的结构
type NATSMatrixEvent struct {
	Type      string                 `json:"type"`
	EventID   string                 `json:"event_id"`
	RoomID    string                 `json:"room_id"`
	Sender    string                 `json:"sender"`
	Content   map[string]interface{} `json:"content"`
	Timestamp int64                  `json:"origin_server_ts"`
	TxnID     string                 `json:"txn_id"`
}

// 创建NATS机器人服务
func NewNATSBotService(config *BotConfig, natsConfig *NATSConfig) *NATSBotService {
	botManager := NewMultiBotManager(config.BotUserID, config.ASToken, config.DendriteURL)

	return &NATSBotService{
		config:     config,
		natsConfig: natsConfig,
		botManager: botManager,
		httpClient: &http.Client{Timeout: 30 * time.Second},
		rooms:      make(map[string]bool),
		users:      make(map[string]string),
		lastSeen:   make(map[string]int64),
	}
}

// 启动NATS机器人服务
func (nbs *NATSBotService) Start() error {
	// 连接NATS
	if err := nbs.connectNATS(); err != nil {
		return fmt.Errorf("连接NATS失败: %w", err)
	}

	// 订阅Matrix事件
	if err := nbs.subscribeMatrixEvents(); err != nil {
		return fmt.Errorf("订阅Matrix事件失败: %w", err)
	}

	// 启动HTTP管理接口
	go nbs.startHTTPServer()

	log.Printf("🚀 NATS Matrix Bot服务已启动")
	log.Printf("📡 NATS URL: %s", nbs.natsConfig.URL)
	log.Printf("📢 订阅主题: %s", nbs.natsConfig.Subject)
	log.Printf("🌐 管理界面: http://localhost%s", nbs.config.ListenPort)

	// 保持服务运行
	select {}
}

// 连接NATS服务器
func (nbs *NATSBotService) connectNATS() error {
	var opts []nats.Option

	// 添加认证信息
	if nbs.natsConfig.Username != "" && nbs.natsConfig.Password != "" {
		opts = append(opts, nats.UserInfo(nbs.natsConfig.Username, nbs.natsConfig.Password))
	}

	// 添加重连配置
	opts = append(opts,
		nats.ReconnectWait(time.Second*2),
		nats.MaxReconnects(-1),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			log.Printf("⚠️ NATS连接断开: %v", err)
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			log.Printf("✅ NATS重新连接成功")
		}),
	)

	nc, err := nats.Connect(nbs.natsConfig.URL, opts...)
	if err != nil {
		return err
	}

	nbs.natsConn = nc
	log.Printf("✅ NATS连接成功: %s", nbs.natsConfig.URL)
	return nil
}

// 订阅Matrix事件
func (nbs *NATSBotService) subscribeMatrixEvents() error {
	// 订阅所有Matrix事件
	_, err := nbs.natsConn.Subscribe(nbs.natsConfig.Subject, nbs.handleNATSMessage)
	if err != nil {
		return err
	}

	// 订阅特定类型的事件
	subjects := []string{
		nbs.natsConfig.Subject + ".room.message",
		nbs.natsConfig.Subject + ".room.member",
		nbs.natsConfig.Subject + ".room.create",
	}

	for _, subject := range subjects {
		_, err := nbs.natsConn.Subscribe(subject, nbs.handleSpecificEvent)
		if err != nil {
			log.Printf("⚠️ 订阅主题失败 %s: %v", subject, err)
		} else {
			log.Printf("📡 已订阅主题: %s", subject)
		}
	}

	return nil
}

// 处理NATS消息
func (nbs *NATSBotService) handleNATSMessage(msg *nats.Msg) {
	var event NATSMatrixEvent
	if err := json.Unmarshal(msg.Data, &event); err != nil {
		log.Printf("❌ 解析NATS消息失败: %v", err)
		return
	}

	log.Printf("📨 收到NATS事件: %s 在房间 %s 来自 %s", event.Type, event.RoomID, event.Sender)

	// 转换为标准Matrix事件格式
	matrixEvent := MatrixEvent{
		Type:      event.Type,
		EventID:   event.EventID,
		RoomID:    event.RoomID,
		Sender:    event.Sender,
		Content:   event.Content,
		Timestamp: event.Timestamp,
	}

	// 处理事件
	nbs.handleMatrixEvent(matrixEvent)

	// 发送ACK确认
	if err := msg.Respond([]byte("ACK")); err != nil {
		log.Printf("⚠️ 发送ACK失败: %v", err)
	}
}

// 处理特定类型的事件
func (nbs *NATSBotService) handleSpecificEvent(msg *nats.Msg) {
	log.Printf("🎯 收到特定事件: %s", msg.Subject)
	nbs.handleNATSMessage(msg)
}

// 处理Matrix事件
func (nbs *NATSBotService) handleMatrixEvent(event MatrixEvent) {
	// 更新用户活跃时间
	nbs.lastSeen[event.Sender] = event.Timestamp

	switch event.Type {
	case "m.room.message":
		nbs.handleMessage(event)
	case "m.room.member":
		nbs.handleMembership(event)
	case "m.room.create":
		nbs.handleRoomCreate(event)
	default:
		log.Printf("📝 忽略事件类型: %s", event.Type)
	}
}

// 处理消息事件
func (nbs *NATSBotService) handleMessage(event MatrixEvent) {
	// 忽略机器人自己的消息
	if nbs.isBotUser(event.Sender) {
		return
	}

	content, ok := event.Content["body"].(string)
	if !ok {
		return
	}

	log.Printf("💬 处理消息: %s (来自: %s)", content, event.Sender)

	// 选择合适的机器人
	selectedBot := nbs.botManager.SelectBotForMessage(content)
	if selectedBot == nil || !selectedBot.Active {
		log.Printf("⚠️ 没有可用的机器人处理消息")
		return
	}

	// 生成回复
	response := nbs.botManager.GenerateResponse(selectedBot, content, event.Sender)
	if response != "" {
		// 发送回复
		go nbs.sendReplyAsBot(selectedBot.UserID, event.RoomID, response)

		// 通过NATS发布回复事件
		go nbs.publishBotResponse(selectedBot.UserID, event.RoomID, response, event.EventID)
	}
}

// 发布机器人回复事件到NATS
func (nbs *NATSBotService) publishBotResponse(botUserID, roomID, message, replyToEventID string) {
	responseEvent := map[string]interface{}{
		"type":        "bot.response",
		"bot_user_id": botUserID,
		"room_id":     roomID,
		"message":     message,
		"reply_to":    replyToEventID,
		"timestamp":   time.Now().Unix(),
	}

	data, err := json.Marshal(responseEvent)
	if err != nil {
		log.Printf("❌ 序列化机器人回复事件失败: %v", err)
		return
	}

	subject := nbs.natsConfig.Subject + ".bot.response"
	if err := nbs.natsConn.Publish(subject, data); err != nil {
		log.Printf("❌ 发布机器人回复事件失败: %v", err)
	} else {
		log.Printf("📤 已发布机器人回复事件到: %s", subject)
	}
}

// 检查是否是机器人用户
func (nbs *NATSBotService) isBotUser(userID string) bool {
	bots := nbs.botManager.GetAllBots()
	_, exists := bots[userID]
	return exists
}

// 发送回复消息
func (nbs *NATSBotService) sendReplyAsBot(botUserID, roomID, message string) {
	if err := nbs.SendMessageAsBot(botUserID, roomID, message); err != nil {
		log.Printf("❌ 机器人 %s 发送回复失败: %v", botUserID, err)
	} else {
		log.Printf("✅ 机器人 %s 已发送回复到房间 %s", botUserID, roomID)
	}
}

// 以指定机器人身份发送消息
func (nbs *NATSBotService) SendMessageAsBot(botUserID, roomID, message string) error {
	content := MessageContent{
		MsgType: "m.text",
		Body:    message,
	}

	txnID := fmt.Sprintf("%d", time.Now().UnixNano())
	url := fmt.Sprintf("%s/_matrix/client/r0/rooms/%s/send/m.room.message/%s?user_id=%s",
		nbs.config.DendriteURL, roomID, txnID, botUserID)

	payload, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+nbs.config.ASToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := nbs.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("发送消息失败: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// 处理成员变更
func (nbs *NATSBotService) handleMembership(event MatrixEvent) {
	membership, ok := event.Content["membership"].(string)
	if !ok {
		return
	}

	log.Printf("👥 成员变更: %s -> %s 在房间 %s", event.Sender, membership, event.RoomID)

	if membership == "join" {
		nbs.rooms[event.RoomID] = true
		nbs.users[event.Sender] = membership
	}
}

// 处理房间创建
func (nbs *NATSBotService) handleRoomCreate(event MatrixEvent) {
	log.Printf("🏠 房间创建: %s", event.RoomID)
	nbs.rooms[event.RoomID] = true
}

// 启动HTTP管理服务器
func (nbs *NATSBotService) startHTTPServer() {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()

	// API端点
	r.GET("/status", nbs.getStatus)
	r.GET("/nats-status", nbs.getNATSStatus)
	r.POST("/publish-test", nbs.publishTestEvent)
	r.GET("/bots", nbs.listBots)

	// Web界面
	r.GET("/", func(c *gin.Context) {
		c.Header("Content-Type", "text/html")
		c.String(200, getNATSWebInterface())
	})

	log.Printf("🌐 HTTP管理服务器启动在端口 %s", nbs.config.ListenPort)
	if err := r.Run(nbs.config.ListenPort); err != nil {
		log.Printf("❌ HTTP服务器启动失败: %v", err)
	}
}

// 获取服务状态
func (nbs *NATSBotService) getStatus(c *gin.Context) {
	status := gin.H{
		"service_name":   "NATS Matrix Bot Service",
		"nats_connected": nbs.natsConn.IsConnected(),
		"nats_url":       nbs.natsConfig.URL,
		"nats_subject":   nbs.natsConfig.Subject,
		"bots_count":     len(nbs.botManager.GetAllBots()),
		"active_bots":    len(nbs.botManager.GetActiveBots()),
		"rooms_count":    len(nbs.rooms),
		"users_count":    len(nbs.users),
		"uptime":         time.Now().Format("2006-01-02 15:04:05"),
	}
	c.JSON(200, status)
}

// 获取NATS状态
func (nbs *NATSBotService) getNATSStatus(c *gin.Context) {
	stats := nbs.natsConn.Stats()
	status := gin.H{
		"connected":       nbs.natsConn.IsConnected(),
		"reconnected":     nbs.natsConn.IsReconnecting(),
		"status":          nbs.natsConn.Status(),
		"servers":         nbs.natsConn.Servers(),
		"discovered_urls": nbs.natsConn.DiscoveredServers(),
		"stats": gin.H{
			"in_msgs":    stats.InMsgs,
			"out_msgs":   stats.OutMsgs,
			"in_bytes":   stats.InBytes,
			"out_bytes":  stats.OutBytes,
			"reconnects": stats.Reconnects,
		},
	}
	c.JSON(200, status)
}

// 发布测试事件
func (nbs *NATSBotService) publishTestEvent(c *gin.Context) {
	var req struct {
		Subject string      `json:"subject"`
		Data    interface{} `json:"data"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	data, err := json.Marshal(req.Data)
	if err != nil {
		c.JSON(500, gin.H{"error": "序列化数据失败"})
		return
	}

	if err := nbs.natsConn.Publish(req.Subject, data); err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "测试事件发布成功"})
}

// 列出机器人
func (nbs *NATSBotService) listBots(c *gin.Context) {
	bots := nbs.botManager.GetAllBots()
	c.JSON(200, gin.H{"bots": bots})
}

// 关闭服务
func (nbs *NATSBotService) Close() {
	if nbs.natsConn != nil {
		nbs.natsConn.Close()
		log.Printf("🔌 NATS连接已关闭")
	}
}

// NATS Web界面
func getNATSWebInterface() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NATS Matrix Bot 管理界面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
        .status-item { text-align: center; padding: 15px; background: #e3f2fd; border-radius: 6px; }
        .connected { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .disconnected { background: #ffebee; border-left: 4px solid #f44336; }
        .controls { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        input, textarea, button { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #2196f3; color: white; cursor: pointer; border: none; }
        button:hover { background: #1976d2; }
        .log { height: 300px; overflow-y: auto; background: #f8f8f8; padding: 10px; font-family: monospace; font-size: 12px; }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .nats-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }
        .stat-box { text-align: center; padding: 10px; background: #f0f0f0; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1 class="header">📡 NATS Matrix Bot 管理界面</h1>
        </div>

        <div class="card">
            <h2>📊 服务状态</h2>
            <div id="status" class="status-grid">加载中...</div>
        </div>

        <div class="card">
            <h2>📡 NATS连接状态</h2>
            <div id="nats-status">加载中...</div>
        </div>

        <div class="card">
            <h2>📈 NATS统计信息</h2>
            <div id="nats-stats" class="nats-stats">加载中...</div>
        </div>

        <div class="controls">
            <div class="card">
                <h2>🧪 发布测试事件</h2>
                <input type="text" id="test-subject" placeholder="NATS主题 (如: matrix.test)" value="matrix.test">
                <textarea id="test-data" placeholder="测试数据 (JSON格式)" rows="4">
{
  "type": "test.message",
  "content": "这是一个测试消息",
  "timestamp": "` + `${new Date().toISOString()}` + `"
}</textarea>
                <button onclick="publishTestEvent()">发布测试事件</button>
                <div id="publish-result"></div>
            </div>

            <div class="card">
                <h2>🤖 机器人状态</h2>
                <div id="bots-list">加载中...</div>
                <button onclick="refreshBots()">刷新机器人列表</button>
            </div>
        </div>

        <div class="card">
            <h2>📝 实时日志</h2>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="card">
            <h2>📚 NATS主题说明</h2>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                <h3>订阅的主题：</h3>
                <ul>
                    <li><code>matrix.events</code> - 所有Matrix事件</li>
                    <li><code>matrix.events.room.message</code> - 房间消息事件</li>
                    <li><code>matrix.events.room.member</code> - 成员变更事件</li>
                    <li><code>matrix.events.room.create</code> - 房间创建事件</li>
                </ul>
                <h3>发布的主题：</h3>
                <ul>
                    <li><code>matrix.events.bot.response</code> - 机器人回复事件</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('log');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logContainer.innerHTML += '<div class="' + className + '">[' + timestamp + '] ' + message + '</div>';
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        async function refreshStatus() {
            try {
                const response = await fetch('/status');
                const status = await response.json();

                document.getElementById('status').innerHTML =
                    '<div class="status-item"><h3>服务名称</h3><p>' + status.service_name + '</p></div>' +
                    '<div class="status-item"><h3>NATS连接</h3><p>' + (status.nats_connected ? '🟢 已连接' : '🔴 断开') + '</p></div>' +
                    '<div class="status-item"><h3>机器人总数</h3><p>' + status.bots_count + '</p></div>' +
                    '<div class="status-item"><h3>活跃机器人</h3><p>' + status.active_bots + '</p></div>' +
                    '<div class="status-item"><h3>已加入房间</h3><p>' + status.rooms_count + '</p></div>' +
                    '<div class="status-item"><h3>运行时间</h3><p>' + status.uptime + '</p></div>';
            } catch (error) {
                log('获取状态失败: ' + error.message, 'error');
            }
        }

        async function refreshNATSStatus() {
            try {
                const response = await fetch('/nats-status');
                const natsStatus = await response.json();

                const statusClass = natsStatus.connected ? 'connected' : 'disconnected';
                const statusText = natsStatus.connected ? '🟢 已连接' : '🔴 断开连接';

                document.getElementById('nats-status').innerHTML =
                    '<div class="' + statusClass + '" style="padding: 15px; border-radius: 6px;">' +
                    '<h3>连接状态: ' + statusText + '</h3>' +
                    '<p><strong>状态:</strong> ' + natsStatus.status + '</p>' +
                    '<p><strong>服务器:</strong> ' + JSON.stringify(natsStatus.servers) + '</p>' +
                    '<p><strong>重连状态:</strong> ' + (natsStatus.reconnected ? '是' : '否') + '</p>' +
                    '</div>';

                // 更新统计信息
                if (natsStatus.stats) {
                    document.getElementById('nats-stats').innerHTML =
                        '<div class="stat-box"><h4>接收消息</h4><p>' + natsStatus.stats.in_msgs + '</p></div>' +
                        '<div class="stat-box"><h4>发送消息</h4><p>' + natsStatus.stats.out_msgs + '</p></div>' +
                        '<div class="stat-box"><h4>接收字节</h4><p>' + formatBytes(natsStatus.stats.in_bytes) + '</p></div>' +
                        '<div class="stat-box"><h4>发送字节</h4><p>' + formatBytes(natsStatus.stats.out_bytes) + '</p></div>' +
                        '<div class="stat-box"><h4>重连次数</h4><p>' + natsStatus.stats.reconnects + '</p></div>';
                }
            } catch (error) {
                log('获取NATS状态失败: ' + error.message, 'error');
            }
        }

        async function refreshBots() {
            try {
                const response = await fetch('/bots');
                const data = await response.json();
                const botsList = document.getElementById('bots-list');

                if (data.bots && Object.keys(data.bots).length > 0) {
                    let html = '';
                    Object.values(data.bots).forEach(bot => {
                        const statusIcon = bot.active ? '🟢' : '🔴';
                        const statusText = bot.active ? '活跃' : '停用';
                        html += '<div style="padding: 10px; border: 1px solid #ddd; margin: 5px 0; border-radius: 4px;">' +
                               '<strong>' + bot.name + '</strong> (' + bot.user_id + ')<br>' +
                               '<small>类型: ' + bot.type + ' | 状态: ' + statusIcon + ' ' + statusText + '</small>' +
                               '</div>';
                    });
                    botsList.innerHTML = html;
                } else {
                    botsList.innerHTML = '<div style="color: #666;">暂无机器人数据</div>';
                }
            } catch (error) {
                log('获取机器人列表失败: ' + error.message, 'error');
            }
        }

        async function publishTestEvent() {
            const subject = document.getElementById('test-subject').value;
            const dataText = document.getElementById('test-data').value;

            if (!subject || !dataText) {
                log('请填写主题和数据', 'error');
                return;
            }

            try {
                const data = JSON.parse(dataText);

                const response = await fetch('/publish-test', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        subject: subject,
                        data: data
                    })
                });

                const result = await response.json();
                if (response.ok) {
                    log('测试事件发布成功: ' + subject, 'success');
                    document.getElementById('publish-result').innerHTML = '<span class="success">✅ 发布成功</span>';
                } else {
                    log('发布失败: ' + result.error, 'error');
                    document.getElementById('publish-result').innerHTML = '<span class="error">❌ ' + result.error + '</span>';
                }
            } catch (error) {
                log('发布测试事件失败: ' + error.message, 'error');
                document.getElementById('publish-result').innerHTML = '<span class="error">❌ JSON格式错误或网络错误</span>';
            }
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 初始化和定时刷新
        document.addEventListener('DOMContentLoaded', function() {
            log('NATS Matrix Bot 管理界面已加载', 'success');
            refreshStatus();
            refreshNATSStatus();
            refreshBots();

            // 每10秒刷新一次状态
            setInterval(() => {
                refreshStatus();
                refreshNATSStatus();
            }, 10000);

            // 每30秒刷新一次机器人列表
            setInterval(refreshBots, 30000);
        });
    </script>
</body>
</html>`
}

// main函数 - NATS版本
func main() {
	// Bot配置
	config := &BotConfig{
		ASToken:     "demo_as_token_replace_with_real_token",
		HSToken:     "demo_hs_token_replace_with_real_token",
		BotUserID:   "@demo-bot:localhost",
		DendriteURL: "http://localhost:8008",
		ListenPort:  ":8080",
		ServerName:  "localhost",
	}

	// NATS配置
	natsConfig := &NATSConfig{
		URL:     "nats://localhost:4222",
		Subject: "matrix.events",
		// Username: "matrix_user",  // 可选
		// Password: "matrix_pass",  // 可选
	}

	log.Println("🚀 启动NATS Matrix Bot Demo...")
	log.Printf("📡 NATS服务器: %s", natsConfig.URL)
	log.Printf("📢 订阅主题: %s", natsConfig.Subject)

	// 创建并启动NATS机器人服务
	natsBot := NewNATSBotService(config, natsConfig)
	if err := natsBot.Start(); err != nil {
		log.Fatal("启动NATS机器人服务失败:", err)
	}
}
