# Matrix Bot Demo

这是一个完整的Matrix Application Service机器人示例，展示如何创建、配置和操作Matrix机器人。

## 🚀 功能特性

- ✅ **完整的AS机器人实现** - 使用Gin框架构建
- ✅ **Web管理界面** - 直观的机器人管理控制台
- ✅ **消息处理** - 智能回复和命令处理
- ✅ **房间管理** - 自动加入房间和成员管理
- ✅ **实时监控** - 状态监控和日志记录
- ✅ **RESTful API** - 完整的管理API接口

## 📋 前置要求

1. **Go 1.21+** - 用于编译和运行Bot
2. **Dendrite服务器** - 运行在 `http://localhost:8008`
3. **Matrix客户端** - 用于测试（如Element）

## 🛠️ 安装和配置

### 1. 生成安全令牌

```bash
# 生成AS Token和HS Token
AS_TOKEN=$(openssl rand -hex 32)
HS_TOKEN=$(openssl rand -hex 32)

echo "AS Token: $AS_TOKEN"
echo "HS Token: $HS_TOKEN"
```

### 2. 配置Application Service

编辑 `appservice-registration.yaml`：

```yaml
# 替换为实际生成的令牌
as_token: "your_generated_as_token_here"
hs_token: "your_generated_hs_token_here"

# 确保URL指向Bot服务
url: http://localhost:8080
```

### 3. 配置Dendrite

在 `dendrite.yaml` 中添加：

```yaml
app_service_api:
  config_files:
    - /path/to/appservice-registration.yaml
```

### 4. 更新Bot配置

编辑 `main.go` 中的配置：

```go
config := &BotConfig{
    ASToken:     "your_generated_as_token_here",
    HSToken:     "your_generated_hs_token_here", 
    BotUserID:   "@demo-bot:localhost",
    DendriteURL: "http://localhost:8008",
    ListenPort:  ":8080",
    ServerName:  "localhost",
}
```

## 🏃‍♂️ 运行

### 1. 启动Dendrite服务器

```bash
cd /path/to/dendrite
./bin/dendrite --config dendrite.yaml
```

### 2. 启动Bot服务

```bash
cd demo-bot
go mod tidy
go run main.go
```

### 3. 访问管理界面

打开浏览器访问: http://localhost:8080

## 🎮 使用指南

### Web管理界面

1. **状态监控** - 查看机器人运行状态
2. **发送消息** - 手动发送消息到指定房间
3. **加入房间** - 让机器人加入新房间
4. **房间列表** - 查看已加入的房间
5. **用户列表** - 查看活跃用户
6. **操作日志** - 实时查看操作记录

### 聊天命令

在Matrix客户端中向机器人发送以下命令：

- `hello` / `hi` - 打招呼
- `help` - 显示帮助信息
- `time` - 显示当前时间
- `status` - 显示机器人状态
- `joke` - 讲个笑话

### API接口

#### 获取状态
```bash
curl http://localhost:8080/status
```

#### 发送消息
```bash
curl -X POST http://localhost:8080/send-message \
  -H "Content-Type: application/json" \
  -d '{"room_id": "!roomid:localhost", "message": "Hello World!"}'
```

#### 加入房间
```bash
curl -X POST http://localhost:8080/join-room \
  -H "Content-Type: application/json" \
  -d '{"room_id": "!roomid:localhost"}'
```

## 🧪 测试步骤

### 1. 创建测试用户

使用Matrix客户端注册一个测试用户。

### 2. 创建房间

在Matrix客户端中创建一个新房间。

### 3. 邀请机器人

在房间中邀请机器人用户 `@demo-bot:localhost`。

### 4. 测试交互

发送消息 "hello" 给机器人，应该收到回复。

## 🔧 自定义开发

### 添加新命令

在 `generateResponse` 函数中添加新的命令处理：

```go
case strings.Contains(message, "weather"):
    return "今天天气不错！☀️"
```

### 添加新API

在 `Start` 函数中添加新的路由：

```go
r.GET("/custom-api", func(c *gin.Context) {
    c.JSON(200, gin.H{"message": "Custom API"})
})
```

### 数据持久化

当前使用内存存储，可以集成数据库：

```go
// 替换内存存储
type BotService struct {
    config *BotConfig
    db     *sql.DB  // 添加数据库连接
    // ...
}
```

## 🐛 故障排除

### 常见问题

1. **机器人无法接收消息**
   - 检查AS Token和HS Token是否正确
   - 确认Dendrite配置中包含了AS配置文件
   - 检查Bot服务是否在正确端口运行

2. **无法发送消息**
   - 确认机器人已加入目标房间
   - 检查房间ID格式是否正确
   - 验证AS Token权限

3. **Web界面无法访问**
   - 确认Bot服务正在运行
   - 检查端口是否被占用
   - 查看防火墙设置

### 日志调试

Bot会输出详细的日志信息，包括：
- 接收到的Matrix事件
- 发送的消息
- API调用结果
- 错误信息

## 📚 扩展资源

- [Matrix Specification](https://matrix.org/docs/spec/)
- [Application Service API](https://matrix.org/docs/spec/application_service/r0.1.2)
- [Dendrite Documentation](https://github.com/matrix-org/dendrite)
- [Gin Framework](https://gin-gonic.com/)

## 📄 许可证

MIT License - 详见LICENSE文件
