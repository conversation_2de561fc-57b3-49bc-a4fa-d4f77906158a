// 高级多机器人AS服务示例
// 展示如何在一个AS中管理和使用多个机器人
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 高级机器人服务
type AdvancedBotService struct {
	config      *BotConfig
	httpClient  *http.Client
	botManager  *MultiBotManager
	rooms       map[string]bool
	users       map[string]string
	lastSeen    map[string]int64
}

// 创建高级机器人服务
func NewAdvancedBotService(config *BotConfig) *AdvancedBotService {
	botManager := NewMultiBotManager(config.BotUserID, config.ASToken, config.DendriteURL)
	
	return &AdvancedBotService{
		config:     config,
		httpClient: &http.Client{Timeout: 30 * time.Second},
		botManager: botManager,
		rooms:      make(map[string]bool),
		users:      make(map[string]string),
		lastSeen:   make(map[string]int64),
	}
}

// 启动高级机器人服务
func (abs *AdvancedBotService) Start() {
	gin.SetMode(gin.ReleaseMode)
	r := gin.Default()

	// CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// Matrix AS端点
	r.PUT("/transactions/:txnId", abs.handleTransaction)
	r.PUT("/_matrix/app/v1/transactions/:txnId", abs.handleTransaction)

	// 管理API
	r.GET("/status", abs.getStatus)
	r.GET("/bots", abs.listBots)
	r.POST("/bots/:botId/toggle", abs.toggleBot)
	r.POST("/send-as-bot", abs.sendAsBot)
	r.POST("/create-bot", abs.createBot)

	// Web界面
	r.GET("/", func(c *gin.Context) {
		c.Header("Content-Type", "text/html")
		c.String(200, getAdvancedWebInterface())
	})

	log.Printf("🚀 高级多机器人AS服务启动在端口 %s", abs.config.ListenPort)
	log.Printf("📊 管理界面: http://localhost%s", abs.config.ListenPort)
	log.Printf("🤖 管理 %d 个机器人", len(abs.botManager.GetAllBots()))

	if err := r.Run(abs.config.ListenPort); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}

// 处理Matrix事务
func (abs *AdvancedBotService) handleTransaction(c *gin.Context) {
	txnID := c.Param("txnId")
	
	// 验证HS令牌
	authHeader := c.GetHeader("Authorization")
	if !strings.HasPrefix(authHeader, "Bearer ") || 
	   strings.TrimPrefix(authHeader, "Bearer ") != abs.config.HSToken {
		c.JSON(401, gin.H{"error": "Invalid homeserver token"})
		return
	}

	var transaction MatrixTransaction
	if err := c.ShouldBindJSON(&transaction); err != nil {
		c.JSON(400, gin.H{"error": "Invalid transaction format"})
		return
	}

	log.Printf("📨 收到事务 %s，包含 %d 个事件", txnID, len(transaction.Events))

	// 处理每个事件
	for _, event := range transaction.Events {
		abs.handleEvent(event)
	}

	c.JSON(200, gin.H{})
}

// 处理Matrix事件
func (abs *AdvancedBotService) handleEvent(event MatrixEvent) {
	abs.lastSeen[event.Sender] = event.Timestamp

	switch event.Type {
	case "m.room.message":
		abs.handleMessage(event)
	case "m.room.member":
		abs.handleMembership(event)
	}
}

// 处理消息事件
func (abs *AdvancedBotService) handleMessage(event MatrixEvent) {
	// 忽略机器人自己的消息
	if abs.isBotUser(event.Sender) {
		return
	}

	content, ok := event.Content["body"].(string)
	if !ok {
		return
	}

	log.Printf("💬 收到消息: %s (来自: %s)", content, event.Sender)

	// 选择合适的机器人处理消息
	selectedBot := abs.botManager.SelectBotForMessage(content)
	if selectedBot == nil || !selectedBot.Active {
		log.Printf("⚠️ 没有可用的机器人处理消息")
		return
	}

	// 生成回复
	response := abs.botManager.GenerateResponse(selectedBot, content, event.Sender)
	if response != "" {
		// 使用选定的机器人发送回复
		go abs.sendReplyAsBot(selectedBot.UserID, event.RoomID, response)
	}
}

// 检查是否是机器人用户
func (abs *AdvancedBotService) isBotUser(userID string) bool {
	bots := abs.botManager.GetAllBots()
	_, exists := bots[userID]
	return exists
}

// 以指定机器人身份发送回复
func (abs *AdvancedBotService) sendReplyAsBot(botUserID, roomID, message string) {
	if err := abs.SendMessageAsBot(botUserID, roomID, message); err != nil {
		log.Printf("❌ 机器人 %s 发送回复失败: %v", botUserID, err)
	} else {
		log.Printf("✅ 机器人 %s 已发送回复到房间 %s", botUserID, roomID)
	}
}

// 以指定机器人身份发送消息
func (abs *AdvancedBotService) SendMessageAsBot(botUserID, roomID, message string) error {
	content := MessageContent{
		MsgType: "m.text",
		Body:    message,
	}

	txnID := fmt.Sprintf("%d", time.Now().UnixNano())
	url := fmt.Sprintf("%s/_matrix/client/r0/rooms/%s/send/m.room.message/%s?user_id=%s",
		abs.config.DendriteURL, roomID, txnID, botUserID)

	payload, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	req, err := http.NewRequest("PUT", url, bytes.NewBuffer(payload))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+abs.config.ASToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := abs.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("发送消息失败: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// 处理成员变更
func (abs *AdvancedBotService) handleMembership(event MatrixEvent) {
	membership, ok := event.Content["membership"].(string)
	if !ok {
		return
	}

	if membership == "join" {
		abs.rooms[event.RoomID] = true
		abs.users[event.Sender] = membership
	}
}

// API处理函数

// 获取状态
func (abs *AdvancedBotService) getStatus(c *gin.Context) {
	allBots := abs.botManager.GetAllBots()
	activeBots := abs.botManager.GetActiveBots()

	status := gin.H{
		"service_name":   "Advanced Multi-Bot AS",
		"main_bot_id":    abs.config.BotUserID,
		"dendrite_url":   abs.config.DendriteURL,
		"total_bots":     len(allBots),
		"active_bots":    len(activeBots),
		"rooms_count":    len(abs.rooms),
		"users_count":    len(abs.users),
		"uptime":         time.Now().Format("2006-01-02 15:04:05"),
	}
	c.JSON(200, status)
}

// 列出所有机器人
func (abs *AdvancedBotService) listBots(c *gin.Context) {
	bots := abs.botManager.GetAllBots()
	c.JSON(200, gin.H{"bots": bots})
}

// 切换机器人状态
func (abs *AdvancedBotService) toggleBot(c *gin.Context) {
	botID := c.Param("botId")
	
	var req struct {
		Active bool `json:"active"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	if abs.botManager.SetBotActive(botID, req.Active) {
		action := "激活"
		if !req.Active {
			action = "停用"
		}
		c.JSON(200, gin.H{"success": true, "message": fmt.Sprintf("机器人 %s 已%s", botID, action)})
	} else {
		c.JSON(404, gin.H{"error": "机器人不存在"})
	}
}

// 以指定机器人身份发送消息
func (abs *AdvancedBotService) sendAsBot(c *gin.Context) {
	var req struct {
		BotUserID string `json:"bot_user_id" binding:"required"`
		RoomID    string `json:"room_id" binding:"required"`
		Message   string `json:"message" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	if err := abs.SendMessageAsBot(req.BotUserID, req.RoomID, req.Message); err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "消息发送成功"})
}

// 创建新机器人（注册到AS命名空间）
func (abs *AdvancedBotService) createBot(c *gin.Context) {
	var req struct {
		UserID      string `json:"user_id" binding:"required"`
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Type        string `json:"type"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": err.Error()})
		return
	}

	// 这里可以添加实际的用户创建逻辑
	// 目前只是添加到本地管理器
	
	c.JSON(200, gin.H{
		"success": true, 
		"message": fmt.Sprintf("机器人 %s 创建成功", req.UserID),
		"note": "实际创建需要调用Matrix注册API",
	})
}

// 高级Web界面
func getAdvancedWebInterface() string {
	return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级多机器人AS管理界面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; color: #333; }
        .bot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px; }
        .bot-card { border: 1px solid #ddd; padding: 15px; border-radius: 6px; }
        .bot-active { border-color: #4caf50; background: #f1f8e9; }
        .bot-inactive { border-color: #f44336; background: #ffebee; }
        .controls { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        input, textarea, select, button { width: 100%; padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #2196f3; color: white; cursor: pointer; border: none; }
        button:hover { background: #1976d2; }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .toggle-btn { width: auto; padding: 5px 10px; margin: 0 5px; }
        .active-btn { background: #4caf50; }
        .inactive-btn { background: #f44336; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1 class="header">🤖 高级多机器人AS管理界面</h1>
        </div>

        <div class="card">
            <h2>📊 服务状态</h2>
            <div id="status">加载中...</div>
        </div>

        <div class="card">
            <h2>🤖 机器人管理</h2>
            <div id="bots-grid" class="bot-grid">加载中...</div>
        </div>

        <div class="controls">
            <div class="card">
                <h2>💬 指定机器人发送消息</h2>
                <select id="bot-select">
                    <option value="">选择机器人...</option>
                </select>
                <input type="text" id="target-room" placeholder="目标房间ID">
                <textarea id="bot-message" placeholder="消息内容..." rows="3"></textarea>
                <button onclick="sendAsBot()">发送消息</button>
                <div id="send-result"></div>
            </div>

            <div class="card">
                <h2>➕ 创建新机器人</h2>
                <input type="text" id="new-bot-id" placeholder="机器人用户ID (如: @new-bot:localhost)">
                <input type="text" id="new-bot-name" placeholder="机器人名称">
                <input type="text" id="new-bot-desc" placeholder="机器人描述">
                <select id="new-bot-type">
                    <option value="ai">AI助手</option>
                    <option value="tool">工具机器人</option>
                    <option value="game">游戏机器人</option>
                    <option value="notify">通知机器人</option>
                </select>
                <button onclick="createBot()">创建机器人</button>
                <div id="create-result"></div>
            </div>
        </div>
    </div>

    <script>
        async function refreshStatus() {
            try {
                const response = await fetch('/status');
                const status = await response.json();
                document.getElementById('status').innerHTML = 
                    '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">' +
                    '<div><strong>总机器人数:</strong> ' + status.total_bots + '</div>' +
                    '<div><strong>活跃机器人:</strong> ' + status.active_bots + '</div>' +
                    '<div><strong>已加入房间:</strong> ' + status.rooms_count + '</div>' +
                    '<div><strong>运行时间:</strong> ' + status.uptime + '</div>' +
                    '</div>';
            } catch (error) {
                console.error('获取状态失败:', error);
            }
        }

        async function refreshBots() {
            try {
                const response = await fetch('/bots');
                const data = await response.json();
                const botsGrid = document.getElementById('bots-grid');
                const botSelect = document.getElementById('bot-select');
                
                botsGrid.innerHTML = '';
                botSelect.innerHTML = '<option value="">选择机器人...</option>';
                
                Object.values(data.bots).forEach(bot => {
                    // 机器人卡片
                    const botCard = document.createElement('div');
                    botCard.className = 'bot-card ' + (bot.active ? 'bot-active' : 'bot-inactive');
                    botCard.innerHTML = 
                        '<h3>' + bot.name + '</h3>' +
                        '<p><strong>ID:</strong> ' + bot.user_id + '</p>' +
                        '<p><strong>类型:</strong> ' + bot.type + '</p>' +
                        '<p><strong>描述:</strong> ' + bot.description + '</p>' +
                        '<p><strong>状态:</strong> ' + (bot.active ? '🟢 活跃' : '🔴 停用') + '</p>' +
                        '<button class="toggle-btn ' + (bot.active ? 'active-btn' : 'inactive-btn') + '" ' +
                        'onclick="toggleBot(\'' + bot.user_id + '\', ' + !bot.active + ')">' +
                        (bot.active ? '停用' : '激活') + '</button>';
                    botsGrid.appendChild(botCard);
                    
                    // 添加到选择框
                    if (bot.active) {
                        const option = document.createElement('option');
                        option.value = bot.user_id;
                        option.textContent = bot.name + ' (' + bot.user_id + ')';
                        botSelect.appendChild(option);
                    }
                });
            } catch (error) {
                console.error('获取机器人列表失败:', error);
            }
        }

        async function toggleBot(botId, active) {
            try {
                const response = await fetch('/bots/' + encodeURIComponent(botId) + '/toggle', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ active: active })
                });
                
                if (response.ok) {
                    refreshBots();
                } else {
                    alert('切换机器人状态失败');
                }
            } catch (error) {
                console.error('切换机器人状态失败:', error);
            }
        }

        async function sendAsBot() {
            const botId = document.getElementById('bot-select').value;
            const roomId = document.getElementById('target-room').value;
            const message = document.getElementById('bot-message').value;
            
            if (!botId || !roomId || !message) {
                alert('请填写所有字段');
                return;
            }

            try {
                const response = await fetch('/send-as-bot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        bot_user_id: botId,
                        room_id: roomId,
                        message: message
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    document.getElementById('send-result').innerHTML = '<span class="success">✅ 发送成功</span>';
                    document.getElementById('bot-message').value = '';
                } else {
                    document.getElementById('send-result').innerHTML = '<span class="error">❌ ' + result.error + '</span>';
                }
            } catch (error) {
                document.getElementById('send-result').innerHTML = '<span class="error">❌ 网络错误</span>';
            }
        }

        async function createBot() {
            const userId = document.getElementById('new-bot-id').value;
            const name = document.getElementById('new-bot-name').value;
            const description = document.getElementById('new-bot-desc').value;
            const type = document.getElementById('new-bot-type').value;
            
            if (!userId || !name) {
                alert('请填写用户ID和名称');
                return;
            }

            try {
                const response = await fetch('/create-bot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: userId,
                        name: name,
                        description: description,
                        type: type
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    document.getElementById('create-result').innerHTML = '<span class="success">✅ ' + result.message + '</span>';
                    // 清空表单
                    document.getElementById('new-bot-id').value = '';
                    document.getElementById('new-bot-name').value = '';
                    document.getElementById('new-bot-desc').value = '';
                } else {
                    document.getElementById('create-result').innerHTML = '<span class="error">❌ ' + result.error + '</span>';
                }
            } catch (error) {
                document.getElementById('create-result').innerHTML = '<span class="error">❌ 网络错误</span>';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
            refreshBots();
            
            // 每30秒刷新一次
            setInterval(() => {
                refreshStatus();
                refreshBots();
            }, 30000);
        });
    </script>
</body>
</html>`;
}
