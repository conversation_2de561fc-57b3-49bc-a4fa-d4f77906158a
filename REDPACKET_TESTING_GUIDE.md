# 红包功能测试指南

## 🔐 接口鉴权说明

**是的，所有红包接口都有完整的Matrix鉴权机制！**

在 [`redpacketapi/routing/routing.go`](redpacketapi/routing/routing.go) 中，所有接口都使用了 `httputil.MakeAuthAPI`：

```go
// 发送红包 - 需要认证
publicAPIMux.Handle("/rooms/{roomID}/redpacket",
    httputil.MakeAuthAPI("send_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
        return SendRedPacket(req, device, redpacketAPI)
    }),
).Methods(http.MethodPost, http.MethodOptions)

// 抢红包 - 需要认证  
publicAPIMux.Handle("/redpacket/{eventID}/grab",
    httputil.MakeAuthAPI("grab_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
        return GrabRedPacket(req, device, redpacketAPI)
    }),
).Methods(http.MethodPost, http.MethodOptions)

// 查询红包 - 需要认证
publicAPIMux.Handle("/redpacket/{eventID}",
    httputil.MakeAuthAPI("get_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
        return GetRedPacket(req, device, redpacketAPI)
    }),
).Methods(http.MethodGet, http.MethodOptions)
```

## 🚀 完整测试流程

### 步骤1: 启动Dendrite服务

1. **配置红包服务**
   ```yaml
   # 在 dendrite.yaml 中确保红包服务已启用
   redpacket:
     enabled: true
     max_amount: 200.0
     max_count: 100
     default_expires_in: 86400
     min_amount: 0.01
     allow_lucky_redpacket: true
     database:
       connection_string: "**************************************************************************"
   ```

2. **启动服务**
   ```bash
   ./bin/dendrite --config dendrite.yaml
   ```

### 步骤2: 创建测试用户和房间

1. **注册测试用户**
   ```bash
   # 注册用户1 (发红包的用户)
   curl -X POST "http://localhost:8008/_matrix/client/r0/register" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "redpacket_sender",
       "password": "test123456",
       "auth": {"type": "m.login.dummy"}
     }'
   
   # 注册用户2 (抢红包的用户)
   curl -X POST "http://localhost:8008/_matrix/client/r0/register" \
     -H "Content-Type: application/json" \
     -d '{
       "username": "redpacket_receiver",
       "password": "test123456", 
       "auth": {"type": "m.login.dummy"}
     }'
   ```

2. **登录获取访问令牌**
   ```bash
   # 用户1登录
   curl -X POST "http://localhost:8008/_matrix/client/r0/login" \
     -H "Content-Type: application/json" \
     -d '{
       "type": "m.login.password",
       "user": "redpacket_sender",
       "password": "test123456"
     }'
   
   # 保存返回的 access_token，例如: syt_cmVkcGFja2V0X3NlbmRlcg_...
   
   # 用户2登录
   curl -X POST "http://localhost:8008/_matrix/client/r0/login" \
     -H "Content-Type: application/json" \
     -d '{
       "type": "m.login.password", 
       "user": "redpacket_receiver",
       "password": "test123456"
     }'
   ```

3. **创建测试房间**
   ```bash
   # 用户1创建房间
   curl -X POST "http://localhost:8008/_matrix/client/r0/createRoom" \
     -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "红包测试房间",
       "topic": "测试红包功能",
       "preset": "public_chat"
     }'
   
   # 保存返回的 room_id，例如: !abc123:localhost
   ```

4. **邀请用户2加入房间**
   ```bash
   # 邀请用户2
   curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/invite" \
     -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "user_id": "@redpacket_receiver:localhost"
     }'
   
   # 用户2接受邀请
   curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/join" \
     -H "Authorization: Bearer YOUR_RECEIVER_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{}'
   ```

### 步骤3: 测试红包功能

#### 3.1 发送普通红包

```bash
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/redpacket" \
  -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "normal",
    "total_amount": 10.0,
    "total_count": 5,
    "message": "测试普通红包",
    "expires_in": 3600
  }'
```

**预期响应:**
```json
{
  "event_id": "$abc123def456:localhost",
  "redpacket": {
    "id": "rp_abc123",
    "event_id": "$abc123def456:localhost", 
    "room_id": "!YOUR_ROOM_ID:localhost",
    "sender_id": "@redpacket_sender:localhost",
    "type": "normal",
    "total_amount": 10.0,
    "total_count": 5,
    "remaining_amount": 10.0,
    "remaining_count": 5,
    "message": "测试普通红包",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z",
    "expires_at": "2024-01-01T13:00:00Z"
  }
}
```

#### 3.2 发送拼手气红包

```bash
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/redpacket" \
  -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lucky",
    "total_amount": 20.0,
    "total_count": 3,
    "message": "拼手气红包来啦！",
    "expires_in": 7200
  }'
```

#### 3.3 抢红包

```bash
# 用户2抢红包
curl -X POST "http://localhost:8008/_matrix/client/r0/redpacket/\$YOUR_EVENT_ID:localhost/grab" \
  -H "Authorization: Bearer YOUR_RECEIVER_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

**预期响应:**
```json
{
  "success": true,
  "amount": 2.5,
  "message": "恭喜你抢到了 2.50 元！",
  "grab": {
    "id": "grab_xyz789",
    "redpacket_id": "rp_abc123",
    "user_id": "@redpacket_receiver:localhost",
    "amount": 2.5,
    "grabbed_at": "2024-01-01T12:01:00Z",
    "is_luckiest": false,
    "user_nickname": "redpacket_receiver"
  }
}
```

#### 3.4 查询红包信息

```bash
curl -X GET "http://localhost:8008/_matrix/client/r0/redpacket/\$YOUR_EVENT_ID:localhost" \
  -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN"
```

**预期响应:**
```json
{
  "redpacket": {
    "id": "rp_abc123",
    "event_id": "$abc123def456:localhost",
    "room_id": "!YOUR_ROOM_ID:localhost", 
    "sender_id": "@redpacket_sender:localhost",
    "type": "normal",
    "total_amount": 10.0,
    "total_count": 5,
    "remaining_amount": 7.5,
    "remaining_count": 4,
    "message": "测试普通红包",
    "status": "active",
    "created_at": "2024-01-01T12:00:00Z",
    "expires_at": "2024-01-01T13:00:00Z"
  },
  "grabs": [
    {
      "id": "grab_xyz789",
      "redpacket_id": "rp_abc123", 
      "user_id": "@redpacket_receiver:localhost",
      "amount": 2.5,
      "grabbed_at": "2024-01-01T12:01:00Z",
      "is_luckiest": false,
      "user_nickname": "redpacket_receiver"
    }
  ]
}
```

## 🧪 测试用例

### 正常功能测试

1. **✅ 发送普通红包** - 金额平均分配
2. **✅ 发送拼手气红包** - 金额随机分配
3. **✅ 抢红包成功** - 获得随机金额
4. **✅ 查询红包详情** - 显示完整信息
5. **✅ 手气最佳标记** - 拼手气红包最大金额标记

### 边界条件测试

1. **❌ 重复抢红包**
   ```bash
   # 同一用户再次抢红包应该失败
   curl -X POST "http://localhost:8008/_matrix/client/r0/redpacket/\$YOUR_EVENT_ID:localhost/grab" \
     -H "Authorization: Bearer YOUR_RECEIVER_ACCESS_TOKEN"
   ```
   预期: `{"error": "你已经抢过这个红包了"}`

2. **❌ 红包抢完**
   ```bash
   # 当红包被抢完后再抢应该失败
   ```
   预期: `{"error": "红包已经被抢完了"}`

3. **❌ 红包过期**
   ```bash
   # 过期红包无法抢取
   ```
   预期: `{"error": "红包已过期"}`

4. **❌ 无效参数**
   ```bash
   # 发送无效金额的红包
   curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/redpacket" \
     -H "Authorization: Bearer YOUR_SENDER_ACCESS_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "type": "normal",
       "total_amount": -1.0,
       "total_count": 5
     }'
   ```
   预期: `{"error": "total_amount must be positive"}`

### 权限测试

1. **❌ 无认证访问**
   ```bash
   # 不提供Authorization头
   curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/redpacket" \
     -H "Content-Type: application/json" \
     -d '{"type": "normal", "total_amount": 10.0, "total_count": 5}'
   ```
   预期: `401 Unauthorized`

2. **❌ 无效令牌**
   ```bash
   # 使用无效的access_token
   curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!YOUR_ROOM_ID:localhost/redpacket" \
     -H "Authorization: Bearer invalid_token" \
     -H "Content-Type: application/json" \
     -d '{"type": "normal", "total_amount": 10.0, "total_count": 5}'
   ```
   预期: `401 Unauthorized`

## 🛠️ 调试工具

### 查看数据库状态

```sql
-- 查看红包表
SELECT * FROM redpacketapi_redpackets ORDER BY created_at DESC;

-- 查看抢红包记录
SELECT * FROM redpacketapi_redpacket_grabs ORDER BY grabbed_at DESC;

-- 查看特定红包的抢取情况
SELECT rp.*, rg.user_id, rg.amount, rg.grabbed_at, rg.is_luckiest
FROM redpacketapi_redpackets rp
LEFT JOIN redpacketapi_redpacket_grabs rg ON rp.id = rg.redpacket_id
WHERE rp.event_id = '$YOUR_EVENT_ID:localhost';
```

### 查看服务日志

```bash
# 查看Dendrite日志中的红包相关信息
tail -f dendrite.log | grep -i redpacket
```

## 📝 测试脚本

创建一个自动化测试脚本 `test_redpacket.sh`：

```bash
#!/bin/bash

# 配置
DENDRITE_URL="http://localhost:8008"
SENDER_TOKEN="YOUR_SENDER_ACCESS_TOKEN"
RECEIVER_TOKEN="YOUR_RECEIVER_ACCESS_TOKEN"
ROOM_ID="!YOUR_ROOM_ID:localhost"

echo "🎉 开始测试红包功能..."

# 1. 发送红包
echo "📤 发送红包..."
RESPONSE=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/redpacket" \
  -H "Authorization: Bearer $SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lucky",
    "total_amount": 10.0,
    "total_count": 3,
    "message": "自动化测试红包"
  }')

EVENT_ID=$(echo $RESPONSE | jq -r '.event_id')
echo "✅ 红包发送成功，事件ID: $EVENT_ID"

# 2. 抢红包
echo "🎁 抢红包..."
GRAB_RESPONSE=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/redpacket/$EVENT_ID/grab" \
  -H "Authorization: Bearer $RECEIVER_TOKEN")

AMOUNT=$(echo $GRAB_RESPONSE | jq -r '.amount')
echo "✅ 抢红包成功，获得金额: $AMOUNT"

# 3. 查询红包
echo "🔍 查询红包信息..."
INFO_RESPONSE=$(curl -s -X GET "$DENDRITE_URL/_matrix/client/r0/redpacket/$EVENT_ID" \
  -H "Authorization: Bearer $SENDER_TOKEN")

REMAINING=$(echo $INFO_RESPONSE | jq -r '.redpacket.remaining_amount')
echo "✅ 红包剩余金额: $REMAINING"

echo "🎊 测试完成！"
```

## 🔧 常见问题排查

1. **数据库连接失败**
   - 检查PostgreSQL是否运行
   - 验证连接字符串是否正确
   - 确认数据库用户权限

2. **红包服务未启动**
   - 检查配置文件中 `redpacket.enabled: true`
   - 查看启动日志是否有错误

3. **认证失败**
   - 确认access_token有效且未过期
   - 检查用户是否在房间中

4. **接口404错误**
   - 确认Dendrite版本包含红包功能
   - 检查路由是否正确注册

通过以上完整的测试流程，你可以全面验证红包功能的各个方面！🚀