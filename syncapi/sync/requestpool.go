/*
Matrix 同步API请求处理池 - RequestPool

该文件实现了Matrix协议中/sync端点的主要逻辑，负责处理客户端的长轮询请求，
管理用户在线状态，并将服务器事件同步到客户端。

主要功能：
1. 处理HTTP长轮询连接
2. 管理用户最后活跃时间
3. 跟踪和发布用户在线状态
4. 协调不同事件流的同步
5. 处理增量同步和完整同步

架构说明：
- 使用sync.Map实现线程安全的用户状态存储
- 通过Notifier实现事件通知机制
- 支持Prometheus监控指标
- 使用独立的goroutine进行状态清理

关键数据结构：
- RequestPool: 主结构体，管理所有同步请求
- PresencePublisher/PresenceConsumer: 状态发布/消费接口
- SyncRequest: 封装单个同步请求的上下文

文件位置: syncapi/sync/requestpool.go
版本: v1.2.3
最后更新: 2024-06-06
*/
// Copyright 2024 New Vector Ltd.
// Copyright 2019, 2020 The Matrix.org Foundation C.I.C.
// Copyright 2017, 2018 New Vector Ltd
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package sync

import (
	"context"
	"database/sql"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/matrix-org/util"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"

	"github.com/element-hq/dendrite/internal/sqlutil"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/element-hq/dendrite/syncapi/internal"
	"github.com/element-hq/dendrite/syncapi/notifier"
	"github.com/element-hq/dendrite/syncapi/storage"
	"github.com/element-hq/dendrite/syncapi/streams"
	"github.com/element-hq/dendrite/syncapi/types"
	userapi "github.com/element-hq/dendrite/userapi/api"
)

/*
RequestPool 管理HTTP长轮询连接用于/sync请求
包含数据库连接、配置、用户API、房间API等依赖项
使用sync.Map记录最后活跃时间和用户在线状态
*/
type RequestPool struct {
	db       storage.Database                // 数据库接口
	cfg      *config.SyncAPI                 // 同步API配置
	userAPI  userapi.SyncUserAPI             // 用户API接口
	rsAPI    roomserverAPI.SyncRoomserverAPI // 房间服务API接口
	lastseen *sync.Map                       // 记录用户最后活跃时间
	presence *sync.Map                       // 记录用户在线状态
	streams  *streams.Streams                // 流处理器
	Notifier *notifier.Notifier              // 通知器
	producer PresencePublisher               // 状态发布者接口
	consumer PresenceConsumer                // 状态消费者接口
}

/*
PresencePublisher 定义状态发布接口
用于将用户状态变更发布到外部系统
*/
type PresencePublisher interface {
	SendPresence(userID string, presence types.Presence, statusMsg *string) error
}

/*
PresenceConsumer 定义状态消费接口
用于接收并处理用户状态变更
*/
type PresenceConsumer interface {
	EmitPresence(ctx context.Context, userID string, presence types.Presence, statusMsg *string, ts spec.Timestamp, fromSync bool)
}

/*
NewRequestPool 创建新的RequestPool实例
参数:
  - db: 数据库存储接口
  - cfg: 同步API配置
  - userAPI: 用户API接口
  - rsAPI: 房间服务API接口
  - streams: 流处理器
  - notifier: 通知器
  - producer: 状态发布者
  - consumer: 状态消费者
  - enableMetrics: 是否启用监控指标
*/
func NewRequestPool(
	db storage.Database, cfg *config.SyncAPI,
	userAPI userapi.SyncUserAPI,
	rsAPI roomserverAPI.SyncRoomserverAPI,
	streams *streams.Streams, notifier *notifier.Notifier,
	producer PresencePublisher, consumer PresenceConsumer, enableMetrics bool,
) *RequestPool {
	if enableMetrics {
		// 注册Prometheus监控指标
		prometheus.MustRegister(
			activeSyncRequests, waitingSyncRequests,
		)
	}
	rp := &RequestPool{
		db:       db,
		cfg:      cfg,
		userAPI:  userAPI,
		rsAPI:    rsAPI,
		lastseen: &sync.Map{},
		presence: &sync.Map{},
		streams:  streams,
		Notifier: notifier,
		producer: producer,
		consumer: consumer,
	}
	// 启动后台goroutine定期清理状态
	go rp.cleanLastSeen()
	go rp.cleanPresence(db, time.Minute*5)
	return rp
}

/*
cleanLastSeen 定期清理最后活跃时间记录
每分钟执行一次，清空lastseen map
*/
func (rp *RequestPool) cleanLastSeen() {
	for {
		rp.lastseen.Range(func(key interface{}, _ interface{}) bool {
			rp.lastseen.Delete(key)
			return true
		})
		time.Sleep(time.Minute)
	}
}

/*
cleanPresence 定期清理在线状态记录
参数:
  - db: 数据库存储接口
  - cleanupTime: 清理间隔时间
*/
func (rp *RequestPool) cleanPresence(db storage.Presence, cleanupTime time.Duration) {
	if !rp.cfg.Matrix.Presence.EnableOutbound {
		return
	}
	for {
		rp.presence.Range(func(key interface{}, v interface{}) bool {
			p := v.(types.PresenceInternal)
			if time.Since(p.LastActiveTS.Time()) > cleanupTime {
				rp.updatePresence(db, types.PresenceUnavailable.String(), p.UserID)
				rp.presence.Delete(key)
			}
			return true
		})
		time.Sleep(cleanupTime)
	}
}

/*
PresenceMap 跟踪用户最后活跃时间和状态

数据结构:
- mu: 互斥锁，保证线程安全
- seen: 嵌套map结构，记录每个用户每种状态最后活跃时间
  - 外层key: 用户ID
  - 内层key: 状态类型(online/unavailable/offline)
  - 值: 最后活跃时间戳

用途:
- 用于确定用户当前应该显示的状态
- 避免频繁的状态更新
- 提供状态超时机制
*/
type PresenceMap struct {
	mu   sync.Mutex                              // 保护seen map的互斥锁
	seen map[string]map[types.Presence]time.Time // 用户状态时间记录
}

// lastPresence 全局状态跟踪器实例
var lastPresence PresenceMap

/*
presenceTimeout 状态超时时间

定义:
- 在线状态在超时后自动变为不可用
- 不可用状态在超时后自动变为离线
- 客户端应在此时间内完成下一次同步以保持状态

取值原则:
- 足够长以确保客户端能完成下一次同步
- 足够短以提供准确的状态显示
- 当前设置为10秒，基于典型客户端同步间隔
*/
const presenceTimeout = time.Second * 10

/*
updatePresence 更新用户在线状态
参数:
  - db: 数据库存储接口
  - presence: 新的状态字符串
  - userID: 用户ID
*/
/*
updatePresence 更新用户在线状态

参数:
  - db: 数据库存储接口
  - presence: 新的状态字符串(online/unavailable/offline)
  - userID: 用户ID

功能:
- 调用updatePresenceInternal处理实际状态更新逻辑
- 设置checkAgain=true允许状态超时后自动检查更新

注意事项:
- 这是外部调用的入口方法
- 内部会处理状态超时和自动降级逻辑
*/
func (rp *RequestPool) updatePresence(db storage.Presence, presence string, userID string) {
	// allow checking back on presence to set offline if needed
	rp.updatePresenceInternal(db, presence, userID, true)
}

/*
updatePresenceInternal 内部方法，实际处理状态更新逻辑
参数:
  - db: 数据库存储接口
  - presence: 新的状态字符串
  - userID: 用户ID
  - checkAgain: 是否需要再次检查状态
*/
/*
updatePresenceInternal 内部方法，实际处理状态更新逻辑

参数:
  - db: 数据库存储接口
  - presence: 新的状态字符串(online/unavailable/offline)
  - userID: 用户ID
  - checkAgain: 是否需要再次检查状态(用于离线状态超时检查)

工作流程:
1. 检查是否启用了状态功能
2. 加锁保证线程安全
3. 处理空状态默认为online
4. 验证状态字符串有效性
5. 创建新的状态记录
6. 初始化状态跟踪map(如果需要)
7. 更新该状态最后活跃时间
8. 根据各状态最后活跃时间确定当前应显示的状态:
   - online优先(10秒内活跃)
   - 其次unavailable(10秒内活跃)
   - 最后offline(10秒内活跃且无其他设备在线)
9. 从数据库获取并设置状态消息
10. 存储新状态到内存
11. 通过producer发布状态变更
12. 通过consumer通知内部系统

注意事项:
- 使用互斥锁保证线程安全
- 状态优先级: online > unavailable > offline
- 离线状态会设置定时器再次检查确认
- 避免频繁发布相同状态
- 同步更新内部状态视图以保证/sync响应一致性
*/
func (rp *RequestPool) updatePresenceInternal(db storage.Presence, presence string, userID string, checkAgain bool) {
	if !rp.cfg.Matrix.Presence.EnableOutbound {
		return
	}

	// lock the map to this thread
	lastPresence.mu.Lock()
	defer lastPresence.mu.Unlock()

	if presence == "" {
		presence = types.PresenceOnline.String()
	}

	presenceID, ok := types.PresenceFromString(presence)
	if !ok { // this should almost never happen
		return
	}

	newPresence := types.PresenceInternal{
		Presence:     presenceID,
		UserID:       userID,
		LastActiveTS: spec.AsTimestamp(time.Now()),
	}

	// make sure that the map is defined correctly as needed
	if lastPresence.seen == nil {
		lastPresence.seen = make(map[string]map[types.Presence]time.Time)
	}
	if lastPresence.seen[userID] == nil {
		lastPresence.seen[userID] = make(map[types.Presence]time.Time)
	}

	now := time.Now()
	// update time for each presence
	lastPresence.seen[userID][presenceID] = now

	// Default to unknown presence
	presenceToSet := types.PresenceUnknown
	switch {
	case now.Sub(lastPresence.seen[userID][types.PresenceOnline]) < presenceTimeout:
		// online will always get priority
		presenceToSet = types.PresenceOnline
	case now.Sub(lastPresence.seen[userID][types.PresenceUnavailable]) < presenceTimeout:
		// idle gets secondary priority because your presence shouldnt be idle if you are on a different device
		// kinda copying discord presence
		presenceToSet = types.PresenceUnavailable
	case now.Sub(lastPresence.seen[userID][types.PresenceOffline]) < presenceTimeout:
		// only set offline status if there is no known online devices
		// clients may set offline to attempt to not alter the online status of the user
		presenceToSet = types.PresenceOffline

		if checkAgain {
			// after a timeout, check presence again to make sure it gets set as offline sooner or later
			time.AfterFunc(presenceTimeout, func() {
				rp.updatePresenceInternal(db, types.PresenceOffline.String(), userID, false)
			})
		}
	}

	// ensure we also send the current status_msg to federated servers and not nil
	dbPresence, err := db.GetPresences(context.Background(), []string{userID})
	if err != nil && err != sql.ErrNoRows {
		return
	}
	if len(dbPresence) > 0 && dbPresence[0] != nil {
		newPresence.ClientFields = dbPresence[0].ClientFields
	}
	newPresence.ClientFields.Presence = presenceToSet.String()

	defer rp.presence.Store(userID, newPresence)
	// avoid spamming presence updates when syncing
	existingPresence, ok := rp.presence.LoadOrStore(userID, newPresence)
	if ok {
		p := existingPresence.(types.PresenceInternal)
		if p.ClientFields.Presence == newPresence.ClientFields.Presence {
			return
		}
	}

	if err := rp.producer.SendPresence(userID, presenceToSet, newPresence.ClientFields.StatusMsg); err != nil {
		logrus.WithError(err).Error("Unable to publish presence message from sync")
		return
	}

	// now synchronously update our view of the world. It's critical we do this before calculating
	// the /sync response else we may not return presence: online immediately.
	rp.consumer.EmitPresence(
		context.Background(), userID, presenceToSet, newPresence.ClientFields.StatusMsg,
		spec.AsTimestamp(time.Now()), true,
	)

}

/*
updateLastSeen 更新用户最后活跃时间记录

工作流程:
1. 检查是否已记录过该设备，避免重复更新
2. 获取客户端真实IP地址(考虑代理头)
3. 构造最后活跃时间更新请求
4. 异步调用用户API更新最后活跃时间
5. 在本地缓存中记录更新时间

参数:
  - req: HTTP请求对象，包含:
  - RemoteAddr: 客户端IP地址
  - Headers: 可能包含真实IP头(X-Real-IP/X-Forwarded-For)
  - UserAgent: 用户代理字符串
  - device: 用户设备信息，包含:
  - UserID: 用户唯一标识
  - ID: 设备唯一标识

注意事项:
- 使用sync.Map实现线程安全
- 每分钟会通过cleanLastSeen清理缓存
- 异步调用用户API，不等待响应
- 考虑代理情况下的真实IP获取
- 每个设备每分钟最多更新一次
*/
func (rp *RequestPool) updateLastSeen(req *http.Request, device *userapi.Device) {
	if _, ok := rp.lastseen.LoadOrStore(device.UserID+device.ID, struct{}{}); ok {
		return
	}

	remoteAddr := req.RemoteAddr
	if rp.cfg.RealIPHeader != "" {
		if header := req.Header.Get(rp.cfg.RealIPHeader); header != "" {
			// TODO: Maybe this isn't great but it will satisfy both X-Real-IP
			// and X-Forwarded-For (which can be a list where the real client
			// address is the first listed address). Make more intelligent?
			addresses := strings.Split(header, ",")
			if ip := net.ParseIP(addresses[0]); ip != nil {
				remoteAddr = addresses[0]
			}
		}
	}

	lsreq := &userapi.PerformLastSeenUpdateRequest{
		UserID:     device.UserID,
		DeviceID:   device.ID,
		RemoteAddr: remoteAddr,
		UserAgent:  req.UserAgent(),
	}
	lsres := &userapi.PerformLastSeenUpdateResponse{}
	go rp.userAPI.PerformLastSeenUpdate(req.Context(), lsreq, lsres) // nolint:errcheck

	rp.lastseen.Store(device.UserID+device.ID, time.Now())
}

/*
activeSyncRequests Prometheus指标，记录当前活跃的同步请求数
*/
var activeSyncRequests = prometheus.NewGauge(
	prometheus.GaugeOpts{
		Namespace: "dendrite",
		Subsystem: "syncapi",
		Name:      "active_sync_requests",
		Help:      "The number of sync requests that are active right now",
	},
)

/*
waitingSyncRequests Prometheus指标，记录等待被通知器唤醒的同步请求数
*/
var waitingSyncRequests = prometheus.NewGauge(
	prometheus.GaugeOpts{
		Namespace: "dendrite",
		Subsystem: "syncapi",
		Name:      "waiting_sync_requests",
		Help:      "The number of sync requests that are waiting to be woken by a notifier",
	},
)

/*
OnIncomingSyncRequest 处理客户端/sync请求
必须在专用goroutine中调用，会阻塞直到响应就绪或超时

工作流程:
1. 解析请求参数并创建SyncRequest对象
2. 更新用户最后活跃时间和在线状态
3. 清理旧的发送到设备消息
4. 进入主循环:
  - 检查是否需要立即返回(初始同步/timeout=0/full_state=true)
  - 如果需要等待:
  - 设置定时器
  - 创建监听器等待通知
  - 处理超时或取消情况
  - 执行完整同步或增量同步:
  - 获取数据库快照
  - 处理各事件流(PDU、输入、回执等)
  - 返回包含新批次令牌的响应

参数:
  - req: HTTP请求对象，包含客户端请求信息
  - device: 用户设备信息，包含用户ID和设备ID

返回:
  - util.JSONResponse: JSON格式的响应，包含:
  - 事件列表
  - 设备列表变更
  - 新批次令牌
  - 其他同步数据

错误处理:
  - 无效的同步令牌: 返回400错误
  - 数据库错误: 返回500错误
  - 其他错误: 根据情况返回适当状态码
*/
func (rp *RequestPool) OnIncomingSyncRequest(req *http.Request, device *userapi.Device) util.JSONResponse {
	// Extract values from request

	syncReq, err := newSyncRequest(req, *device, rp.db)
	if err != nil {
		if err == types.ErrMalformedSyncToken {
			return util.JSONResponse{
				Code: http.StatusBadRequest,
				JSON: spec.InvalidParam(err.Error()),
			}
		}
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.Unknown(err.Error()),
		}
	}

	activeSyncRequests.Inc() // 同步请求计数器 监控 看看在哪里能看到 // 看起来是 Prometheus 的指标
	defer activeSyncRequests.Dec()

	rp.updateLastSeen(req, device)                                         // 记录活跃时间
	rp.updatePresence(rp.db, req.FormValue("set_presence"), device.UserID) // 跟新状态

	waitingSyncRequests.Inc() //  增加等待处理的同步请求计数器

	defer waitingSyncRequests.Dec()

	// Clean up old send-to-device messages from before this stream position.
	// This is needed to avoid sending the same message multiple times
	// - 清理早于当前流位置(stream position)的发送到设备消息

	// - 防止重复发送相同的消息给设备

	if err = rp.db.CleanSendToDeviceUpdates(syncReq.Context, syncReq.Device.UserID, syncReq.Device.ID, syncReq.Since.SendToDevicePosition); err != nil {
		syncReq.Log.WithError(err).Error("p.DB.CleanSendToDeviceUpdates failed")
	}

	// loop until we get some data
	// 等待数据
	// - 避免空响应(empty sync)

	// - 优化客户端轮询行为

	// - 减少不必要的数据传输

	// - 支持长轮询减少请求次数

	for {
		startTime := time.Now()
		currentPos := rp.Notifier.CurrentPosition() // 获取当前事件通知器(Notifier)的最新流位置
		// **
		// - 保存当前所有事件流的最新位置

		// - 通过读写锁(`sync.RWMutex`)保护并发访问

		// **

		// if the since token matches the current positions, wait via the notifier
		if !rp.shouldReturnImmediately(syncReq, currentPos) { // 这里就是异步
			timer := time.NewTimer(syncReq.Timeout) // case of timeout=0 is handled above // 计算超时时间  打点器
			defer timer.Stop()

			userStreamListener := rp.Notifier.GetListener(*syncReq) // 获取到用户流监听器 监听某一个时间
			defer userStreamListener.Close()

			giveup := func() util.JSONResponse { // 给一个预期预期错误
				syncReq.Log.Debugln("Responding to sync since client gave up or timeout was reached")
				syncReq.Response.NextBatch = syncReq.Since // 返回当前的流位置
				// We should always try to include OTKs in sync responses, otherwise clients might upload keys
				// even if that's not required. See also:
				// https://github.com/matrix-org/synapse/blob/29f06704b8871a44926f7c99e73cf4a978fb8e81/synapse/rest/client/sync.py#276-L281
				// Only try to get OTKs if the context isn't already done.
				if syncReq.Context.Err() == nil { // 例如超时关闭
					err = internal.DeviceOTKCounts(syncReq.Context, rp.userAPI, syncReq.Device.UserID, syncReq.Device.ID, syncReq.Response)
					if err != nil && err != context.Canceled {
						syncReq.Log.WithError(err).Warn("failed to get OTK counts")
					}
				}
				return util.JSONResponse{
					Code: http.StatusOK,
					JSON: syncReq.Response,
				}
			}

			select {
			case <-syncReq.Context.Done(): // Caller gave up
				return giveup() // 请求的超时关闭

			case <-timer.C: // Timeout reached
				return giveup() // 预定的超时器

			case <-userStreamListener.GetNotifyChannel(syncReq.Since): // 等待信息 阻塞主
				// 将另一个StreamingToken中的非零位置值合并到当前token中
				// 只更新比当前值更大的位置（确保位置单调递增）
				// 对9种不同类型的事件流位置分别处理

				currentPos.ApplyUpdates(userStreamListener.GetSyncPosition()) // 将新位置合并到当前流位置
				/*
									 客户端请求 sync?since=s123
					服务端返回：
					- 事件 124,125,126
					- next_batch=s126

					下次请求 sync?since=s126 将获取127之后的事件
				*/
				syncReq.Log.WithField("currentPos", currentPos).Debugln("Responding to sync after wake-up")
			}
		} else {
			syncReq.Log.WithField("currentPos", currentPos).Debugln("Responding to sync immediately")
		}

		// 获取消息 若上面阻塞就是异步 非阻塞就是同步
		// 预设快照事物方法
		// 1. 创建数据库快照（NewDatabaseSnapshot）
		// 2. 执行回调函数f(snapshot)
		// 3. 根据执行结果提交或回滚事务
		// 4. 返回新流位置或原始位置（失败时）
		withTransaction := func(from types.StreamPosition, f func(snapshot storage.DatabaseTransaction) types.StreamPosition) types.StreamPosition {
			var succeeded bool
			snapshot, err := rp.db.NewDatabaseSnapshot(req.Context())
			if err != nil {
				logrus.WithError(err).Error("Failed to acquire database snapshot for sync request")
				return from
			}
			defer func() {
				succeeded = err == nil
				sqlutil.EndTransactionWithCheck(snapshot, &succeeded, &err)
			}()
			return f(snapshot)
		}

		if syncReq.Since.IsEmpty() { // 如果是空的就 是客户端完整的同步  没有 since 的参数下
			// Complete sync
			/**
						为9种不同类型的事件流分别获取最新位置：

			- DeviceList
			- PDU(房间事件)
			- Typing(输入状态)
			- Receipt(已读回执)
			- Invite(邀请)
			- SendToDevice(设备间消息)
			- AccountData(账户数据)
			- NotificationData(通知)
			- Presence(在线状态)
			- 对每个流使用`withTransaction`保证原子性

			- 调用各流的`CompleteSync`方法获取最新位置

			- 确保各流位置的一致性
			- 校准基准点的时候 会吧历史数据返回


			*/
			syncReq.Response.NextBatch = types.StreamingToken{
				// Get the current DeviceListPosition first, as the currentPosition
				// might advance while processing other streams, resulting in flakey
				// tests.
				DeviceListPosition: withTransaction(
					syncReq.Since.DeviceListPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.DeviceListStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				PDUPosition: withTransaction(
					syncReq.Since.PDUPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.PDUStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				TypingPosition: withTransaction(
					syncReq.Since.TypingPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.TypingStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				ReceiptPosition: withTransaction(
					syncReq.Since.ReceiptPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.ReceiptStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				InvitePosition: withTransaction(
					syncReq.Since.InvitePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.InviteStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				SendToDevicePosition: withTransaction(
					syncReq.Since.SendToDevicePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.SendToDeviceStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				AccountDataPosition: withTransaction(
					syncReq.Since.AccountDataPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.AccountDataStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				NotificationDataPosition: withTransaction(
					syncReq.Since.NotificationDataPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.NotificationDataStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
				PresencePosition: withTransaction(
					syncReq.Since.PresencePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.PresenceStreamProvider.CompleteSync(
							syncReq.Context, txn, syncReq,
						)
					},
				),
			}
		} else {
			// Incremental sync
			syncReq.Response.NextBatch = types.StreamingToken{
				PDUPosition: withTransaction(
					syncReq.Since.PDUPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.PDUStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.PDUPosition, rp.Notifier.CurrentPosition().PDUPosition,
						)
					},
				),
				TypingPosition: withTransaction(
					syncReq.Since.TypingPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.TypingStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.TypingPosition, rp.Notifier.CurrentPosition().TypingPosition,
						)
					},
				),
				ReceiptPosition: withTransaction(
					syncReq.Since.ReceiptPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.ReceiptStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.ReceiptPosition, rp.Notifier.CurrentPosition().ReceiptPosition,
						)
					},
				),
				InvitePosition: withTransaction(
					syncReq.Since.InvitePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.InviteStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.InvitePosition, rp.Notifier.CurrentPosition().InvitePosition,
						)
					},
				),
				SendToDevicePosition: withTransaction(
					syncReq.Since.SendToDevicePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.SendToDeviceStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.SendToDevicePosition, rp.Notifier.CurrentPosition().SendToDevicePosition,
						)
					},
				),
				AccountDataPosition: withTransaction(
					syncReq.Since.AccountDataPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.AccountDataStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.AccountDataPosition, rp.Notifier.CurrentPosition().AccountDataPosition,
						)
					},
				),
				NotificationDataPosition: withTransaction(
					syncReq.Since.NotificationDataPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.NotificationDataStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.NotificationDataPosition, rp.Notifier.CurrentPosition().NotificationDataPosition,
						)
					},
				),
				DeviceListPosition: withTransaction(
					syncReq.Since.DeviceListPosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.DeviceListStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.DeviceListPosition, rp.Notifier.CurrentPosition().DeviceListPosition,
						)
					},
				),
				PresencePosition: withTransaction(
					syncReq.Since.PresencePosition,
					func(txn storage.DatabaseTransaction) types.StreamPosition {
						return rp.streams.PresenceStreamProvider.IncrementalSync(
							syncReq.Context, txn, syncReq,
							syncReq.Since.PresencePosition, rp.Notifier.CurrentPosition().PresencePosition,
						)
					},
				),
			}
			// it's possible for there to be no updates for this user even though since < current pos,
			// e.g busy servers with a quiet user. In this scenario, we don't want to return a no-op
			// response immediately, so let's try this again but pretend they bumped their since token.
			// If the incremental sync was processed very quickly then we expect the next loop to block
			// with a notifier, but if things are slow it's entirely possible that currentPos is no
			// longer the current position so we will hit this code path again. We need to do this and
			// not return a no-op response because:
			// - It's an inefficient use of bandwidth.
			// - Some sytests which test 'waking up' sync rely on some sync requests to block, which
			//   they weren't always doing, resulting in flakey tests.
			if !syncReq.Response.HasUpdates() {
				syncReq.Since = currentPos
				// do not loop again if the ?timeout= is 0 as that means "return immediately"
				if syncReq.Timeout > 0 {
					syncReq.Timeout = syncReq.Timeout - time.Since(startTime)
					if syncReq.Timeout < 0 {
						syncReq.Timeout = 0
					}
					continue
				}
			}
		}

		return util.JSONResponse{
			Code: http.StatusOK,
			JSON: syncReq.Response,
		}
	}
}

/*
OnIncomingKeyChangeRequest 处理客户端密钥变更请求

工作流程:
1. 从URL参数获取from和to令牌
2. 验证令牌格式
3. 创建同步请求上下文
4. 获取数据库快照
5. 执行增量同步获取密钥变更
6. 捕获设备列表变更
7. 返回变更的设备列表

参数:
  - req: HTTP请求对象，包含查询参数
  - device: 用户设备信息，包含用户ID和设备ID

返回:
  - util.JSONResponse: JSON格式的响应，包含:
    * changed: 设备列表变更的用户ID数组
    * left: 离开的用户ID数组

错误处理:
  - 缺少from/to参数: 返回400错误
  - 无效的令牌格式: 返回400错误
  - 数据库错误: 返回500错误
  - 其他错误: 根据情况返回适当状态码

注意事项:
  - 必须在事务中执行所有数据库操作
  - 需要确保及时释放数据库资源
  - 该方法主要用于处理客户端轮询设备密钥变更
*/

/*
*

	什么情况下密钥会发生变更
	设备首次登录时：

新设备注册时会生成全新的设备密钥对
包含身份密钥(Ed25519)和签名密钥(Curve25519)
密钥轮换时：

定期自动轮换一次性密钥(OTKs)
当可用OTK数量低于阈值时(默认5个)
设备注销时：

设备主动登出会吊销所有密钥
服务器端会标记密钥为失效
安全事件触发：

检测到密钥泄露时
用户手动触发密钥重置
设备被管理员撤销
跨设备签名更新：

当用户在其他设备更新主身份密钥时
所有设备需要更新签名密钥链
密钥变更会通过以下方式通知：

DeviceList流位置更新
m.device_list_update事件
同步响应中的device_lists.changed字段
*/
func (rp *RequestPool) OnIncomingKeyChangeRequest(req *http.Request, device *userapi.Device) util.JSONResponse {
	from := req.URL.Query().Get("from")
	to := req.URL.Query().Get("to")
	if from == "" || to == "" {
		return util.JSONResponse{
			Code: 400,
			JSON: spec.InvalidParam("missing ?from= or ?to="),
		}
	}
	fromToken, err := types.NewStreamTokenFromString(from)
	if err != nil {
		return util.JSONResponse{
			Code: 400,
			JSON: spec.InvalidParam("bad 'from' value"),
		}
	}
	toToken, err := types.NewStreamTokenFromString(to)
	if err != nil {
		return util.JSONResponse{
			Code: 400,
			JSON: spec.InvalidParam("bad 'to' value"),
		}
	}
	syncReq, err := newSyncRequest(req, *device, rp.db)
	if err != nil {
		util.GetLogger(req.Context()).WithError(err).Error("newSyncRequest failed")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	snapshot, err := rp.db.NewDatabaseSnapshot(req.Context())
	if err != nil {
		logrus.WithError(err).Error("Failed to acquire database snapshot for key change")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	var succeeded bool
	defer sqlutil.EndTransactionWithCheck(snapshot, &succeeded, &err)
	rp.streams.PDUStreamProvider.IncrementalSync(req.Context(), snapshot, syncReq, fromToken.PDUPosition, toToken.PDUPosition)
	_, _, err = internal.DeviceListCatchup(
		req.Context(), snapshot, rp.userAPI, rp.rsAPI, syncReq.Device.UserID,
		syncReq.Response, fromToken.DeviceListPosition, toToken.DeviceListPosition,
	)
	if err != nil {
		util.GetLogger(req.Context()).WithError(err).Error("Failed to DeviceListCatchup info")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	succeeded = true
	return util.JSONResponse{
		Code: 200,
		JSON: struct {
			Changed []string `json:"changed"`
			Left    []string `json:"left"`
		}{
			Changed: syncReq.Response.DeviceLists.Changed,
			Left:    syncReq.Response.DeviceLists.Left,
		},
	}
}

/*
shouldReturnImmediately 判断/sync请求是否应立即返回而不等待通知

参数:
  - syncReq: 同步请求对象，包含:
  - Since: 客户端提供的同步令牌
  - Timeout: 客户端请求的超时时间
  - WantFullState: 是否请求完整状态
  - currentPos: 服务器当前的事件流位置

返回:
  - bool: 如果满足以下任一条件则返回true:
  - 当前流位置已经超过客户端提供的同步令牌位置
  - 客户端设置了timeout=0(立即返回)
  - 客户端请求了完整状态(full_state=true)

使用场景:
1. 初始同步: Since为空，需要立即返回完整状态
2. 客户端轮询: timeout=0表示不等待新事件
3. 状态重置: full_state=true强制返回当前所有状态
4. 普通增量同步: 当有未同步事件时立即返回

注意事项:
- 该方法是/sync性能优化的关键部分
- 返回true时不会创建监听器，减少资源消耗
- 需要与Notifier.CurrentPosition()配合使用
*/
func (rp *RequestPool) shouldReturnImmediately(syncReq *types.SyncRequest, currentPos types.StreamingToken) bool {
	if currentPos.IsAfter(syncReq.Since) || syncReq.Timeout == 0 || syncReq.WantFullState {
		return true
	}
	return false
}
