// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package syncapi

import (
	"context"

	"github.com/element-hq/dendrite/internal/fulltext"
	"github.com/element-hq/dendrite/internal/httputil"
	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/element-hq/dendrite/setup/process"
	"github.com/sirupsen/logrus"

	"github.com/element-hq/dendrite/internal/caching"

	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/jetstream"
	userapi "github.com/element-hq/dendrite/userapi/api"

	"github.com/element-hq/dendrite/syncapi/consumers"
	"github.com/element-hq/dendrite/syncapi/notifier"
	"github.com/element-hq/dendrite/syncapi/producers"
	"github.com/element-hq/dendrite/syncapi/routing"
	"github.com/element-hq/dendrite/syncapi/storage"
	"github.com/element-hq/dendrite/syncapi/streams"
	"github.com/element-hq/dendrite/syncapi/sync"
)

// AddPublicRoutes sets up and registers HTTP handlers for the SyncAPI
// component.
func AddPublicRoutes(
	processContext *process.ProcessContext,  // 进程上下文，管理组件生命周期
	routers httputil.Routers,               // HTTP 路由器，用于注册 API 端点
	dendriteCfg *config.Dendrite,           // Dendrite 完整配置
	cm *sqlutil.Connections,                // 数据库连接管理器
	natsInstance *jetstream.NATSInstance,   // NATS JetStream 消息队列实例
	userAPI userapi.SyncUserAPI,            // 用户 API 接口，用于用户相关操作
	rsAPI api.SyncRoomserverAPI,            // 房间服务器 API 接口，用于房间相关操作
	caches caching.LazyLoadCache,           // 延迟加载缓存，优化性能
	enableMetrics bool,                     // 是否启用 Prometheus 指标监控
) {
	// 准备 JetStream 和 NATS 客户端连接，用于组件间异步消息传递
	js, natsClient := natsInstance.Prepare(processContext, &dendriteCfg.Global.JetStream)

	// 创建 SyncAPI 专用的数据库连接，存储同步状态、流位置等数据
	syncDB, err := storage.NewSyncServerDatasource(processContext.Context(), cm, &dendriteCfg.SyncAPI.Database)
	if err != nil {
		logrus.WithError(err).Panicf("failed to connect to sync db") // 数据库连接失败则终止程序
	}

	// 创建打字通知的内存缓存，临时存储用户打字状态
	eduCache := caching.NewTypingCache()
	// 创建事件通知器，负责通知等待同步的客户端有新事件
	notifier := notifier.NewNotifier(rsAPI)
	// 创建同步流提供者，管理各种类型的数据流（消息、邀请、设备等）
	streams := streams.NewSyncStreamProviders(syncDB, userAPI, rsAPI, eduCache, caches, notifier)
	// 设置通知器的当前位置为最新流位置，确保从正确位置开始通知
	notifier.SetCurrentPosition(streams.Latest(context.Background()))
	// 从数据库加载通知器的持久化状态
	if err = notifier.Load(context.Background(), syncDB); err != nil {
		logrus.WithError(err).Panicf("failed to load notifier ") // 加载失败则终止程序
	}

	// 初始化全文搜索引擎（如果启用）
	var fts *fulltext.Search
	if dendriteCfg.SyncAPI.Fulltext.Enabled { // 检查配置是否启用全文搜索
		fts, err = fulltext.New(processContext, dendriteCfg.SyncAPI.Fulltext) // 创建全文搜索实例
		if err != nil {
			logrus.WithError(err).Panicf("failed to create full text") // 创建失败则终止程序
		}
	}

	// 创建联邦在线状态生产者，向其他服务器发送用户在线状态变更
	federationPresenceProducer := &producers.FederationAPIPresenceProducer{
		Topic:     dendriteCfg.Global.JetStream.Prefixed(jetstream.OutputPresenceEvent), // 在线状态事件主题
		JetStream: js, // JetStream 实例
	}
	// 创建在线状态消费者，处理来自其他组件的在线状态事件
	presenceConsumer := consumers.NewPresenceConsumer(
		processContext, &dendriteCfg.SyncAPI, js, natsClient, syncDB,
		notifier, streams.PresenceStreamProvider,
		userAPI,
	)

	// 创建同步请求池，管理客户端的 /sync 请求，实现长轮询和实时推送
	requestPool := sync.NewRequestPool(syncDB, &dendriteCfg.SyncAPI, userAPI, rsAPI, streams, notifier, federationPresenceProducer, presenceConsumer, enableMetrics)

	// 启动在线状态消费者，开始监听在线状态变更事件
	if err = presenceConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start presence consumer") // 启动失败则终止程序
	}

	// 创建设备密钥变更事件消费者，处理端到端加密相关的密钥更新
	keyChangeConsumer := consumers.NewOutputKeyChangeEventConsumer(
		processContext, &dendriteCfg.SyncAPI, dendriteCfg.Global.JetStream.Prefixed(jetstream.OutputKeyChangeEvent), // 密钥变更事件主题
		js, rsAPI, syncDB, notifier,
		streams.DeviceListStreamProvider, // 设备列表流提供者
	)
	// 启动密钥变更消费者
	if err = keyChangeConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start key change consumer") // 启动失败则终止程序
	}

	// 初始化应用服务事件生产者（如果配置了应用服务）
	var asProducer *producers.AppserviceEventProducer
	if len(dendriteCfg.AppServiceAPI.Derived.ApplicationServices) > 0 { // 检查是否配置了应用服务
		asProducer = &producers.AppserviceEventProducer{
			JetStream: js, Topic: dendriteCfg.Global.JetStream.Prefixed(jetstream.OutputAppserviceEvent), // 应用服务事件主题
		}
	}

	// 创建房间事件消费者，处理来自房间服务器的所有房间相关事件（消息、状态变更等）
	roomConsumer := consumers.NewOutputRoomEventConsumer(
		processContext, &dendriteCfg.SyncAPI, js, syncDB, notifier, streams.PDUStreamProvider, // PDU（协议数据单元）流提供者
		streams.InviteStreamProvider, rsAPI, fts, asProducer, // 邀请流提供者、房间服务器API、全文搜索、应用服务生产者
	)
	// 启动房间事件消费者
	if err = roomConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start room server consumer") // 启动失败则终止程序
	}

	// 创建客户端数据消费者，处理用户账户数据变更（如推送规则、房间标签等）
	clientConsumer := consumers.NewOutputClientDataConsumer(
		processContext, &dendriteCfg.SyncAPI, js, natsClient, syncDB, notifier,
		streams.AccountDataStreamProvider, fts, // 账户数据流提供者、全文搜索
	)
	// 启动客户端数据消费者
	if err = clientConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start client data consumer") // 启动失败则终止程序
	}

	// 创建通知数据消费者，处理推送通知相关的数据
	notificationConsumer := consumers.NewOutputNotificationDataConsumer(
		processContext, &dendriteCfg.SyncAPI, js, syncDB, notifier, streams.NotificationDataStreamProvider, // 通知数据流提供者
	)
	// 启动通知数据消费者
	if err = notificationConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start notification data consumer") // 启动失败则终止程序
	}

	// 创建打字事件消费者，处理用户打字通知
	typingConsumer := consumers.NewOutputTypingEventConsumer(
		processContext, &dendriteCfg.SyncAPI, js, eduCache, notifier, streams.TypingStreamProvider, // 打字流提供者
	)
	// 启动打字事件消费者
	if err = typingConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start typing consumer") // 启动失败则终止程序
	}

	// 创建设备消息消费者，处理端到端加密的设备间直接消息
	sendToDeviceConsumer := consumers.NewOutputSendToDeviceEventConsumer(
		processContext, &dendriteCfg.SyncAPI, js, syncDB, userAPI, notifier, streams.SendToDeviceStreamProvider, // 设备消息流提供者
	)
	// 启动设备消息消费者
	if err = sendToDeviceConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start send-to-device consumer") // 启动失败则终止程序
	}

	// 创建已读回执消费者，处理消息已读状态
	receiptConsumer := consumers.NewOutputReceiptEventConsumer(
		processContext, &dendriteCfg.SyncAPI, js, syncDB, notifier, streams.ReceiptStreamProvider, // 已读回执流提供者
	)
	// 启动已读回执消费者
	if err = receiptConsumer.Start(); err != nil {
		logrus.WithError(err).Panicf("failed to start receipts consumer") // 启动失败则终止程序
	}

	// 创建速率限制器，防止客户端过于频繁地请求同步
	rateLimits := httputil.NewRateLimits(&dendriteCfg.ClientAPI.RateLimiting)

	// 设置 HTTP 路由，注册 SyncAPI 的所有公开端点（如 /sync, /events 等）
	routing.Setup(
		routers.Client, requestPool, syncDB, userAPI,
		rsAPI, &dendriteCfg.SyncAPI, caches, fts,
		rateLimits,
	)
}
