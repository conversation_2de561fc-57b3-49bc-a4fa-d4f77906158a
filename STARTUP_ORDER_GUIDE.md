# 阅后即焚服务启动顺序指南

## 🚨 **重要：启动顺序问题**

您遇到的 panic 错误是因为启动顺序不正确导致的。阅后即焚服务在 roomserver 完全初始化之前就开始工作了。

## 🔧 **正确的集成步骤**

### **步骤1: 在 roomserver 初始化时集成（但不启动）**

```go
// 在 roomserver 创建后立即调用
roomserverAPI := roomserver.NewInternalAPI(cfg, roomserverDB, caches, fsAPI, keyRing)

// 初始化阅后即焚服务（但不启动）
err = expiring_messages.IntegrateIntoRoomserver(roomserverDB, roomserverAPI, cfg)
if err != nil {
    logrus.WithError(err).Error("集成阅后即焚功能失败")
}
```

### **步骤2: 在所有服务初始化完成后启动**

```go
// 在 main 函数的最后，所有服务都初始化完成后
func main() {
    // ... 所有服务初始化 ...
    
    // 启动 HTTP 服务器之前
    logrus.Info("所有服务初始化完成，启动阅后即焚消息服务")
    expiring_messages.StartServiceAfterRoomserverReady()
    
    // 启动 HTTP 服务器
    base.SetupAndServeHTTP(...)
}
```

## 📋 **三种启动方式**

### **方式1: 延迟启动（推荐）**
```go
// 自动延迟5秒启动，确保所有组件就绪
expiring_messages.StartService()
```

### **方式2: 立即启动**
```go
// 确保 roomserver 完全初始化后使用
expiring_messages.StartServiceImmediately()
```

### **方式3: 自定义延迟**
```go
// 自定义延迟时间
expiring_messages.StartServiceWithDelay(10 * time.Second)
```

## 🔍 **错误原因分析**

您的错误堆栈：
```
QueryLatestEventsAndState(...) // 空指针引用
↓
queryRequiredEventsForBuilder(...)
↓
QueryAndBuildEvent(...)
↓
createRedactionEvent(...) // 阅后即焚服务
```

**根本原因**: roomserver 的 `Queryer` 组件还没有完全初始化，但阅后即焚服务已经开始尝试查询房间状态。

## 🛠️ **修复方案**

### **在您的代码中修改**

1. **找到集成阅后即焚的位置**，确保只是初始化，不启动：
```go
// ❌ 错误：立即启动
err = expiring_messages.IntegrateIntoRoomserver(db, rsAPI, cfg)

// ✅ 正确：只初始化
err = expiring_messages.InitializeServiceFromConfig(db, rsAPI, cfg)
```

2. **在 main 函数最后启动**：
```go
// 在所有服务初始化完成后
defer func() {
    time.Sleep(2 * time.Second) // 额外等待
    expiring_messages.StartServiceImmediately()
}()
```

## 📊 **预期的日志输出**

**正确的启动顺序日志**：
```
INFO 阅后即焚消息服务已从配置初始化（延迟启动）
INFO 服务已准备就绪，请在 roomserver 完全初始化后调用 StartService()
INFO ✅ 阅后即焚消息服务已成功集成（从配置）
INFO ⏰ 请在 roomserver 完全启动后调用 StartService() 启动服务

... 其他服务初始化 ...

INFO 所有服务初始化完成，启动阅后即焚消息服务
INFO 立即启动阅后即焚消息服务
INFO 阅后即焚消息服务已启动
INFO 启动阅后即焚消息处理服务 check_interval=10s batch_size=100
```

## 🧪 **测试验证**

### **1. 检查服务状态**
```go
expiring_messages.CheckServiceStatus()
```

### **2. 验证启动顺序**
确保看到以下日志顺序：
1. "服务已准备就绪，请在 roomserver 完全初始化后调用 StartService()"
2. "所有服务初始化完成，启动阅后即焚消息服务"
3. "启动阅后即焚消息处理服务"

### **3. 功能测试**
```bash
# 发送测试消息
curl -X PUT 'http://localhost:8008/_matrix/client/v3/rooms/!room:server.com/send/m.room.message/test' \
  -H 'Authorization: Bearer TOKEN' \
  -d '{"msgtype": "m.text", "body": "test", "expire_ts": 1750672109}'
```

## ⚠️ **常见错误**

### **错误1: 在 roomserver 构造函数中启动**
```go
// ❌ 错误
func NewInternalAPI(...) *RoomserverInternalAPI {
    // ...
    expiring_messages.StartService() // 太早了！
    return api
}
```

### **错误2: 没有延迟启动**
```go
// ❌ 错误
expiring_messages.StartServiceImmediately() // roomserver 可能还没准备好
```

### **错误3: 重复启动**
```go
// ❌ 错误
expiring_messages.StartService()
expiring_messages.StartService() // 重复启动
```

## 🎯 **最佳实践**

1. **分离初始化和启动**: 在 roomserver 创建时初始化，在所有服务就绪后启动
2. **使用延迟启动**: 给 roomserver 足够的时间完成内部组件初始化
3. **添加错误处理**: 检查服务状态，处理启动失败的情况
4. **监控日志**: 确保启动顺序正确

按照这个指南修改后，应该就不会再出现 panic 错误了！
