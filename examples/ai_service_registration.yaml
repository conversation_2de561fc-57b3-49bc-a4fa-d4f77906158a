# AI Service Application Service Registration
# This is a specific example for an AI chatbot service
# Based on the AI service implementation in this Dendrite project

# Unique identifier for the AI service
id: ai-service-001

# URL where the AI service is running
# This should match where your AI service HTTP server is listening
url: http://localhost:8080

# Application Service token - replace with a secure random string
# You can generate this with: openssl rand -hex 32
as_token: "ai_service_token_replace_with_secure_random_string_32_chars_minimum"

# Homeserver token - replace with a secure random string  
# You can generate this with: openssl rand -hex 32
hs_token: "homeserver_token_replace_with_secure_random_string_32_chars_minimum"

# The AI bot user localpart
# Full user ID will be @ai-bot:your_server_name
sender_localpart: ai-bot

# Namespaces managed by the AI service
namespaces:
  # AI service manages all users starting with "ai-" or "bot-"
  users:
    - exclusive: true
      regex: "@ai-.*:.*"
    - exclusive: true  
      regex: "@bot-.*:.*"
    - exclusive: true
      regex: "@assistant-.*:.*"
      
  # AI service can create room aliases for AI-related rooms
  aliases:
    - exclusive: true
      regex: "#ai-.*:.*"
    - exclusive: true
      regex: "#bot-.*:.*"
    - exclusive: false
      regex: "#assistant-.*:.*"
      
  # No specific room management needed for AI service
  rooms: []

# No specific protocols for AI service
protocols: []

# AI service should not be rate limited for responsive conversations
rate_limited: false

# AI service needs ephemeral events for typing indicators and read receipts
push_ephemeral: true

# Enable transaction de-duplication to prevent duplicate AI responses
de_duplicating_transactions: true
