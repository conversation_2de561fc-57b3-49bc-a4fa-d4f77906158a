# Application Service Registration Configuration Example
# This file demonstrates how to configure an application service for Dendrite
# Place this file in a location referenced by your dendrite.yaml config

# Unique identifier for this application service
# This should be unique across all application services on your homeserver
id: example-appservice

# The URL where your application service is running
# Dendrite will send events to this URL
url: http://localhost:9000

# Application Service token - used by the AS to authenticate with the homeserver
# Generate a secure random token for production use
as_token: "your_application_service_token_here_replace_with_secure_random_string"

# Homeserver token - used by the homeserver to authenticate with the AS
# Generate a secure random token for production use  
hs_token: "your_homeserver_token_here_replace_with_secure_random_string"

# The localpart of the application service's sender user
# This user will be created automatically by Dendrite
# Full user ID will be @sender_localpart:your_server_name
sender_localpart: appservice_bot

# Namespaces that this application service manages
namespaces:
  # User namespaces - users that this AS can create/manage
  users:
    - exclusive: true  # Only this AS can create users matching this pattern
      regex: "@example_.*:.*"  # Regex pattern for usernames
    - exclusive: false  # Non-exclusive namespace (shared with other ASes)
      regex: "@shared_.*:.*"
      
  # Room alias namespaces - aliases that this AS can create/manage  
  aliases:
    - exclusive: true
      regex: "#example_.*:.*"
    - exclusive: false
      regex: "#shared_.*:.*"
      
  # Room namespaces - specific rooms this AS manages (usually empty)
  rooms: []

# Protocols supported by this application service (optional)
# Used for protocol bridging (e.g., IRC, Telegram, etc.)
protocols:
  - "example_protocol"

# Whether this application service should be rate limited
# Set to false for trusted application services
rate_limited: false

# Push ephemeral events to the application service (optional)
# Set to true if your AS needs to receive typing notifications, read receipts, etc.
push_ephemeral: false

# De-duplicating transactions (optional)
# If true, the homeserver will not retry failed transactions
de_duplicating_transactions: false
