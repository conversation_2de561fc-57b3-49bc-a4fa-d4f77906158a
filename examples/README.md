# Application Service Registration Examples

这个目录包含了 Dendrite Application Service 注册配置文件的示例。

## 文件说明

### 1. `appservice_registration.yaml`
通用的 Application Service 注册配置模板，包含了所有可用的配置选项和详细注释。

### 2. `ai_service_registration.yaml`  
专门为 AI 聊天机器人服务设计的配置示例，基于本项目中的 AI 服务实现。

### 3. `bridge_service_registration.yaml`
协议桥接服务的配置示例，适用于 Telegram、WhatsApp、IRC 等桥接服务。

## 使用方法

### 1. 选择合适的模板
根据您的需求选择相应的配置模板：
- 通用服务：使用 `appservice_registration.yaml`
- AI 机器人：使用 `ai_service_registration.yaml`  
- 协议桥接：使用 `bridge_service_registration.yaml`

### 2. 复制并修改配置
```bash
# 复制模板到您的配置目录
cp examples/ai_service_registration.yaml /etc/dendrite/appservices/my-service.yaml

# 编辑配置文件
nano /etc/dendrite/appservices/my-service.yaml
```

### 3. 生成安全令牌
替换配置文件中的令牌占位符：
```bash
# 生成 AS Token
openssl rand -hex 32

# 生成 HS Token  
openssl rand -hex 32
```

### 4. 更新 Dendrite 主配置
在 `dendrite.yaml` 中添加您的 Application Service 配置：
```yaml
app_service_api:
  config_files:
    - /etc/dendrite/appservices/my-service.yaml
```

### 5. 重启 Dendrite
重启 Dendrite 服务以加载新的 Application Service 配置。

## 配置字段说明

### 必需字段
- `id`: Application Service 的唯一标识符
- `url`: Application Service 的 HTTP 端点 URL
- `as_token`: Application Service 令牌
- `hs_token`: Homeserver 令牌  
- `sender_localpart`: 发送者用户的本地部分
- `namespaces`: 命名空间配置

### 可选字段
- `protocols`: 支持的协议列表
- `rate_limited`: 是否启用速率限制
- `push_ephemeral`: 是否推送临时事件
- `de_duplicating_transactions`: 是否启用事务去重

## 命名空间配置

### users 命名空间
定义 Application Service 可以创建和管理的用户模式：
```yaml
users:
  - exclusive: true    # 独占模式
    regex: "@bot_.*:.*"  # 正则表达式模式
```

### aliases 命名空间  
定义 Application Service 可以创建和管理的房间别名模式：
```yaml
aliases:
  - exclusive: true
    regex: "#bot_.*:.*"
```

### rooms 命名空间
定义 Application Service 管理的特定房间（通常为空）。

## 安全注意事项

1. **令牌安全**: 使用强随机令牌，至少 32 字符
2. **网络安全**: 确保 Application Service 端点的网络安全
3. **权限控制**: 合理配置命名空间，避免过度权限
4. **日志监控**: 监控 Application Service 的访问日志

## 故障排除

### 常见问题
1. **连接失败**: 检查 URL 和网络连接
2. **认证失败**: 验证令牌配置
3. **权限错误**: 检查命名空间配置
4. **重复 ID**: 确保 Application Service ID 唯一

### 调试方法
1. 检查 Dendrite 日志
2. 验证 Application Service 响应
3. 测试网络连接
4. 验证配置语法

## 参考资源

- [Matrix Application Service API 规范](https://matrix.org/docs/spec/application_service/latest)
- [Dendrite 官方文档](https://matrix-org.github.io/dendrite/)
- [mautrix 桥接项目](https://docs.mau.fi/bridges/)
