# Bridge Service Application Service Registration
# This is an example for a protocol bridge (e.g., Telegram, WhatsApp, IRC)
# Commonly used with mautrix bridges

# Unique identifier for the bridge service
id: telegram-bridge

# URL where the bridge service is running
url: http://localhost:29317

# Application Service token - replace with a secure random string
as_token: "telegram_bridge_as_token_replace_with_secure_random_string"

# Homeserver token - replace with a secure random string
hs_token: "telegram_bridge_hs_token_replace_with_secure_random_string"

# The bridge bot user localpart
sender_localpart: telegrambot

# Namespaces for the Telegram bridge
namespaces:
  # Bridge manages Telegram users
  users:
    - exclusive: true
      regex: "@telegram_.*:.*"
    - exclusive: true
      regex: "@_telegram_.*:.*"
      
  # Bridge manages Telegram room aliases
  aliases:
    - exclusive: true
      regex: "#telegram_.*:.*"
    - exclusive: true
      regex: "#_telegram_.*:.*"
      
  # No specific room management
  rooms: []

# Telegram protocol support
protocols:
  - "telegram"

# Bridges should typically not be rate limited
rate_limited: false

# Bridges need ephemeral events for proper message sync
push_ephemeral: true

# Enable transaction de-duplication
de_duplicating_transactions: true
