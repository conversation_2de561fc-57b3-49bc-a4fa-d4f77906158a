# Dendrite Matrix 服务器部署 Makefile
# 提供常用的部署和管理命令

# 声明伪目标（不对应实际文件的目标）
.PHONY: help setup build start stop restart logs status clean user test deploy

# 默认目标：显示帮助
.DEFAULT_GOAL := help

# 终端颜色定义
GREEN := \033[32m
YELLOW := \033[33m
RED := \033[31m
NC := \033[0m

help: ## 显示所有可用命令
	@echo "$(GREEN)Dendrite Matrix 服务器部署工具$(NC)"
	@echo ""
	@echo "$(YELLOW)常用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-12s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# === 初始化相关命令 ===

# setup: ## 初始化项目（生成密钥、配置文件、目录）
# 	@echo "$(GREEN)初始化 Dendrite 项目...$(NC)"
# 	@$(MAKE) keys
# 	@mkdir -p logs
# 	@echo "$(GREEN)项目初始化完成！$(NC)"

# keys: ## 生成 Matrix 服务器签名密钥
# 	@echo "$(GREEN)生成 Matrix 签名密钥...$(NC)"
# 	@if [ ! -f matrix_key.pem ]; then \
# 		go run ./cmd/generate-keys --private-key matrix_key.pem; \
# 		echo "$(GREEN)密钥已生成: matrix_key.pem$(NC)"; \
# 	else \
# 		echo "$(YELLOW)密钥文件已存在$(NC)"; \
# 	fi

# === Docker 服务管理命令 ===

build: ## 构建 Dendrite Docker 镜像
	@echo "$(GREEN)构建 Docker 镜像...$(NC)"
	docker-compose build

start: ## 启动 Dendrite 服务
	@echo "$(GREEN)启动服务...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)服务已启动！$(NC)"
	@echo "$(YELLOW)客户端访问: http://localhost:8008$(NC)"
	@echo "$(YELLOW)联邦接口: http://localhost:8448$(NC)"

stop: ## 停止 Dendrite 服务
	@echo "$(GREEN)停止服务...$(NC)"
	docker-compose down

restart: ## 重启 Dendrite 服务
	@echo "$(GREEN)重启服务...$(NC)"
	docker-compose restart

# === 监控和调试命令 ===

logs: ## 查看实时日志
	@echo "$(GREEN)显示服务日志...$(NC)"
	docker-compose logs -f dendrite

status: ## 查看服务运行状态
	@echo "$(GREEN)服务状态:$(NC)"
	docker-compose ps

test: ## 测试服务是否正常运行
	@echo "$(GREEN)测试服务连接...$(NC)"
	@curl -s http://localhost:8008/_matrix/client/versions > /dev/null && \
		echo "$(GREEN)✓ 客户端 API 正常$(NC)" || \
		echo "$(RED)✗ 客户端 API 异常$(NC)"

# === 用户管理命令 ===

user: ## 创建新用户账户
	@echo "$(GREEN)创建用户账户...$(NC)"
	@read -p "用户名: " username; \
	read -s -p "密码: " password; \
	echo ""; \
	docker-compose exec dendrite /usr/bin/create-account \
		-config /etc/dendrite/dendrite.yaml \
		-username $$username -password $$password

# === 清理命令 ===

clean: ## 清理 Docker 容器和镜像
	@echo "$(RED)清理容器和镜像...$(NC)"
	docker-compose down -v --rmi all
	docker system prune -f

# === 一键部署命令 ===

deploy: setup build start ## 一键部署（初始化 -> 构建 -> 启动）
	@echo "$(GREEN)部署完成！$(NC)"
	@echo "$(YELLOW)访问 http://localhost:8008 开始使用$(NC)"
