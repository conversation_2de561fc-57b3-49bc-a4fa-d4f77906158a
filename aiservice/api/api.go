// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务API包 - 定义AI对话服务的接口和类型
package api

import (
	"context"
	"time"

	"github.com/matrix-org/gomatrixserverlib/spec"
)

// AIServiceInternalAPI 定义AI服务的内部API接口
type AIServiceInternalAPI interface {
	// ProcessMessage 处理收到的消息，如果是艾特AI的消息则生成回复
	ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)
	
	// IsAIUser 检查给定的用户ID是否是AI用户
	IsAIUser(ctx context.Context, userID spec.UserID) bool
	
	// GetAIResponse 根据输入消息生成AI回复
	GetAIResponse(ctx context.Context, message string, roomID spec.RoomID, senderID spec.UserID) (string, error)
}

// ProcessMessageRequest 处理消息的请求结构
type ProcessMessageRequest struct {
	RoomID    spec.RoomID  `json:"room_id"`    // 房间ID
	EventID   string       `json:"event_id"`   // 事件ID
	Sender    spec.UserID  `json:"sender"`     // 发送者用户ID
	EventType string       `json:"event_type"` // 事件类型
	Content   MessageContent `json:"content"`  // 消息内容
	Timestamp time.Time    `json:"timestamp"`  // 时间戳
}

// ProcessMessageResponse 处理消息的响应结构
type ProcessMessageResponse struct {
	ShouldReply bool   `json:"should_reply"` // 是否应该回复
	ReplyText   string `json:"reply_text"`   // 回复内容
	AIUserID    spec.UserID `json:"ai_user_id"` // AI用户ID
}

// MessageContent 消息内容结构
type MessageContent struct {
	Body    string `json:"body"`              // 消息正文
	MsgType string `json:"msgtype,omitempty"` // 消息类型
}

// AIConfig AI服务配置
type AIConfig struct {
	Enabled     bool     `yaml:"enabled"`      // 是否启用AI服务
	AIUserID    string   `yaml:"ai_user_id"`   // AI用户的Matrix ID
	AIUserName  string   `yaml:"ai_user_name"` // AI用户显示名称
	Triggers    []string `yaml:"triggers"`     // 触发AI回复的关键词
	MaxLength   int      `yaml:"max_length"`   // 最大回复长度
	APIEndpoint string   `yaml:"api_endpoint"` // 外部AI API端点（可选）
	APIKey      string   `yaml:"api_key"`      // API密钥（可选）
}

// DefaultAIConfig 返回默认的AI配置
func DefaultAIConfig() AIConfig {
	return AIConfig{
		Enabled:    false,
		AIUserID:   "@ai:localhost",
		AIUserName: "AI Assistant",
		Triggers:   []string{"@ai", "ai", "助手"},
		MaxLength:  1000,
	}
}