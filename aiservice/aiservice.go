// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务包 - 提供AI对话功能的主入口
package aiservice

import (
	"github.com/element-hq/dendrite/aiservice/api"
	"github.com/element-hq/dendrite/aiservice/internal"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/element-hq/dendrite/setup/process"
	userapi "github.com/element-hq/dendrite/userapi/api"
)

// NewInternalAPI 创建新的AI服务内部API实例
func NewInternalAPI(
	processContext *process.ProcessContext,
	cfg *config.AIService,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
) api.AIServiceInternalAPI {
	return internal.NewAIService(cfg, rsAPI, userAPI)
}