// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务启动包 - 负责启动AI服务的各种消费者
package aiservice

import (
	"github.com/element-hq/dendrite/aiservice/api"
	"github.com/element-hq/dendrite/aiservice/consumers"
	"github.com/element-hq/dendrite/internal/transactions"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/element-hq/dendrite/setup/jetstream"
	"github.com/element-hq/dendrite/setup/process"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/sirupsen/logrus"
)

// StartConsumers 启动AI服务的所有消费者
func StartConsumers(
	processContext *process.ProcessContext,
	cfg *config.AIService,
	natsInstance *jetstream.NATSInstance,
	aiAPI api.AIServiceInternalAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
	clientCfg *config.ClientAPI,
	txnCache *transactions.Cache,
) error {
	// 检查AI服务是否启用
	if !cfg.Enabled {
		logrus.Info("AI服务未启用，跳过消费者启动")
		return nil
	}

	logrus.Info("启动AI服务消费者...")

	// 获取JetStream上下文
	js, _ := natsInstance.Prepare(processContext, &cfg.Matrix.JetStream)

	// 创建并启动房间事件消费者 // 这个消费者
	roomEventConsumer := consumers.NewOutputRoomEventConsumer(
		processContext, cfg, js, aiAPI, rsAPI, userAPI, clientCfg, txnCache,
	)

	// 启动消费者
	if err := roomEventConsumer.Start(); err != nil {
		logrus.WithError(err).Error("启动AI服务房间事件消费者失败")
		return err
	}

	logrus.Info("AI服务消费者启动成功")
	return nil
}