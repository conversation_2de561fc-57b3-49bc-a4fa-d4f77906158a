// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务测试包
package internal

import (
	"context"
	"testing"
	"time"

	aiapi "github.com/element-hq/dendrite/aiservice/api"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 创建测试用的AI服务配置
func createTestConfig() *config.AIService {
	return &config.AIService{
		Enabled:    true,
		AIUserID:   "@ai:test.com",
		AIUserName: "Test AI",
		Triggers:   []string{"@ai", "ai", "助手"},
		MaxLength:  1000,
		ReplyDelay: 0, // 测试时不需要延迟
	}
}

// 创建测试用的AI服务实例
func createTestAIService() *AIService {
	cfg := createTestConfig()
	aiUserID, _ := spec.NewUserID("@ai:test.com", true)
	return &AIService{
		cfg:       cfg,
		rsAPI:     nil, // 测试时不需要真实的API
		userAPI:   nil,
		aiUserID:  *aiUserID,
		responses: []string{"测试回复1", "测试回复2", "测试回复3"},
	}
}

// TestIsAIUser 测试AI用户识别功能
func TestIsAIUser(t *testing.T) {
	ai := createTestAIService()
	ctx := context.Background()

	// 测试AI用户
	aiUserID, _ := spec.NewUserID("@ai:test.com", true)
	assert.True(t, ai.IsAIUser(ctx, *aiUserID), "应该识别AI用户")

	// 测试普通用户
	normalUserID, _ := spec.NewUserID("@user:test.com", true)
	assert.False(t, ai.IsAIUser(ctx, *normalUserID), "不应该将普通用户识别为AI用户")
}

// TestShouldReplyToMessage 测试消息触发逻辑
func TestShouldReplyToMessage(t *testing.T) {
	ai := createTestAIService()

	testCases := []struct {
		name     string
		message  string
		expected bool
	}{
		{"艾特AI用户", "@ai:test.com 你好", true},
		{"包含触发词ai", "ai 帮我解答问题", true},
		{"包含触发词助手", "助手 现在几点了", true},
		{"包含AI用户ID", "请问 @ai:test.com 能帮忙吗", true},
		{"普通消息", "今天天气不错", false},
		{"空消息", "", false},
		{"只有空格", "   ", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ai.shouldReplyToMessage(tc.message)
			assert.Equal(t, tc.expected, result, "消息: %s", tc.message)
		})
	}
}

// TestGetAIResponse 测试AI回复生成
func TestGetAIResponse(t *testing.T) {
	ai := createTestAIService()
	ctx := context.Background()
	roomID, _ := spec.NewRoomID("!test:test.com")
	senderID, _ := spec.NewUserID("@user:test.com", true)

	testCases := []struct {
		name            string
		message         string
		expectedContain string
	}{
		{"问候消息", "你好", "你好"},
		{"时间查询", "现在几点了", "时间"},
		{"帮助请求", "help", "帮助"},
		{"感谢消息", "谢谢", "不客气"},
		{"告别消息", "再见", "再见"},
		{"空消息", "", "你好"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			response, err := ai.GetAIResponse(ctx, tc.message, *roomID, *senderID)
			require.NoError(t, err, "生成回复不应该出错")
			assert.NotEmpty(t, response, "回复不应该为空")
			assert.Contains(t, response, tc.expectedContain, "回复应该包含预期内容")
			assert.LessOrEqual(t, len(response), ai.cfg.MaxLength, "回复长度不应该超过限制")
		})
	}
}

// TestProcessMessage 测试消息处理流程
func TestProcessMessage(t *testing.T) {
	ai := createTestAIService()
	ctx := context.Background()

	// 测试应该回复的消息
	roomID, _ := spec.NewRoomID("!test:test.com")
	senderID, _ := spec.NewUserID("@user:test.com", true)
	req := &aiapi.ProcessMessageRequest{
		RoomID:    *roomID,
		EventID:   "$event1:test.com",
		Sender:    *senderID,
		EventType: "m.room.message",
		Content: aiapi.MessageContent{
			Body:    "ai 你好",
			MsgType: "m.text",
		},
		Timestamp: time.Now(),
	}

	resp, err := ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.True(t, resp.ShouldReply, "应该回复这条消息")
	assert.NotEmpty(t, resp.ReplyText, "回复文本不应该为空")
	assert.Equal(t, ai.aiUserID, resp.AIUserID, "AI用户ID应该正确")

	// 测试不应该回复的消息
	req.Content.Body = "普通消息"
	resp, err = ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.False(t, resp.ShouldReply, "不应该回复这条消息")

	// 测试AI用户自己的消息
	req.Sender = ai.aiUserID
	req.Content.Body = "ai 测试"
	resp, err = ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.False(t, resp.ShouldReply, "不应该回复AI用户自己的消息")

	// 测试非文本消息
	normalUser, _ := spec.NewUserID("@user:test.com", true)
	req.Sender = *normalUser
	req.Content.MsgType = "m.image"
	resp, err = ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.False(t, resp.ShouldReply, "不应该回复非文本消息")

	// 测试非消息事件
	req.EventType = "m.room.member"
	req.Content.MsgType = "m.text"
	resp, err = ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.False(t, resp.ShouldReply, "不应该回复非消息事件")
}

// TestTruncateString 测试字符串截断功能
func TestTruncateString(t *testing.T) {
	ai := createTestAIService()

	testCases := []struct {
		name     string
		input    string
		maxLen   int
		expected string
	}{
		{"短字符串", "hello", 10, "hello"},
		{"正好等于长度", "hello", 5, "hello"},
		{"需要截断", "hello world", 5, "hello"},
		{"中文字符串", "你好世界", 6, "你好世界"},
		{"中文截断", "你好世界测试", 6, "你好世界"},
		{"空字符串", "", 5, ""},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := ai.truncateString(tc.input, tc.maxLen)
			assert.Equal(t, tc.expected, result)
			assert.LessOrEqual(t, len(result), tc.maxLen, "结果长度不应该超过限制")
		})
	}
}

// TestAIServiceDisabled 测试AI服务禁用时的行为
func TestAIServiceDisabled(t *testing.T) {
	cfg := createTestConfig()
	cfg.Enabled = false // 禁用AI服务

	aiUserID, _ := spec.NewUserID("@ai:test.com", true)
	ai := &AIService{
		cfg:       cfg,
		rsAPI:     nil,
		userAPI:   nil,
		aiUserID:  *aiUserID,
		responses: []string{"测试回复"},
	}

	ctx := context.Background()
	roomID, _ := spec.NewRoomID("!test:test.com")
	senderID, _ := spec.NewUserID("@user:test.com", true)
	req := &aiapi.ProcessMessageRequest{
		RoomID:    *roomID,
		EventID:   "$event1:test.com",
		Sender:    *senderID,
		EventType: "m.room.message",
		Content: aiapi.MessageContent{
			Body:    "ai 你好",
			MsgType: "m.text",
		},
		Timestamp: time.Now(),
	}

	resp, err := ai.ProcessMessage(ctx, req)
	require.NoError(t, err, "处理消息不应该出错")
	assert.False(t, resp.ShouldReply, "禁用时不应该回复任何消息")
}

// BenchmarkGetAIResponse 性能测试
func BenchmarkGetAIResponse(b *testing.B) {
	ai := createTestAIService()
	ctx := context.Background()
	roomID, _ := spec.NewRoomID("!test:test.com")
	senderID, _ := spec.NewUserID("@user:test.com", true)
	message := "ai 你好，请帮我解答一个问题"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := ai.GetAIResponse(ctx, message, *roomID, *senderID)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkShouldReplyToMessage 触发检测性能测试
func BenchmarkShouldReplyToMessage(b *testing.B) {
	ai := createTestAIService()
	message := "ai 这是一条测试消息，用来检测触发词匹配的性能"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		ai.shouldReplyToMessage(message)
	}
}