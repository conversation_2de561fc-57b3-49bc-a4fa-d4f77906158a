// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务内部实现包
package internal

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"

	aiapi "github.com/element-hq/dendrite/aiservice/api"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// ChatMessage 聊天消息结构
type ChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatRequest AI API请求结构
type ChatRequest struct {
	Messages         []ChatMessage `json:"messages"`
	Stream           bool          `json:"stream"`
	Model            string        `json:"model"`
	Temperature      float64       `json:"temperature"`
	PresencePenalty  float64       `json:"presence_penalty"`
	FrequencyPenalty float64       `json:"frequency_penalty"`
	TopP             float64       `json:"top_p"`
	TopK             int           `json:"top_k"`
	EnableThinking   bool          `json:"enable_thinking"`
}

// ChatChoice AI API响应选择
type ChatChoice struct {
	Index   int `json:"index"`
	Message struct {
		Role    string `json:"role"`
		Content string `json:"content"`
	} `json:"message"`
	FinishReason string `json:"finish_reason"`
}

// ChatResponse AI API响应结构
type ChatResponse struct {
	ID      string       `json:"id"`
	Object  string       `json:"object"`
	Created int64        `json:"created"`
	Model   string       `json:"model"`
	Choices []ChatChoice `json:"choices"`
}

// AIService AI服务的内部实现
type AIService struct {
	cfg        *config.AIService                 // AI服务配置
	rsAPI      roomserverAPI.ClientRoomserverAPI // 房间服务器API
	userAPI    userapi.ClientUserAPI             // 用户API
	aiUserID   spec.UserID                       // AI用户ID
	responses  []string                          // 预定义回复列表
	httpClient *http.Client                      // HTTP客户端
}

// NewAIService 创建新的AI服务实例
func NewAIService(
	cfg *config.AIService,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
) *AIService {
	// 解析AI用户ID
	aiUserID, err := spec.NewUserID(cfg.AIUserID, true)
	if err != nil {
		logrus.WithError(err).Fatalf("无效的AI用户ID: %s", cfg.AIUserID)
	}

	// 预定义的AI回复
	responses := []string{
		"你好！我是AI助手，有什么可以帮助你的吗？",
		"我理解你的问题，让我想想...",
		"这是一个很有趣的话题！",
		"根据我的理解，这个问题可能需要更多的上下文。",
		"我很乐意帮助你解决这个问题。",
		"让我为你提供一些建议...",
		"这确实是一个值得思考的问题。",
		"我觉得我们可以从不同的角度来看这个问题。",
		"感谢你的提问，这让我学到了新东西！",
		"我正在处理你的请求，请稍等...",
	}

	return &AIService{
		cfg:        cfg,
		rsAPI:      rsAPI,
		userAPI:    userAPI,
		aiUserID:   *aiUserID,
		responses:  responses,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	}
}

// ProcessMessage 实现API接口 - 处理收到的消息
func (ai *AIService) ProcessMessage(ctx context.Context, req *aiapi.ProcessMessageRequest) (*aiapi.ProcessMessageResponse, error) {
	// 检查是否启用AI服务
	if !ai.cfg.Enabled {
		return &aiapi.ProcessMessageResponse{ShouldReply: false}, nil
	}

	// 检查是否是AI用户自己发送的消息（避免自己回复自己）
	if req.Sender.String() == ai.aiUserID.String() {
		logrus.Debug("跳过AI用户自己发送的消息，避免无限循环")
		return &aiapi.ProcessMessageResponse{ShouldReply: false}, nil
	}

	// 检查消息类型是否为文本消息
	if req.EventType != "m.room.message" || req.Content.MsgType != "m.text" {
		return &aiapi.ProcessMessageResponse{ShouldReply: false}, nil
	}

	// 检查消息是否包含触发词或艾特AI用户
	shouldReply := ai.shouldReplyToMessage(req.Content.Body)
	if !shouldReply {
		return &aiapi.ProcessMessageResponse{ShouldReply: false}, nil
	}

	// 生成AI回复
	replyText, err := ai.GetAIResponse(ctx, req.Content.Body, req.RoomID, req.Sender)
	if err != nil {
		logrus.WithError(err).Error("生成AI回复失败")
		replyText = "抱歉，我现在无法处理你的请求。"
	}

	return &aiapi.ProcessMessageResponse{
		ShouldReply: true,
		ReplyText:   replyText,
		AIUserID:    ai.aiUserID,
	}, nil
}

// IsAIUser 实现API接口 - 检查是否是AI用户
func (ai *AIService) IsAIUser(ctx context.Context, userID spec.UserID) bool {
	return userID.String() == ai.aiUserID.String()
}

// callExternalAI 调用外部AI API
func (ai *AIService) callExternalAI(ctx context.Context, userMessage string) (string, error) {
	// 如果没有配置API端点，使用本地回复
	if ai.cfg.APIEndpoint == "" {
		return ai.getLocalResponse(userMessage), nil
	}

	// 构建请求数据
	chatReq := ChatRequest{
		Messages: []ChatMessage{
			{
				Role:    "system",
				Content: "目前你身处一个群里，用户正在询问你相关问题，请用可爱的语气回答用户的问题",
			},
			{
				Role:    "user",
				Content: userMessage,
			},
		},
		Stream:           false,
		Model:            "opeai/qwen2.5-72b",
		Temperature:      0.5,
		PresencePenalty:  0,
		FrequencyPenalty: 0,
		TopP:             0.7,
		TopK:             1,
	}

	// 序列化请求数据
	reqBody, err := json.Marshal(chatReq)
	if err != nil {
		logrus.WithError(err).Error("序列化AI请求失败")
		return ai.getLocalResponse(userMessage), nil
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", ai.cfg.APIEndpoint, bytes.NewBuffer(reqBody))
	if err != nil {
		logrus.WithError(err).Error("创建AI请求失败")
		return ai.getLocalResponse(userMessage), nil
	}

	// 设置请求头
	req.Header.Set("accept", "application/json, text/event-stream")
	req.Header.Set("accept-language", "zh-CN,zh;q=0.9")
	req.Header.Set("authorization", "Bearer "+ai.cfg.APIKey)
	req.Header.Set("cache-control", "no-cache")
	req.Header.Set("content-type", "application/json")
	req.Header.Set("user-agent", "Dendrite-AI-Service/1.0")

	// 发送请求
	resp, err := ai.httpClient.Do(req)
	if err != nil {
		logrus.WithError(err).Error("发送AI请求失败")
		return ai.getLocalResponse(userMessage), nil
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logrus.WithError(err).Error("读取AI响应失败")
		return ai.getLocalResponse(userMessage), nil
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		logrus.WithFields(logrus.Fields{
			"status_code": resp.StatusCode,
			"response":    string(respBody),
		}).Error("AI API返回错误状态码")
		return ai.getLocalResponse(userMessage), nil
	}

	// 解析响应
	var chatResp ChatResponse
	if err := json.Unmarshal(respBody, &chatResp); err != nil {
		logrus.WithError(err).Error("解析AI响应失败")
		return ai.getLocalResponse(userMessage), nil
	}

	// 提取回复内容
	if len(chatResp.Choices) > 0 && chatResp.Choices[0].Message.Content != "" {
		content := chatResp.Choices[0].Message.Content
		logrus.WithFields(logrus.Fields{
			"user_message": userMessage,
			"ai_response":  content,
		}).Info("AI API调用成功")
		return content, nil
	}

	logrus.Warn("AI API返回空内容，使用本地回复")
	return ai.getLocalResponse(userMessage), nil
}

// getLocalResponse 获取本地回复（备用方案）
func (ai *AIService) getLocalResponse(message string) string {
	message = strings.ToLower(strings.TrimSpace(message))

	// 移除艾特符号和AI用户ID
	message = strings.ReplaceAll(message, "@"+ai.aiUserID.String(), "")
	message = strings.ReplaceAll(message, ai.aiUserID.String(), "")
	for _, trigger := range ai.cfg.Triggers {
		message = strings.ReplaceAll(message, strings.ToLower(trigger), "")
	}
	message = strings.TrimSpace(message)

	// 根据消息内容生成不同类型的回复
	var response string

	switch {
	case strings.Contains(message, "你好") || strings.Contains(message, "hello") || strings.Contains(message, "hi"):
		response = "你好！很高兴见到你！有什么我可以帮助你的吗？"

	case strings.Contains(message, "时间") || strings.Contains(message, "time"):
		response = fmt.Sprintf("现在的时间是 %s", time.Now().Format("2006-01-02 15:04:05"))

	case strings.Contains(message, "天气") || strings.Contains(message, "weather"):
		response = "抱歉，我目前还无法获取实时天气信息。你可以查看天气应用或网站获取准确的天气预报。"

	case strings.Contains(message, "帮助") || strings.Contains(message, "help"):
		response = "我是AI助手，可以回答一些基本问题。你可以问我关于时间、简单的计算、或者只是想聊天。试试艾特我并说'你好'！"

	case strings.Contains(message, "谢谢") || strings.Contains(message, "thank"):
		response = "不客气！很高兴能帮助你。如果还有其他问题，随时可以问我。"

	case strings.Contains(message, "再见") || strings.Contains(message, "bye"):
		response = "再见！希望我们下次还能聊天。祝你有美好的一天！"

	case len(message) == 0:
		response = "你好！我注意到你艾特了我，有什么可以帮助你的吗？"

	default:
		// 随机选择一个通用回复
		rand.Seed(time.Now().UnixNano())
		response = ai.responses[rand.Intn(len(ai.responses))]

		// 如果消息较长，添加更个性化的回复
		if len(message) > 20 {
			response += fmt.Sprintf(" 关于你提到的'%s'，这确实是个有趣的话题。",
				ai.truncateString(message, 30))
		}
	}

	return response
}

// GetAIResponse 实现API接口 - 生成AI回复
func (ai *AIService) GetAIResponse(ctx context.Context, message string, roomID spec.RoomID, senderID spec.UserID) (string, error) {
	// 清理消息内容
	cleanMessage := strings.TrimSpace(message)

	// 移除艾特符号和AI用户ID
	cleanMessage = strings.ReplaceAll(cleanMessage, "@"+ai.aiUserID.String(), "")
	cleanMessage = strings.ReplaceAll(cleanMessage, ai.aiUserID.String(), "")
	for _, trigger := range ai.cfg.Triggers {
		cleanMessage = strings.ReplaceAll(cleanMessage, trigger, "")
	}
	cleanMessage = strings.TrimSpace(cleanMessage)

	// 调用外部AI API
	response, err := ai.callExternalAI(ctx, cleanMessage)
	if err != nil {
		logrus.WithError(err).Error("调用外部AI失败，使用本地回复")
		response = ai.getLocalResponse(cleanMessage)
	}

	// 确保回复不超过最大长度
	if len(response) > ai.cfg.MaxLength {
		response = ai.truncateString(response, ai.cfg.MaxLength-3) + "..."
	}

	return response, nil
}

// shouldReplyToMessage 检查是否应该回复消息
func (ai *AIService) shouldReplyToMessage(message string) bool {
	message = strings.ToLower(message)

	// 检查是否艾特了AI用户
	if strings.Contains(message, "@"+ai.aiUserID.String()) ||
		strings.Contains(message, ai.aiUserID.String()) {
		return true
	}

	// 检查是否包含触发词
	for _, trigger := range ai.cfg.Triggers {
		if strings.Contains(message, strings.ToLower(trigger)) {
			return true
		}
	}

	return false
}

// truncateString 截断字符串到指定长度
func (ai *AIService) truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen]
}

// SendAIMessage 发送AI消息到房间
func (ai *AIService) SendAIMessage(ctx context.Context, roomID spec.RoomID, message string) error {
	// 构造消息内容
	content := map[string]interface{}{
		"msgtype": "m.text",
		"body":    message,
	}

	_, err := json.Marshal(content)
	if err != nil {
		return fmt.Errorf("序列化消息内容失败: %w", err)
	}

	// 注意：这里需要实际的发送消息实现
	// 在真实实现中，需要调用适当的API来发送消息
	logrus.Infof("AI用户 %s 在房间 %s 中发送消息: %s",
		ai.aiUserID.String(), roomID.String(), message)

	return nil
}
