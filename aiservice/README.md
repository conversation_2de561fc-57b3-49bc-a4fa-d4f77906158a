# Dendrite AI 服务

Dendrite AI 服务为 Matrix 服务器提供智能对话功能。当用户在房间中艾特 AI 用户或使用特定触发词时，AI 会自动生成并发送回复。

## 功能特性

- **智能对话**: 基于规则的 AI 回复系统
- **触发词检测**: 支持多种触发词和艾特检测
- **延迟模拟**: 模拟真实用户的打字时间
- **可配置**: 灵活的配置选项
- **多语言支持**: 支持中文和英文交互

## 配置

在 `dendrite.yaml` 配置文件中添加以下配置：

```yaml
# AI 服务配置
ai_service:
  # 是否启用 AI 服务
  enabled: true
  
  # AI 用户的 Matrix ID
  ai_user_id: "@ai:yourdomain.com"
  
  # AI 用户显示名称
  ai_user_name: "AI Assistant"
  
  # 触发 AI 回复的关键词列表
  triggers:
    - "@ai"
    - "ai"
    - "助手"
    - "AI"
  
  # 最大回复长度
  max_length: 1000
  
  # 回复延迟（毫秒），模拟真实用户的打字时间
  reply_delay: 1000
  
  # 是否在房间中自动加入 AI 用户
  auto_join_rooms: false
```

## 使用方法

### 1. 启用 AI 服务

在配置文件中设置 `ai_service.enabled: true`

### 2. 创建 AI 用户

AI 用户会自动创建，用户 ID 由配置中的 `ai_user_id` 指定。

### 3. 与 AI 对话

在任何房间中，您可以通过以下方式触发 AI 回复：

#### 艾特 AI 用户
```
@ai:yourdomain.com 你好！
```

#### 使用触发词
```
ai 现在几点了？
助手 帮我解答一个问题
```

### 4. AI 回复类型

AI 会根据消息内容生成不同类型的回复：

- **问候**: 检测到 "你好"、"hello"、"hi" 等
- **时间查询**: 检测到 "时间"、"time" 等
- **帮助请求**: 检测到 "帮助"、"help" 等
- **感谢**: 检测到 "谢谢"、"thank" 等
- **告别**: 检测到 "再见"、"bye" 等
- **通用回复**: 其他情况下的随机回复

## 架构说明

### 组件结构

```
aiservice/
├── api/                    # API 接口定义
│   └── api.go
├── internal/               # 内部实现
│   └── ai.go
├── consumers/              # 消息消费者
│   └── roomserver.go
├── aiservice.go           # 主入口
├── aiservice_start.go     # 启动逻辑
└── README.md              # 文档
```

### 工作流程

1. **消息监听**: `OutputRoomEventConsumer` 监听所有房间事件
2. **消息过滤**: 只处理文本消息类型的事件
3. **触发检测**: 检查消息是否包含触发词或艾特 AI 用户
4. **AI 处理**: `AIService` 生成适当的回复
5. **消息发送**: 将 AI 回复发送到房间

### 集成点

- **房间服务器**: 监听房间事件流
- **用户 API**: 管理 AI 用户身份
- **客户端 API**: 发送 AI 回复消息
- **配置系统**: 读取 AI 服务配置

## 扩展开发

### 添加新的回复类型

在 `aiservice/internal/ai.go` 的 `GetAIResponse` 方法中添加新的条件判断：

```go
case strings.Contains(message, "天气") || strings.Contains(message, "weather"):
    response = "抱歉，我目前还无法获取实时天气信息。"
```

### 集成外部 AI API

可以在配置中设置 `api_endpoint` 和 `api_key`，然后在 `GetAIResponse` 方法中调用外部 AI 服务：

```go
if ai.cfg.APIEndpoint != "" {
    // 调用外部 AI API
    response, err = ai.callExternalAPI(message)
    if err != nil {
        // 降级到内置回复
        response = ai.getBuiltinResponse(message)
    }
}
```

### 添加更多触发条件

可以扩展 `shouldReplyToMessage` 方法来支持更复杂的触发逻辑：

```go
// 检查消息长度
if len(message) > 100 {
    return true // 长消息总是触发回复
}

// 检查是否包含问号
if strings.Contains(message, "?") || strings.Contains(message, "？") {
    return true // 问题总是触发回复
}
```

## 注意事项

1. **性能**: AI 服务会监听所有房间事件，在高流量服务器上可能需要优化
2. **权限**: 确保 AI 用户有适当的权限加入和发送消息到房间
3. **速率限制**: 考虑添加速率限制以防止 AI 回复过于频繁
4. **存储**: 当前实现不存储对话历史，每次回复都是独立的

## 故障排除

### AI 服务未启动

检查配置文件中 `ai_service.enabled` 是否设置为 `true`。

### AI 不回复消息

1. 检查触发词配置是否正确
2. 确认消息类型为 `m.text`
3. 查看日志中是否有错误信息

### AI 用户无法发送消息

1. 确认 AI 用户已正确创建
2. 检查 AI 用户是否有房间发送权限
3. 验证房间服务器 API 连接是否正常

## 日志

AI 服务会输出以下类型的日志：

- `INFO`: 服务启动、消息处理状态
- `ERROR`: 错误信息，如消息解析失败、API 调用失败等
- `DEBUG`: 详细的处理流程信息

启用调试日志可以帮助诊断问题：

```yaml
logging:
  - type: std
    level: debug