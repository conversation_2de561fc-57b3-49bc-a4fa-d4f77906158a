// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// AI服务房间服务器消费者 - 监听房间事件并处理AI回复
package consumers

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"net/http"
	"strings"
	"time"

	"github.com/element-hq/dendrite/aiservice/api"
	"github.com/element-hq/dendrite/clientapi/routing"
	"github.com/element-hq/dendrite/internal/transactions"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/element-hq/dendrite/setup/jetstream"
	"github.com/element-hq/dendrite/setup/process"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/nats-io/nats.go"
	"github.com/sirupsen/logrus"
)

// OutputRoomEventConsumer 消费房间事件并处理AI回复
type OutputRoomEventConsumer struct {
	ctx       context.Context
	jetstream nats.JetStreamContext
	durable   string
	topic     string
	aiAPI     api.AIServiceInternalAPI
	rsAPI     roomserverAPI.ClientRoomserverAPI
	userAPI   userapi.ClientUserAPI
	cfg       *config.AIService
	clientCfg *config.ClientAPI
	txnCache  *transactions.Cache
}

// NewOutputRoomEventConsumer 创建新的房间事件消费者
func NewOutputRoomEventConsumer(
	process *process.ProcessContext,
	cfg *config.AIService,
	js nats.JetStreamContext,
	aiAPI api.AIServiceInternalAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
	clientCfg *config.ClientAPI,
	txnCache *transactions.Cache,
) *OutputRoomEventConsumer {
	return &OutputRoomEventConsumer{
		ctx:       process.Context(),
		jetstream: js,
		durable:   cfg.Matrix.JetStream.Prefixed("AIServiceRoomServerConsumer"),
		topic:     cfg.Matrix.JetStream.Prefixed(jetstream.OutputRoomEvent),
		aiAPI:     aiAPI,
		rsAPI:     rsAPI,
		userAPI:   userAPI,
		cfg:       cfg,
		clientCfg: clientCfg,
		txnCache:  txnCache,
	}
}

// Start 开始消费房间事件
func (s *OutputRoomEventConsumer) Start() error {
	if !s.cfg.Enabled {
		logrus.Info("AI服务未启用，跳过房间事件消费者启动")
		return nil
	}

	logrus.Info("启动AI服务房间事件消费者")
	return jetstream.JetStreamConsumer(
		s.ctx, s.jetstream, s.topic, s.durable, 1,
		s.onMessage, nats.DeliverAll(), nats.ManualAck(),
	)
}

// onMessage 处理接收到的房间事件消息
func (s *OutputRoomEventConsumer) onMessage(ctx context.Context, msgs []*nats.Msg) bool {
	msg := msgs[0] // 批量大小为1，所以只有一条消息
	defer func() {
		if err := msg.Ack(); err != nil {
			logrus.WithError(err).Error("确认NATS消息失败")
		}
	}()

	// 解析房间事件
	var output roomserverAPI.OutputEvent
	if err := json.Unmarshal(msg.Data, &output); err != nil {
		logrus.WithError(err).Error("解析房间事件失败")
		return true
	}
	logrus.Info(fmt.Sprintf("内容 %+v", output))

	// 只处理新的房间事件
	if output.Type != roomserverAPI.OutputTypeNewRoomEvent {
		return true
	}

	event := output.NewRoomEvent.Event
	eventType := event.Type()
	logrus.Info(fmt.Sprintf("内容event  %+v", eventType))
	
	// 处理消息事件和加密消息事件
	if eventType != "m.room.message" && eventType != "m.room.encrypted" {
		logrus.Debug(fmt.Sprintf("跳过非消息事件: %s", eventType))
		return true
	}
	 
	// 如果是加密消息，记录但暂时跳过处理 // j
	if eventType == "m.room.encrypted" {
		logrus.Info("检测到加密消息，AI服务暂不支持加密消息处理")
		logrus.Info("建议：请在Matrix客户端中关闭房间加密，或创建一个未加密的房间来测试AI功能")
		return true
	}
/**
  看到事件类型是 m.room.encrypted 说明房间启用了端到端加密。这是Matrix的安全特性，当房间开启加密时，所有消息都会被加密成 m.room.encrypted 类型。

让我修改代码来处理加密消息：
*/
	// 解析消息内容
	var content api.MessageContent
	if err := json.Unmarshal(event.Content(), &content); err != nil {
		logrus.WithError(err).Error("解析消息内容失败")
		return true
	}

	// 只处理文本消息
	if content.MsgType != "m.text" {
		return true
	}

	// 获取发送者ID和房间ID
	senderID := event.SenderID()
	roomID := event.RoomID()

	// 转换为用户ID（如果需要）
	var userID spec.UserID
	if senderIDStr := string(senderID); senderIDStr != "" {
		parsedUserID, err := spec.NewUserID(senderIDStr, true)
		if err != nil {
			logrus.WithError(err).Error("解析发送者ID失败")
			return true
		}
		userID = *parsedUserID
	} else {
		logrus.Error("发送者ID为空")
		return true
	}

	// 创建处理消息请求
	req := &api.ProcessMessageRequest{
		RoomID:    roomID,
		EventID:   event.EventID(),
		Sender:    userID,
		EventType: event.Type(),
		Content:   content,
		Timestamp: time.Now(),
	}

	// 处理消息
	resp, err := s.aiAPI.ProcessMessage(ctx, req)
	if err != nil {
		logrus.WithError(err).Error("AI处理消息失败")
		return true
	}

	// 如果需要回复，发送AI回复
	if resp.ShouldReply {
		go s.sendAIReply(ctx, roomID, resp.ReplyText, resp.AIUserID)
	}

	return true
}

// sendAIReply 发送AI回复消息
func (s *OutputRoomEventConsumer) sendAIReply(ctx context.Context, roomID spec.RoomID, replyText string, aiUserID spec.UserID) {
	// 添加延迟以模拟真实用户的打字时间
	if s.cfg.ReplyDelay > 0 {
		time.Sleep(time.Duration(s.cfg.ReplyDelay) * time.Millisecond)
	}

	// 首先确保AI用户已加入房间
	if err := s.ensureAIUserInRoom(ctx, roomID, aiUserID); err != nil {
		logrus.WithError(err).Error("确保AI用户加入房间失败")
		return
	}

	logrus.Infof("AI用户 %s 在房间 %s 中回复: %s", aiUserID.String(), roomID.String(), replyText)

	// 构造消息内容
	content := map[string]interface{}{
		"msgtype": "m.text",
		"body":    replyText,
	}

	// 创建设备信息（模拟AI用户的设备）
	device := &userapi.Device{
		UserID:      aiUserID.String(),
		ID:          "AI_DEVICE",
		DisplayName: "AI Assistant Device",
		SessionID:   generateSessionID(aiUserID.String()),
		AccessToken: "ai_access_token_" + aiUserID.String(),
	}

	// 创建模拟的HTTP请求
	reqBody, err := json.Marshal(content)
	if err != nil {
		logrus.WithError(err).Error("序列化AI回复内容失败")
		return
	}

	// 创建HTTP请求对象
	req, err := http.NewRequestWithContext(ctx, "PUT", "/", strings.NewReader(string(reqBody)))
	if err != nil {
		logrus.WithError(err).Error("创建HTTP请求失败")
		return
	}
	req.Header.Set("Content-Type", "application/json")

	// 调用 SendEvent 函数发送消息
	response := routing.SendEvent(
		req,              // HTTP请求
		device,           // AI设备信息
		roomID.String(),  // 房间ID
		"m.room.message", // 事件类型
		nil,              // 事务ID（可选）
		nil,              // 状态键（消息事件不需要）
		s.clientCfg,      // 客户端API配置
		s.rsAPI,          // 房间服务器API
		s.txnCache,       // 事务缓存
	)

	// 检查发送结果
	if response.Code == http.StatusOK {
		logrus.WithFields(logrus.Fields{
			"room_id":  roomID.String(),
			"ai_user":  aiUserID.String(),
			"message":  replyText,
			"response": response.JSON,
		}).Info("AI回复发送成功")
	} else {
		logrus.WithFields(logrus.Fields{
			"room_id": roomID.String(),
			"ai_user": aiUserID.String(),
			"message": replyText,
			"code":    response.Code,
			"error":   response.JSON,
		}).Error("AI回复发送失败")
	}
}

// ensureAIUserInRoom 确保AI用户已加入房间
func (s *OutputRoomEventConsumer) ensureAIUserInRoom(ctx context.Context, roomID spec.RoomID, aiUserID spec.UserID) error {
	// 检查AI用户是否已在房间中
	membershipReq := &roomserverAPI.QueryMembershipForUserRequest{
		RoomID: roomID.String(),
		UserID: aiUserID,
	}
	membershipResp := &roomserverAPI.QueryMembershipForUserResponse{}
	
	if err := s.rsAPI.QueryMembershipForUser(ctx, membershipReq, membershipResp); err != nil {
		logrus.WithError(err).Error("查询AI用户房间成员身份失败")
		return err
	}
	
	// 如果AI用户已经在房间中，直接返回
	if membershipResp.IsInRoom {
		logrus.Debug(fmt.Sprintf("AI用户 %s 已在房间 %s 中", aiUserID.String(), roomID.String()))
		return nil
	}
	
	// AI用户不在房间中，尝试加入房间
	logrus.Info(fmt.Sprintf("AI用户 %s 不在房间 %s 中，尝试加入", aiUserID.String(), roomID.String()))
	
	// 执行加入房间操作
	joinReq := &roomserverAPI.PerformJoinRequest{
		RoomIDOrAlias: roomID.String(),
		UserID:       aiUserID.String(),
		Content: map[string]interface{}{
			"membership": "join",
		},
	}
	
	roomIDResult, _, err := s.rsAPI.PerformJoin(ctx, joinReq)
	if err != nil {
		logrus.WithError(err).Error("AI用户加入房间失败")
		return fmt.Errorf("AI用户加入房间失败: %w", err)
	}
	
	logrus.Info(fmt.Sprintf("AI用户 %s 成功加入房间 %s (返回房间ID: %s)", aiUserID.String(), roomID.String(), roomIDResult))
	return nil
}

// generateSessionID 生成基于用户ID的唯一SessionID
func generateSessionID(userID string) int64 {
	h := fnv.New64a()
	h.Write([]byte(userID))
	return int64(h.Sum64())
}
