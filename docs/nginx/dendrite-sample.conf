# change IP to location of monolith server
upstream monolith {
    server 127.0.0.1:8008;
}
server {
    listen 443 ssl; # IPv4
    listen [::]:443 ssl; # IPv6
    server_name my.hostname.com;

    ssl_certificate /path/to/fullchain.pem;
    ssl_certificate_key /path/to/privkey.pem;
    ssl_dhparam /path/to/ssl-dhparams.pem;

    proxy_set_header Host      $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_read_timeout         600;

    location /.well-known/matrix/server {
        return 200 '{ "m.server": "my.hostname.com:443" }';
    }

    location /.well-known/matrix/client {
        # If your server_name here doesn't match your matrix homeserver URL
        # (e.g. hostname.com as server_name and matrix.hostname.com as homeserver URL)
        # uncomment the following line.
        # add_header Access-Control-Allow-Origin '*';
        return 200 '{ "m.homeserver": { "base_url": "https://my.hostname.com" } }';
    }

    location /_matrix {
        proxy_pass http://monolith;
    }
}
