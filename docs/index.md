---
layout: home
nav_exclude: true
---

# Dendrite

Dendrite is a second-generation Matrix homeserver written in Go! Following the microservice
architecture model, Dendrite is designed to be efficient, reliable and scalable. Despite being beta,
many Matrix features are already supported.

This site aims to include relevant documentation to help you to get started with and
run Dendrite. Check out the following sections:

* **[Installation](installation.md)** for building and deploying your own Dendrite homeserver
* **[Administration](administration.md)** for managing an existing Dendrite deployment
* **[Development](development.md)** for developing against Dendrite

You can also join us in our Matrix rooms dedicated to Dendrite, but please check first that
your question hasn't already been [answered in the FAQ](FAQ.md):

* **[#dendrite:matrix.org](https://matrix.to/#/#dendrite:matrix.org)** for general project discussion and support
* **[#dendrite-dev:matrix.org](https://matrix.to/#/#dendrite-dev:matrix.org)** for chat on Dendrite development specifically
* **[#dendrite-alerts:matrix.org](https://matrix.to/#/#dendrite-alerts:matrix.org)** for release notifications and other important announcements
