# Depending on which port is used for federation (.well-known/matrix/server or SRV record),
# ensure there's a binding for that port in the configuration. Replace "FEDPORT" with port
# number, (e.g. "8448"), and "IPV4" with your server's ipv4 address (separate binding for
# each ip address, e.g. if you use both ipv4 and ipv6 addresses).

Binding {
        Port = FEDPORT
        Interface = IPV4
        TLScertFile = /path/to/fullchainandprivkey.pem
}

VirtualHost {
        ...
        ReverseProxy = /_matrix http://localhost:8008 600
        ...

}
