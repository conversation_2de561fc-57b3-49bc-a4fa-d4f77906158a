# Dendrite 阅后即焚消息配置示例

# 全局配置
global:
  server_name: "example.com"
  private_key: "matrix_key.pem"
  key_id: "ed25519:1"  # 密钥ID，用于签名事件
  
# 房间服务器配置
room_server:
  # 数据库配置
  database:
    connection_string: "postgresql://dendrite:password@localhost/dendrite_roomserver?sslmode=disable"
  
  # 默认房间版本
  default_room_version: "10"
  
  # 阅后即焚消息配置
  expiring_messages:
    # 是否启用阅后即焚功能（默认: true）
    enabled: true
    
    # 检查过期消息的间隔（默认: 30s）
    # 支持的时间单位: ns, us, ms, s, m, h
    check_interval: "30s"
    
    # 每次处理的最大消息数量（默认: 100）
    # 较大的值可以提高处理效率，但会增加内存使用
    batch_size: 100
    
    # 保留已处理记录的天数（默认: 7）
    # 用于审计和调试，过期后会自动清理
    cleanup_days: 7
    
    # 最大过期时间限制，单位：天（默认: 30）
    # 防止客户端设置过长的过期时间
    max_expire_days: 30

# 客户端 API 配置
client_api:
  # 其他客户端 API 配置...
  
# 联邦 API 配置  
federation_api:
  # 其他联邦 API 配置...

# 媒体 API 配置
media_api:
  # 其他媒体 API 配置...

# 同步 API 配置
sync_api:
  # 其他同步 API 配置...

# 用户 API 配置
user_api:
  # 其他用户 API 配置...

# 应用服务 API 配置
app_service_api:
  # 其他应用服务 API 配置...

# 日志配置
logging:
  - type: "std"
    level: "info"
  - type: "file"
    level: "warn"
    params:
      path: "/var/log/dendrite"

# 示例配置说明：
#
# 重要：阅后即焚功能需要正确的签名配置才能工作
# global.server_name: 服务器名称，必须与实际域名匹配
# global.private_key: 私钥文件路径，用于签名撤回事件
# global.key_id: 密钥ID，通常为 "ed25519:1"
#
# 1. check_interval:
#    - 设置为较小值（如 10s）可以更快处理过期消息，但会增加 CPU 使用
#    - 设置为较大值（如 5m）可以减少资源使用，但过期消息处理会有延迟
#
# 2. batch_size:
#    - 设置为较大值可以提高批量处理效率
#    - 但要考虑内存使用和数据库性能
#
# 3. cleanup_days:
#    - 设置为较大值可以保留更多审计记录
#    - 但会占用更多数据库空间
#
# 4. max_expire_days:
#    - 建议根据业务需求设置合理的上限
#    - 过长的过期时间可能影响数据库性能

# 性能调优建议：
#
# 高频使用场景：
# expiring_messages:
#   check_interval: "10s"
#   batch_size: 200
#   cleanup_days: 3
#   max_expire_days: 7
#
# 低频使用场景：
# expiring_messages:
#   check_interval: "2m"
#   batch_size: 50
#   cleanup_days: 14
#   max_expire_days: 90
#
# 生产环境建议：
# expiring_messages:
#   check_interval: "30s"
#   batch_size: 100
#   cleanup_days: 7
#   max_expire_days: 30
