image:
  # -- Docker repository/image to use
  repository: "ghcr.io/element-hq/dendrite-monolith"
  # -- Kubernetes pullPolicy
  pullPolicy: IfNotPresent
  # -- Overrides the image tag whose default is the chart appVersion.
  tag: ""

# -- Configure image pull secrets to use private container registry
# https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/#create-a-pod-that-uses-your-secret
imagePullSecrets: []
# - name: your-pull-secret-name

# signing key to use
signing_key:
  # -- Create a new signing key, if not exists
  create: true
  # -- Use an existing secret
  existingSecret: ""

# -- Default resource requests/limits.
# @default -- sets some sane default values
resources:
  requests:
    memory: "512Mi"
  limits:
    memory: "4096Mi"

persistence:
  # -- The storage class to use for volume claims.
  # Used unless specified at the specific component.
  # Defaults to the cluster default storage class.
  # If defined, storageClassName: <storageClass>
  # If set to "-", storageClassName: "", which disables dynamic provisioning
  # If undefined (the default) or set to null, no storageClassName spec is
  #   set, choosing the default provisioner.  (gp2 on AWS, standard on
  #   GKE, AWS & OpenStack)
  #
  storageClass:
  jetstream:
    # -- Use an existing volume claim for jetstream
    existingClaim: ""
    # -- PVC Storage Request for the jetstream volume
    capacity: "1Gi"
    # -- The storage class to use for volume claims.
    # Defaults to persistence.storageClass
    # If defined, storageClassName: <storageClass>
    # If set to "-", storageClassName: "", which disables dynamic provisioning
    # If undefined (the default) or set to null, no storageClassName spec is
    #   set, choosing the default provisioner.  (gp2 on AWS, standard on
    #   GKE, AWS & OpenStack)
    storageClass:
  media:
    # -- Use an existing volume claim for media files
    existingClaim: ""
    # -- PVC Storage Request for the media volume
    capacity: "1Gi"
    # -- The storage class to use for volume claims.
    # Defaults to persistence.storageClass
    # If defined, storageClassName: <storageClass>
    # If set to "-", storageClassName: "", which disables dynamic provisioning
    # If undefined (the default) or set to null, no storageClassName spec is
    #   set, choosing the default provisioner.  (gp2 on AWS, standard on
    #   GKE, AWS & OpenStack)
    storageClass:
  search:
    # -- Use an existing volume claim for the fulltext search index
    existingClaim: ""
    # -- PVC Storage Request for the search volume
    capacity: "1Gi"
    # -- The storage class to use for volume claims.
    # Defaults to persistence.storageClass
    # If defined, storageClassName: <storageClass>
    # If set to "-", storageClassName: "", which disables dynamic provisioning
    # If undefined (the default) or set to null, no storageClassName spec is
    #   set, choosing the default provisioner.  (gp2 on AWS, standard on
    #   GKE, AWS & OpenStack)
    storageClass:

# -- Add additional arguments to the dendrite command
extraArgs: []

# -- Add additional volumes to the Dendrite Pod
extraVolumes: []
# ex.
# - name: extra-config
#   secret:
#     secretName: extra-config

# -- Configure additional mount points volumes in the Dendrite Pod
extraVolumeMounts: []
# ex.
# - mountPath: /etc/dendrite/extra-config
#   name: extra-config

strategy:
  # -- Strategy to use for rolling updates (e.g. Recreate, RollingUpdate)
  # If you are using ReadWriteOnce volumes, you should probably use Recreate
  type: Recreate
  rollingUpdate:
    # -- Maximum number of pods that can be unavailable during the update process
    maxUnavailable: 25%
    # -- Maximum number of pods that can be scheduled above the desired number of pods
    maxSurge: 25%

# -- Node selector configuration
nodeSelector: {}

# -- Tolerations configuration
tolerations: {}

# -- Affinity configuration
affinity: {}

dendrite_config:
  version: 2
  global:
    # -- **REQUIRED** Servername for this Dendrite deployment.
    server_name: ""

    # -- The private key to use. (**NOTE**: This is overriden in Helm)
    private_key: /etc/dendrite/secrets/signing.key

    # -- The server name to delegate server-server communications to, with optional port
    # e.g. localhost:443
    well_known_server_name: ""

    # -- The server name to delegate client-server communications to, with optional port
    # e.g. localhost:443
    well_known_client_name: ""

    # -- Lists of domains that the server will trust as identity servers to verify third
    # party identifiers such as phone numbers and email addresses.
    trusted_third_party_id_servers:
      - matrix.org
      - vector.im

    # -- The paths and expiry timestamps (as a UNIX timestamp in millisecond precision)
    # to old signing keys that were formerly in use on this domain name. These
    # keys will not be used for federation request or event signing, but will be
    # provided to any other homeserver that asks when trying to verify old events.
    old_private_keys:
    #  If the old private key file is available:
    #  - private_key: old_matrix_key.pem
    #    expired_at: 1601024554498
    #  If only the public key (in base64 format) and key ID are known:
    #  - public_key: mn59Kxfdq9VziYHSBzI7+EDPDcBS2Xl7jeUdiiQcOnM=
    #    key_id: ed25519:mykeyid
    #    expired_at: 1601024554498

    # -- Disable federation. Dendrite will not be able to make any outbound HTTP requests
    # to other servers and the federation API will not be exposed.
    disable_federation: false

    key_validity_period: 168h0m0s

    database:
      # -- The connection string for connections to Postgres.
      # This will be set automatically if using the Postgres dependency
      connection_string: ""

      # -- Default database maximum open connections
      max_open_conns: 90
      # -- Default database maximum idle connections
      max_idle_conns: 5
      # -- Default database maximum lifetime
      conn_max_lifetime: -1

    jetstream:
      # -- Persistent directory to store JetStream streams in.
      storage_path: "/data/jetstream"
      # -- NATS JetStream server addresses if not using internal NATS.
      addresses: []
      # -- The prefix for JetStream streams
      topic_prefix: "Dendrite"
      # -- Keep all data in memory. (**NOTE**: This is overriden in Helm to `false`)
      in_memory: false
      # -- Disables TLS validation. This should **NOT** be used in production.
      disable_tls_validation: true

    cache:
      # -- The estimated maximum size for the global cache in bytes, or in terabytes,
      # gigabytes, megabytes or kilobytes when the appropriate 'tb', 'gb', 'mb' or
      # 'kb' suffix is specified. Note that this is not a hard limit, nor is it a
      # memory limit for the entire process. A cache that is too small may ultimately
      # provide little or no benefit.
      max_size_estimated: 1gb
      # -- The maximum amount of time that a cache entry can live for in memory before
      # it will be evicted and/or refreshed from the database. Lower values result in
      # easier admission of new cache entries but may also increase database load in
      # comparison to higher values, so adjust conservatively. Higher values may make
      # it harder for new items to make it into the cache, e.g. if new rooms suddenly
      # become popular.
      max_age: 1h

    report_stats:
      # -- Configures phone-home statistics reporting. These statistics contain the server
      # name, number of active users and some information on your deployment config.
      # We use this information to understand how Dendrite is being used in the wild.
      enabled: false
      # -- Endpoint to report statistics to.
      endpoint: https://matrix.org/report-usage-stats/push

    presence:
      # -- Controls whether we receive presence events from other servers
      enable_inbound: false
      # -- Controls whether we send presence events for our local users to other servers.
      # (_May increase CPU/memory usage_)
      enable_outbound: false

    server_notices:
      # -- Server notices allows server admins to send messages to all users on the server.
      enabled: false
      # -- The local part for the user sending server notices.
      local_part: "_server"
      # -- The display name for the user sending server notices.
      display_name: "Server Alerts"
      # -- The avatar URL (as a mxc:// URL) name for the user sending server notices.
      avatar_url: ""
      # The room name to be used when sending server notices. This room name will
      # appear in user clients.
      room_name: "Server Alerts"

    # prometheus metrics
    metrics:
      # -- Whether or not Prometheus metrics are enabled.
      enabled: false
      # HTTP basic authentication to protect access to monitoring.
      basic_auth:
        # -- HTTP basic authentication username
        user: "metrics"
        # -- HTTP basic authentication password
        password: metrics

    dns_cache:
      # -- Whether or not the DNS cache is enabled.
      enabled: false
      # -- Maximum number of entries to hold in the DNS cache
      cache_size: 256
      # -- Duration for how long DNS cache items should be considered valid ([see time.ParseDuration](https://pkg.go.dev/time#ParseDuration) for more)
      cache_lifetime: "10m"

    profiling:
      # -- Enable pprof. You will need to manually create a port forwarding to the deployment to access PPROF,
      # as it will only listen on localhost and the defined port.
      # e.g. `kubectl port-forward deployments/dendrite 65432:65432`
      enabled: false
      # -- pprof port, if enabled
      port: 65432

  # -- Configuration for experimental MSC's. (Valid values are: msc2836)
  mscs:
    mscs:
      []
      # A list of enabled MSC's
      # Currently valid values are:
      # - msc2836    (Threading, see https://github.com/matrix-org/matrix-doc/pull/2836)

  app_service_api:
    # -- Disable the validation of TLS certificates of appservices. This is
    # not recommended in production since it may allow appservice traffic
    # to be sent to an insecure endpoint.
    disable_tls_validation: false
    # -- Appservice config files to load on startup. (**NOTE**: This is overriden by Helm, if a folder `./appservices/` exists)
    config_files: []

  client_api:
    # -- Prevents new users from being able to register on this homeserver, except when
    # using the registration shared secret below.
    registration_disabled: true

    # Prevents new guest accounts from being created. Guest registration is also
    # disabled implicitly by setting 'registration_disabled' above.
    guests_disabled: true

    # -- If set, allows registration by anyone who knows the shared secret, regardless of
    # whether registration is otherwise disabled.
    registration_shared_secret: ""

    # -- enable reCAPTCHA registration
    enable_registration_captcha: false
    # -- reCAPTCHA public key
    recaptcha_public_key: ""
    # -- reCAPTCHA private key
    recaptcha_private_key: ""
    # -- reCAPTCHA bypass secret
    recaptcha_bypass_secret: ""
    recaptcha_siteverify_api: ""

    # TURN server information that this homeserver should send to clients.
    turn:
      # -- Duration for how long users should be considered valid ([see time.ParseDuration](https://pkg.go.dev/time#ParseDuration) for more)
      turn_user_lifetime: "24h"
      turn_uris: []
      turn_shared_secret: ""
      # -- The TURN username
      turn_username: ""
      # -- The TURN password
      turn_password: ""

    rate_limiting:
      # -- Enable rate limiting
      enabled: true
      # -- After how many requests a rate limit should be activated
      threshold: 20
      # -- Cooloff time in milliseconds
      cooloff_ms: 500
      # -- Users which should be exempt from rate limiting
      exempt_user_ids:

  federation_api:
    # -- Federation failure threshold. How many consecutive failures that we should
    # tolerate when sending federation requests to a specific server. The backoff
    # is 2**x seconds, so 1 = 2 seconds, 2 = 4 seconds, 3 = 8 seconds, etc.
    # The default value is 16 if not specified, which is circa 18 hours.
    send_max_retries: 16
    # -- Disable TLS validation. This should **NOT** be used in production.
    disable_tls_validation: false
    prefer_direct_fetch: false
    # -- Prevents Dendrite from keeping HTTP connections
    # open for reuse for future requests. Connections will be closed quicker
    # but we may spend more time on TLS handshakes instead.
    disable_http_keepalives: false
    # -- Perspective keyservers, to use as a backup when direct key fetch
    # requests don't succeed.
    # @default -- See value.yaml
    key_perspectives:
      - server_name: matrix.org
        keys:
          - key_id: ed25519:auto
            public_key: Noi6WqcDj0QmPxCNQqgezwTlBKrfqehY1u2FyWP9uYw
          - key_id: ed25519:a_RXGa
            public_key: l8Hft5qXKn1vfHrg3p4+W8gELQVo8N13JkluMfmn2sQ

  media_api:
    # -- The path to store media files (e.g. avatars) in
    base_path: "/data/media_store"
    # -- The max file size for uploaded media files
    max_file_size_bytes: 10485760
    # Whether to dynamically generate thumbnails if needed.
    dynamic_thumbnails: false
    # -- The maximum number of simultaneous thumbnail generators to run.
    max_thumbnail_generators: 10
    # -- A list of thumbnail sizes to be generated for media content.
    # @default -- See value.yaml
    thumbnail_sizes:
      - width: 32
        height: 32
        method: crop
      - width: 96
        height: 96
        method: crop
      - width: 640
        height: 480
        method: scale

  sync_api:
    # -- This option controls which HTTP header to inspect to find the real remote IP
    # address of the client. This is likely required if Dendrite is running behind
    # a reverse proxy server.
    real_ip_header: X-Real-IP
    # -- Configuration for the full-text search engine.
    search:
      # -- Whether fulltext search is enabled.
      enabled: true
      # -- The path to store the search index in.
      index_path: "/data/search"
      # -- The language most likely to be used on the server - used when indexing, to
      # ensure the returned results match expectations. A full list of possible languages
      # can be found [here](https://github.com/element-hq/dendrite/blob/76db8e90defdfb9e61f6caea8a312c5d60bcc005/internal/fulltext/bleve.go#L25-L46)
      language: "en"

  user_api:
    # -- bcrypt cost to use when hashing passwords.
    # (ranges from 4-31; 4 being least secure, 31 being most secure; _NOTE: Using a too high value can cause clients to timeout and uses more CPU._)
    bcrypt_cost: 10
    # -- OpenID Token lifetime in milliseconds.
    openid_token_lifetime_ms: 3600000
    # - Disable TLS validation when hitting push gateways. This should **NOT** be used in production.
    push_gateway_disable_tls_validation: false
    # -- Rooms to join users to after registration
    auto_join_rooms: []

  # -- Default logging configuration
  logging:
    - type: std
      level: info

postgresql:
  # -- Enable and configure postgres as the database for dendrite.
  # @default -- See value.yaml
  enabled: false
  image:
    repository: bitnami/postgresql
    tag: "16.2.0"
  auth:
    username: dendrite
    password: changeme
    database: dendrite

  persistence:
    enabled: false

ingress:
  # -- Create an ingress for the deployment
  enabled: false
  # -- The ingressClass to use. Will be converted to annotation if not yet supported.
  className: ""
  # -- Extra, custom annotations
  annotations: {}
  # -- The ingress hostname for your matrix server.
  # Should align with the server_name and well_known_* hosts.
  # If not set, generated from the dendrite_config values.
  hostName: ""
  # -- TLS configuration. Should contain information for the server_name and well-known hosts.
  # Alternatively, set tls.generate=true to generate defaults based on the dendrite_config.
  tls: []

service:
  type: ClusterIP
  port: 8008

prometheus:
  servicemonitor:
    # -- Enable ServiceMonitor for Prometheus-Operator for scrape metric-endpoint
    enabled: false
    # -- Extra Labels on ServiceMonitor for selector of Prometheus Instance
    labels: {}
  rules:
    # -- Enable PrometheusRules for Prometheus-Operator for setup alerting
    enabled: false
    # -- Extra Labels on PrometheusRules for selector of Prometheus Instance
    labels: {}
    # -- additional alertrules (no default alertrules are provided)
    additionalRules: []

grafana:
  dashboards:
    enabled: false
    # -- Extra Labels on ConfigMap for selector of grafana sidecar
    labels:
      grafana_dashboard: "1"
    # -- Extra Annotations on ConfigMap additional config in grafana sidecar
    annotations: {}
