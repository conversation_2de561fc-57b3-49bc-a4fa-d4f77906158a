{{- define "override.config" }}
{{- if .Values.postgresql.enabled }}
{{- $_ := set .Values.dendrite_config.global.database "connection_string" (print "postgresql://" .Values.postgresql.auth.username ":" .Values.postgresql.auth.password "@" .Release.Name "-postgresql/dendrite?sslmode=disable") -}}
{{ end }}
global:
  private_key: /etc/dendrite/secrets/signing.key
  jetstream:
    in_memory: false
{{ if (gt (len (.Files.Glob "appservices/*")) 0) }}
app_service_api:
  config_files:
    {{- range $x, $y := .Files.Glob "appservices/*" }}
    - /etc/dendrite/appservices/{{ base $x }}
    {{ end }}
{{ end }}
{{ end }}
