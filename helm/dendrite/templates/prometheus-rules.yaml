{{- if and ( .Values.prometheus.rules.enabled ) ( .Capabilities.APIVersions.Has "monitoring.coreos.com/v1" ) }}
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: {{ include "dendrite.fullname" . }}
  labels:
    {{- include "dendrite.labels" . | nindent 4 }}
    {{- with .Values.prometheus.rules.labels }}
    {{- . | toYaml | nindent 4 }}
    {{- end }}
spec:
  groups:
  {{- if .Values.prometheus.rules.additionalRules }}
  - name: {{ template "dendrite.name" . }}-Additional
    rules: {{- toYaml .Values.prometheus.rules.additionalRules | nindent 4 }}
  {{- end }}
{{- end }}
