{{ if and .Values.signing_key.create (not .Values.signing_key.existingSecret ) }}
{{ $name := (print ( include "dendrite.fullname" . ) "-signing-key") }}
{{ $secretName := (print ( include "dendrite.fullname" . ) "-signing-key") }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ $name }}
  labels:
    app.kubernetes.io/component: signingkey-job
    {{- include "dendrite.labels" . | nindent 4 }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $name }}
  labels:
    app.kubernetes.io/component: signingkey-job
    {{- include "dendrite.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ""
    resources:
      - secrets
    resourceNames:
      - {{ $secretName }}
    verbs:
      - get
      - update
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ $name }}
  labels:
    app.kubernetes.io/component: signingkey-job
    {{- include "dendrite.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ $name }}
subjects:
  - kind: ServiceAccount
    name: {{ $name }}
    namespace: {{ .Release.Namespace }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: generate-signing-key
  labels:
    {{- include "dendrite.labels" . | nindent 4 }}
spec:
  template:
    spec:
      imagePullSecrets:
      {{- with .Values.imagePullSecrets }}
      {{ . | toYaml | nindent 6 }}
      {{- end }}
      restartPolicy: "Never"
      serviceAccount: {{ $name }}
      containers:
      - name: upload-key
        image: bitnami/kubectl
        command:
        - sh
        - -c
        - | 
          # check if key already exists
          key=$(kubectl get secret {{ $secretName }} -o jsonpath="{.data['signing\.key']}" 2> /dev/null)
          [ $? -ne 0 ] && echo "Failed to get existing secret" && exit 1
          [ -n "$key" ] && echo "Key already created, exiting." && exit 0
          # wait for signing key
          while [ ! -f /etc/dendrite/signing-key.pem ]; do
            echo "Waiting for signing key.."
            sleep 5;
          done
          # update secret
          kubectl patch secret {{ $secretName }} -p "{\"data\":{\"signing.key\":\"$(base64 /etc/dendrite/signing-key.pem | tr -d '\n')\"}}"
          [ $? -ne 0 ] && echo "Failed to update secret." && exit 1
          echo "Signing key successfully created."
        volumeMounts:
        - mountPath: /etc/dendrite/
          name: signing-key
          readOnly: true
      - name: generate-key
        {{- include "image.name" . | nindent 8 }}
        command:
        - sh 
        - -c
        - |
          /usr/bin/generate-keys -private-key /etc/dendrite/signing-key.pem
          chown 1001:1001 /etc/dendrite/signing-key.pem
        volumeMounts:
        - mountPath: /etc/dendrite/
          name: signing-key
      volumes:
      - name: signing-key
        emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
  parallelism: 1
  completions: 1
  backoffLimit: 1
{{ end }}