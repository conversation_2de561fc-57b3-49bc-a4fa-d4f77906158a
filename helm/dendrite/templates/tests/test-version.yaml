---
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "dendrite.fullname" . }}-test-version"
  labels:
    {{- include "dendrite.selectorLabels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: curl
      image: curlimages/curl
      imagePullPolicy: IfNotPresent
      args:
        - 'http://{{- include "dendrite.fullname" . -}}:8008/_matrix/client/versions'
  restartPolicy: Never
