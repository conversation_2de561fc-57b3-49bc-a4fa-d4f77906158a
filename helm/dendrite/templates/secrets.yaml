{{- if (gt (len (.Files.Glob "appservices/*")) 0) }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "dendrite.fullname" . }}-appservices-conf
type: Opaque
data:
{{ (.Files.Glob "appservices/*").AsSecrets | indent 2 }}
{{- end }}

{{- if and .Values.signing_key.create (not .Values.signing_key.existingSecret) }}
---
apiVersion: v1
kind: Secret
metadata:
  annotations:
    helm.sh/resource-policy: keep
  name: {{ include "dendrite.fullname" . }}-signing-key
type: Opaque
{{- end }}

{{- with .Values.dendrite_config.global.metrics }}
{{- if .enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "dendrite.fullname" $ }}-metrics-basic-auth
type: Opaque
stringData:
  user: {{ .basic_auth.user | quote }}
  password: {{ .basic_auth.password | quote }}
{{- end }}
{{- end }}

---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "dendrite.fullname" . }}-conf
type: Opaque
stringData:
  dendrite.yaml: |
    {{ toYaml ( mustMergeOverwrite .Values.dendrite_config  ( fromYaml (include "override.config" .) ) .Values.dendrite_config ) | nindent 4 }}