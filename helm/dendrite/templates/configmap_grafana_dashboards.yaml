{{- if .Values.grafana.dashboards.enabled }}
{{- range $path, $bytes := .Files.Glob "grafana_dashboards/*.json" }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "dendrite.fullname" $ }}-grafana-dashboards-{{ base $path }}
  labels:
    {{- include "dendrite.labels" $ | nindent 4 }}
    {{- toYaml $.Values.grafana.dashboards.labels | nindent 4 }}
  annotations:
    {{- toYaml $.Values.grafana.dashboards.annotations | nindent 4 }}
data:
  {{- ($.Files.Glob $path ).AsConfig | nindent 2 }}
{{- end }}
{{- end }}
