{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Dendrite dashboard from https://github.com/element-hq/dendrite/", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 13916, "graphTooltip": 0, "id": 60, "links": [], "liveNow": false, "panels": [{"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 4, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS}"}, "refId": "A"}], "title": "Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Total number of registered users", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 0, "y": 1}, "id": 20, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "exemplar": false, "expr": "sum(dendrite_clientapi_reg_users_total{namespace=~\"$namespace\",service=~\"$service\"}) by (namespace,service)", "instant": false, "interval": "", "legendFormat": "{{namespace}}: {{service}}", "refId": "A"}], "title": "Registered Users", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "The number of sync requests that are active right now and are waiting to be woken by a notifier", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 17, "x": 7, "y": 1}, "id": 6, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(dendrite_syncapi_active_sync_requests{namespace=~\"$namespace\",service=~\"$service\"}[$__rate_interval]))by (namspace,service)", "hide": false, "interval": "", "legendFormat": "active: {{namspace}} - {{service}}", "range": true, "refId": "A"}, {"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(dendrite_syncapi_waiting_sync_requests{namespace=~\"$namespace\",service=~\"$service\"}[$__rate_interval]))by (namespace,service)", "hide": false, "interval": "", "legendFormat": "waiting: {{namspace}} - {{service}}", "range": true, "refId": "B"}], "title": "Sync API", "type": "timeseries"}, {"collapsed": false, "datasource": {"uid": "${DS_PROMETHEUS}"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 8, "panels": [], "targets": [{"datasource": {"uid": "${DS_PROMETHEUS}"}, "refId": "A"}], "title": "Federation", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "description": "Collection of queues for sending transactions to other matrix servers", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 10}, "id": 10, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "9.3.6", "targets": [{"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "dendrite_federationapi_destination_queues_running{namespace=~\"$namespace\",service=~\"$service\"}", "interval": "", "legendFormat": "Queue Running: {{namespace}}-{{service}}", "range": true, "refId": "A"}, {"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "dendrite_federationapi_destination_queues_total{namespace=~\"$namespace\",service=~\"$service\"}", "hide": false, "interval": "", "legendFormat": "Queue Total: {{namespace}}-{{service}}", "range": true, "refId": "B"}, {"datasource": {"uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "dendrite_federationapi_destination_queues_backing_off{namespace=~\"$namespace\",service=~\"$service\"}", "hide": false, "interval": "", "legendFormat": "Backing Off: {{namespace}}-{{service}}", "range": true, "refId": "C"}], "title": "Federation Sender Destination", "type": "timeseries"}], "refresh": "10s", "schemaVersion": 37, "style": "dark", "tags": ["matrix", "dendrite"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(dendrite_syncapi_active_sync_requests, namespace)", "hide": 0, "includeAll": true, "multi": true, "name": "namespace", "options": [], "query": {"query": "label_values(dendrite_syncapi_active_sync_requests, namespace)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "definition": "label_values(dendrite_syncapi_active_sync_requests{namespace=~\"$namespace\"}, service)", "hide": 0, "includeAll": true, "multi": true, "name": "service", "options": [], "query": {"query": "label_values(dendrite_syncapi_active_sync_requests{namespace=~\"$namespace\"}, service)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Dendrite", "uid": "RoRt1jEGz", "version": 1, "weekStart": ""}