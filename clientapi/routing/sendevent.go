// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 包声明：定义路由处理相关的功能
package routing

// 导入所需的标准库和第三方库
import (
	"context"       // 上下文管理
	"encoding/json" // JSON编码解码
	"fmt"           // 格式化输出
	"net/http"      // HTTP协议处理
	"reflect"       // 反射操作
	"sync"          // 同步原语
	"time"          // 时间处理

	"github.com/element-hq/dendrite/clientapi/httputil"    // HTTP工具函数
	"github.com/element-hq/dendrite/internal/eventutil"    // 事件工具函数
	"github.com/element-hq/dendrite/internal/transactions" // 事务处理
	"github.com/element-hq/dendrite/roomserver/api"        // 房间服务器API
	"github.com/element-hq/dendrite/roomserver/types"      // 房间服务器类型定义
	"github.com/element-hq/dendrite/setup/config"          // 配置管理
	"github.com/element-hq/dendrite/syncapi/synctypes"     // 同步API类型
	userapi "github.com/element-hq/dendrite/userapi/api"   // 用户API
	"github.com/matrix-org/gomatrixserverlib"              // Matrix服务器库
	"github.com/matrix-org/gomatrixserverlib/spec"         // Matrix规范
	"github.com/matrix-org/util"                           // 工具函数
	"github.com/prometheus/client_golang/prometheus"       // Prometheus监控
	"github.com/sirupsen/logrus"                           // 日志库
)

// Matrix客户端API规范链接：
// http://matrix.org/docs/spec/client_server/r0.2.0.html#put-matrix-client-r0-rooms-roomid-send-eventtype-txnid
// http://matrix.org/docs/spec/client_server/r0.2.0.html#put-matrix-client-r0-rooms-roomid-state-eventtype-statekey
// 发送事件响应结构体，包含事件ID
type sendEventResponse struct {
	EventID string `json:"event_id"` // 事件ID字段
}

// 全局变量声明
var (
	userRoomSendMutexes sync.Map // (roomID+userID) -> mutex. 确保发送事件正确排序的互斥锁映射
)

// Prometheus监控指标：记录发送事件的持续时间
var sendEventDuration = prometheus.NewHistogramVec(
	prometheus.HistogramOpts{
		Namespace: "dendrite",                                                                                // 命名空间
		Subsystem: "clientapi",                                                                               // 子系统
		Name:      "sendevent_duration_millis",                                                               // 指标名称
		Help:      "How long it takes to build and submit a new event from the client API to the roomserver", // 帮助信息
		Buckets: []float64{ // 时间桶（毫秒）
			5, 10, 25, 50, 75, 100, 250, 500, // 短时间范围
			1000, 2000, 3000, 4000, 5000, 6000, // 中等时间范围
			7000, 8000, 9000, 10000, 15000, 20000, // 长时间范围
		},
	},
	[]string{"action"}, // 标签维度
)

// SendEvent 实现以下API端点：
//
//	/rooms/{roomID}/send/{eventType}           - 发送房间事件
//	/rooms/{roomID}/send/{eventType}/{txnID}   - 带事务ID发送房间事件
//	/rooms/{roomID}/state/{eventType}/{stateKey} - 发送房间状态事件
//
// nolint: gocyclo
func SendEvent(
	req *http.Request, // HTTP请求对象
	device *userapi.Device, // 设备信息
	roomID, eventType string, // 房间ID和事件类型
	txnID, stateKey *string, // 事务ID和状态键（可选）
	cfg *config.ClientAPI, // 客户端API配置
	rsAPI api.ClientRoomserverAPI, // 房间服务器API接口
	txnCache *transactions.Cache, // 事务缓存
) util.JSONResponse {
	// 查询房间版本信息
	roomVersion, err := rsAPI.QueryRoomVersionForRoom(req.Context(), roomID)
	if err != nil {
		// 如果查询失败，返回不支持的房间版本错误
		return util.JSONResponse{
			Code: http.StatusBadRequest,                    // 400状态码
			JSON: spec.UnsupportedRoomVersion(err.Error()), // 错误信息
		}
	}

	// 如果提供了事务ID，尝试从缓存中获取响应
	if txnID != nil {
		// 尝试从事务缓存中获取响应
		if res, ok := txnCache.FetchTransaction(device.AccessToken, *txnID, req.URL); ok {
			return *res // 返回缓存的响应
		}
	}

	// 在伪ID房间中将用户ID状态键转换为房间键
	// 伪ID房间是指使用 RoomVersionPseudoIDs 版本创建的房间，这种房间为了保护用户隐私， （RoomVersionPseudoIDs 事件约等于 ）
	// 不直接使用用户的真实Matrix ID，而是为每个用户在每个房间中生成唯一的伪ID（pseudoID）
	// 这样可以防止用户在不同房间之间被关联，提供更好的隐私保护
	if roomVersion == gomatrixserverlib.RoomVersionPseudoIDs && stateKey != nil {
		// 解析并验证房间ID格式
		// spec.NewRoomID 会验证房间ID是否符合Matrix规范格式（如 !roomid:domain.com）
		// 并将字符串形式的房间ID解析为结构化的RoomID对象，包含房间的本地部分和域名部分
		// 这一步确保房间ID格式正确，才能进行后续的伪ID转换操作
		parsedRoomID, innerErr := spec.NewRoomID(roomID) // 校验合规性并给出匿名id  也就是吧本地部分提取出来
		if innerErr != nil {
			// 房间ID无效
			return util.JSONResponse{
				Code: http.StatusBadRequest,                // 400状态码
				JSON: spec.InvalidParam("invalid room ID"), // 无效参数错误
			}
		}

		// 转换客户端状态键为内部状态键
		newStateKey, innerErr := synctypes.FromClientStateKey(*parsedRoomID, *stateKey, func(roomID spec.RoomID, userID spec.UserID) (*spec.SenderID, error) {
			return rsAPI.QuerySenderIDForUser(req.Context(), roomID, userID) // 查询用户的发送者ID
		})
		if innerErr != nil {
			// TODO: 为失败情况制定更好的逻辑（例如找不到发送者ID）
			util.GetLogger(req.Context()).WithError(innerErr).Error("synctypes.FromClientStateKey failed")
			return util.JSONResponse{
				Code: http.StatusInternalServerError,        // 500状态码
				JSON: spec.Unknown("internal server error"), // 内部服务器错误
			}
		}
		stateKey = newStateKey // 更新状态键
	}

	// 为特定房间中的特定用户创建互斥锁
	// 这避免了快速连续接收的事件以混乱顺序发送到房间服务器的情况
	userID := device.UserID                                                   // 获取用户ID
	domain := device.UserDomain()                                             // 获取用户域名
	mutex, _ := userRoomSendMutexes.LoadOrStore(roomID+userID, &sync.Mutex{}) // 加载或存储互斥锁
	mutex.(*sync.Mutex).Lock()                                                // 加锁
	defer mutex.(*sync.Mutex).Unlock()                                        // 延迟解锁

	var r map[string]interface{} // 必须是JSON对象
	// 解析HTTP请求中的JSON数据
	resErr := httputil.UnmarshalJSONRequest(req, &r)
	if resErr != nil {
		return *resErr // 返回解析错误
	}

	// 如果是状态事件，检查内容是否相同
	if stateKey != nil {
		// 如果现有/新状态内容相等，返回现有的event_id，使请求幂等
		if resp := stateEqual(req.Context(), rsAPI, eventType, *stateKey, roomID, r); resp != nil {
			return *resp // 返回现有事件的响应
		}
	}

	startedGeneratingEvent := time.Now() // 记录开始生成事件的时间

	// 如果我们发送成员更新，确保删除授权via键（如果存在）
	// 否则如果房间设置为"受限"加入规则，其他服务器将无法验证事件
	if eventType == spec.MRoomMember {
		delete(r, "join_authorised_via_users_server") // 删除授权字段
	}

	// 对于权限级别事件，我们需要将userID替换为pseudoID
	// 在伪ID房间中，权限级别事件的用户字段必须使用伪ID而不是真实的Matrix用户ID
	if roomVersion == gomatrixserverlib.RoomVersionPseudoIDs && eventType == spec.MRoomPowerLevels {
		err = updatePowerLevels(req, r, roomID, rsAPI) // 更新权限级别
		if err != nil {
			return util.JSONResponse{
				Code: http.StatusInternalServerError,             // 500状态码
				JSON: spec.InternalServerError{Err: err.Error()}, // 内部服务器错误
			}
		}
	}

	// 解析时间戳参数
	evTime, err := httputil.ParseTSParam(req)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,          // 400状态码
			JSON: spec.InvalidParam(err.Error()), // 无效参数错误
		}
	}

	// 生成发送事件
	e, resErr := generateSendEvent(req.Context(), r, device, roomID, eventType, stateKey, rsAPI, evTime)
	if resErr != nil {
		return *resErr // 返回生成错误
	}
	timeToGenerateEvent := time.Since(startedGeneratingEvent) // 计算生成事件耗时

	// 验证别名是否存在
	if eventType == spec.MRoomCanonicalAlias && stateKey != nil && *stateKey == "" {
		aliasReq := api.AliasEvent{} // 别名事件结构
		// 解析事件内容为别名事件
		if err = json.Unmarshal(e.Content(), &aliasReq); err != nil {
			return util.ErrorResponse(fmt.Errorf("unable to parse alias event: %w", err))
		}
		// 验证别名事件是否有效
		if !aliasReq.Valid() {
			return util.JSONResponse{
				Code: http.StatusBadRequest,                                  // 400状态码
				JSON: spec.InvalidParam("Request contains invalid aliases."), // 包含无效别名
			}
		}
		aliasRes := &api.GetAliasesForRoomIDResponse{} // 获取房间别名响应
		// 获取房间的所有别名
		if err = rsAPI.GetAliasesForRoomID(req.Context(), &api.GetAliasesForRoomIDRequest{RoomID: roomID}, aliasRes); err != nil {
			return util.JSONResponse{
				Code: http.StatusInternalServerError, // 500状态码
				JSON: spec.InternalServerError{},     // 内部服务器错误
			}
		}
		var found int                                                 // 找到的别名数量
		requestAliases := append(aliasReq.AltAliases, aliasReq.Alias) // 请求中的所有别名
		// 检查请求的别名是否在现有别名中
		for _, alias := range aliasRes.Aliases {
			for _, altAlias := range requestAliases {
				if altAlias == alias {
					found++ // 找到匹配的别名
				}
			}
		}
		// 检查我们找到的现有别名数量至少与请求中的数量相同
		if aliasReq.Alias != "" && found < len(requestAliases) {
			return util.JSONResponse{
				Code: http.StatusBadRequest,                     // 400状态码
				JSON: spec.BadAlias("No matching alias found."), // 未找到匹配的别名
			}
		}
	}

	var txnAndSessionID *api.TransactionID // 事务和会话ID
	// 如果提供了事务ID，创建事务ID结构
	if txnID != nil {
		txnAndSessionID = &api.TransactionID{
			TransactionID: *txnID,           // 事务ID
			SessionID:     device.SessionID, // 会话ID
		}
	}

	// 将新事件传递给房间服务器并接收正确的事件ID
	// 在重复事务的情况下，事件ID会被丢弃
	startedSubmittingEvent := time.Now() // 记录开始提交事件的时间
	if err := api.SendEvents(
		req.Context(), rsAPI, // 上下文和房间服务器API
		api.KindNew, // 新事件类型
		[]*types.HeaderedEvent{ // 事件列表
			{PDU: e}, // 包装的PDU事件
		},
		device.UserDomain(), // 用户域名
		domain,              // 域名
		domain,              // 域名
		txnAndSessionID,     // 事务和会话ID
		false,               // 不是异步
	); err != nil {
		// 发送事件失败
		util.GetLogger(req.Context()).WithError(err).Error("SendEvents failed")
		return util.JSONResponse{
			Code: http.StatusInternalServerError, // 500状态码
			JSON: spec.InternalServerError{},     // 内部服务器错误
		}
	}
	timeToSubmitEvent := time.Since(startedSubmittingEvent) // 计算提交事件耗时
	// 记录成功发送事件到房间服务器的日志
	util.GetLogger(req.Context()).WithFields(logrus.Fields{
		"event_id":     e.EventID(), // 事件ID
		"room_id":      roomID,      // 房间ID
		"room_version": roomVersion, // 房间版本
	}).Info("Sent event to roomserver")

	// 构建成功响应
	res := util.JSONResponse{
		Code: http.StatusOK,                  // 200状态码
		JSON: sendEventResponse{e.EventID()}, // 包含事件ID的响应
	}
	// 将响应添加到事务缓存
	if txnID != nil {
		txnCache.AddTransaction(device.AccessToken, *txnID, req.URL, &res)
	}

	// 记录生成事件与提交事件到房间服务器所花费的时间
	sendEventDuration.With(prometheus.Labels{"action": "build"}).Observe(float64(timeToGenerateEvent.Milliseconds())) // 构建时间
	sendEventDuration.With(prometheus.Labels{"action": "submit"}).Observe(float64(timeToSubmitEvent.Milliseconds()))  // 提交时间

	return res // 返回响应
}

// updatePowerLevels 更新权限级别事件中的用户ID为伪ID
// 在伪ID房间中，权限级别事件的users字段需要将真实的Matrix用户ID转换为对应的伪ID
// 这确保了房间内的权限设置使用的是伪ID而不是可以跨房间关联的真实用户ID
func updatePowerLevels(req *http.Request, r map[string]interface{}, roomID string, rsAPI api.ClientRoomserverAPI) error {
	users, ok := r["users"] // 获取用户字段
	if !ok {
		return nil // 如果没有用户字段，直接返回
	}
	userMap := users.(map[string]interface{})  // 转换为用户映射
	validRoomID, err := spec.NewRoomID(roomID) // 创建有效的房间ID
	if err != nil {
		return err // 返回错误
	}
	// 遍历用户映射，将用户ID替换为发送者ID
	for user, level := range userMap {
		uID, err := spec.NewUserID(user, true) // 创建用户ID
		if err != nil {
			continue // 我们正在就地修改映射，所以第一次迭代后会有无效的用户ID
		}
		senderID, err := rsAPI.QuerySenderIDForUser(req.Context(), *validRoomID, *uID) // 查询发送者ID
		if err != nil {
			return err // 返回错误
		} else if senderID == nil {
			// 警告：未找到发送者ID
			util.GetLogger(req.Context()).Warnf("sender ID not found for %s in %s", uID, *validRoomID)
			continue // 继续下一个用户
		}
		userMap[string(*senderID)] = level // 设置发送者ID的权限级别
		delete(userMap, user)              // 删除原用户ID
	}
	r["users"] = userMap // 更新用户映射
	return nil           // 返回成功
}

// stateEqual 比较新的和现有的状态事件内容。如果它们相等，返回包含现有event_id的*util.JSONResponse，
// 使此请求幂等。
func stateEqual(ctx context.Context, rsAPI api.ClientRoomserverAPI, eventType, stateKey, roomID string, newContent map[string]interface{}) *util.JSONResponse {
	stateRes := api.QueryCurrentStateResponse{} // 查询当前状态响应
	tuple := gomatrixserverlib.StateKeyTuple{   // 状态键元组
		EventType: eventType, // 事件类型
		StateKey:  stateKey,  // 状态键
	}
	// 查询当前状态
	err := rsAPI.QueryCurrentState(ctx, &api.QueryCurrentStateRequest{
		RoomID:      roomID,                                   // 房间ID
		StateTuples: []gomatrixserverlib.StateKeyTuple{tuple}, // 状态元组列表
	}, &stateRes)
	if err != nil {
		return nil // 查询失败，返回nil
	}
	// 如果找到现有事件
	if existingEvent, ok := stateRes.StateEvents[tuple]; ok {
		var existingContent map[string]interface{} // 现有内容
		// 解析现有事件内容
		if err = json.Unmarshal(existingEvent.Content(), &existingContent); err != nil {
			return nil // 解析失败，返回nil
		}
		// 深度比较现有内容和新内容
		if reflect.DeepEqual(existingContent, newContent) {
			return &util.JSONResponse{
				Code: http.StatusOK,                              // 200状态码
				JSON: sendEventResponse{existingEvent.EventID()}, // 返回现有事件ID
			}
		}

	}
	return nil // 内容不同或没有现有事件，返回nil
}

// generateSendEvent 生成要发送的事件
func generateSendEvent(
	ctx context.Context, // 上下文
	r map[string]interface{}, // 请求内容
	device *userapi.Device, // 设备信息
	roomID, eventType string, // 房间ID和事件类型
	stateKey *string, // 状态键（可选）
	rsAPI api.ClientRoomserverAPI, // 房间服务器API
	evTime time.Time, // 事件时间
) (gomatrixserverlib.PDU, *util.JSONResponse) {
	// 解析传入的HTTP请求
	fullUserID, err := spec.NewUserID(device.UserID, true) // 创建完整用户ID
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusBadRequest,      // 400状态码
			JSON: spec.BadJSON("Bad userID"), // 错误的用户ID
		}
	}
	validRoomID, err := spec.NewRoomID(roomID) // 创建有效房间ID
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusBadRequest,             // 400状态码
			JSON: spec.BadJSON("RoomID is invalid"), // 房间ID无效
		}
	}
	senderID, err := rsAPI.QuerySenderIDForUser(ctx, *validRoomID, *fullUserID) // 查询用户的发送者ID
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusInternalServerError,         // 500状态码
			JSON: spec.NotFound("internal server error"), // 内部服务器错误
		}
	} else if senderID == nil {
		// TODO: 缺少发送者ID是否总是意味着他们没有加入？
		//       这个逻辑是否应该以某种方式推迟到房间服务器？
		return nil, &util.JSONResponse{
			Code: http.StatusForbidden,                 // 403状态码
			JSON: spec.Forbidden("not joined to room"), // 未加入房间
		}
	}

	// 创建新事件并设置我们能设置的所有字段
	proto := gomatrixserverlib.ProtoEvent{
		SenderID: string(*senderID), // 发送者ID
		RoomID:   roomID,            // 房间ID
		Type:     eventType,         // 事件类型
		StateKey: stateKey,          // 状态键
	}
	err = proto.SetContent(r) // 设置事件内容
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("proto.SetContent failed")
		return nil, &util.JSONResponse{
			Code: http.StatusInternalServerError, // 500状态码
			JSON: spec.InternalServerError{},     // 内部服务器错误
		}
	}

	// 获取签名身份
	identity, err := rsAPI.SigningIdentityFor(ctx, *validRoomID, *fullUserID)
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusInternalServerError, // 500状态码
			JSON: spec.InternalServerError{},     // 内部服务器错误
		}
	}

	var queryRes api.QueryLatestEventsAndStateResponse // 查询最新事件和状态响应
	// 查询并构建事件
	e, err := eventutil.QueryAndBuildEvent(ctx, &proto, &identity, evTime, rsAPI, &queryRes)
	switch specificErr := err.(type) { // 根据具体错误类型处理
	case nil: // 没有错误
	case eventutil.ErrRoomNoExists: // 房间不存在错误
		return nil, &util.JSONResponse{
			Code: http.StatusNotFound,                  // 404状态码
			JSON: spec.NotFound("Room does not exist"), // 房间不存在
		}
	case gomatrixserverlib.BadJSONError: // JSON格式错误
		return nil, &util.JSONResponse{
			Code: http.StatusBadRequest,             // 400状态码
			JSON: spec.BadJSON(specificErr.Error()), // JSON格式错误
		}
	case gomatrixserverlib.EventValidationError: // 事件验证错误
		if specificErr.Code == gomatrixserverlib.EventValidationTooLarge { // 事件过大
			return nil, &util.JSONResponse{
				Code: http.StatusRequestEntityTooLarge,  // 413状态码
				JSON: spec.BadJSON(specificErr.Error()), // 请求实体过大
			}
		}
		return nil, &util.JSONResponse{
			Code: http.StatusBadRequest,             // 400状态码
			JSON: spec.BadJSON(specificErr.Error()), // JSON格式错误
		}
	default: // 其他错误
		util.GetLogger(ctx).WithError(err).Error("eventutil.BuildEvent failed")
		return nil, &util.JSONResponse{
			Code: http.StatusInternalServerError, // 500状态码
			JSON: spec.InternalServerError{},     // 内部服务器错误
		}
	}

	// 检查此用户是否可以执行此操作
	stateEvents := make([]gomatrixserverlib.PDU, len(queryRes.StateEvents)) // 状态事件列表
	for i := range queryRes.StateEvents {
		stateEvents[i] = queryRes.StateEvents[i].PDU // 提取PDU
	}
	provider, err := gomatrixserverlib.NewAuthEvents(gomatrixserverlib.ToPDUs(stateEvents)) // 创建授权事件提供者
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusForbidden,        // 403状态码
			JSON: spec.Forbidden(err.Error()), // 禁止访问
		}
	}
	// 检查是否允许此操作
	if err = gomatrixserverlib.Allowed(e.PDU, provider, func(roomID spec.RoomID, senderID spec.SenderID) (*spec.UserID, error) {
		return rsAPI.QueryUserIDForSender(ctx, *validRoomID, senderID) // 查询发送者对应的用户ID
	}); err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusForbidden,        // 403状态码
			JSON: spec.Forbidden(err.Error()), // TODO: 这个错误字符串对客户端来说是否可理解？
		}
	}

	// 用户不应该能够向同一个房间发送墓碑事件
	if e.Type() == "m.room.tombstone" {
		content := make(map[string]interface{}) // 事件内容
		// 解析事件内容
		if err = json.Unmarshal(e.Content(), &content); err != nil {
			util.GetLogger(ctx).WithError(err).Error("Cannot unmarshal the event content.")
			return nil, &util.JSONResponse{
				Code: http.StatusBadRequest,                               // 400状态码
				JSON: spec.BadJSON("Cannot unmarshal the event content."), // 无法解析事件内容
			}
		}
		// 检查替换房间是否指向同一个房间
		if content["replacement_room"] == e.RoomID().String() {
			return nil, &util.JSONResponse{
				Code: http.StatusBadRequest,                                                          // 400状态码
				JSON: spec.InvalidParam("Cannot send tombstone event that points to the same room."), // 不能发送指向同一房间的墓碑事件
			}
		}
	}

	return e.PDU, nil // 返回生成的PDU事件
}
