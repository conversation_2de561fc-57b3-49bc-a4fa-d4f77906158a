// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package routing

import (
	"context"
	"crypto/ed25519"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	appserviceAPI "github.com/element-hq/dendrite/appservice/api"
	"github.com/element-hq/dendrite/clientapi/auth/authtypes"
	"github.com/element-hq/dendrite/clientapi/httputil"
	"github.com/element-hq/dendrite/clientapi/threepid"
	"github.com/element-hq/dendrite/internal/eventutil"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/types"
	"github.com/element-hq/dendrite/setup/config"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/getsentry/sentry-go"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/gomatrixserverlib/fclient"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/matrix-org/util"
)

func SendBan(
	req *http.Request, profileAPI userapi.ClientUserAPI, device *userapi.Device,
	roomID string, cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI,
) util.JSONResponse {
	body, evTime, reqErr := extractRequestData(req)
	if reqErr != nil {
		return *reqErr
	}

	if body.UserID == "" {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("missing user_id"),
		}
	}

	deviceUserID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to ban this user, bad userID"),
		}
	}
	validRoomID, err := spec.NewRoomID(roomID)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("RoomID is invalid"),
		}
	}
	senderID, err := rsAPI.QuerySenderIDForUser(req.Context(), *validRoomID, *deviceUserID)
	if err != nil || senderID == nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to ban this user, unknown senderID"),
		}
	}

	errRes := checkMemberInRoom(req.Context(), rsAPI, *deviceUserID, roomID)
	if errRes != nil {
		return *errRes
	}

	pl, errRes := getPowerlevels(req, rsAPI, roomID)
	if errRes != nil {
		return *errRes
	}
	allowedToBan := pl.UserLevel(*senderID) >= pl.Ban
	if !allowedToBan {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to ban this user, power level too low."),
		}
	}

	return sendMembership(req.Context(), profileAPI, device, roomID, spec.Ban, body.Reason, cfg, body.UserID, evTime, rsAPI, asAPI)
}

func sendMembership(ctx context.Context, profileAPI userapi.ClientUserAPI, device *userapi.Device,
	roomID, membership, reason string, cfg *config.ClientAPI, targetUserID string, evTime time.Time,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI) util.JSONResponse {

	event, err := buildMembershipEvent(
		ctx, targetUserID, reason, profileAPI, device, membership,
		roomID, false, cfg, evTime, rsAPI, asAPI,
	)
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("buildMembershipEvent failed")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	serverName := device.UserDomain()
	if err = roomserverAPI.SendEvents(
		ctx, rsAPI,
		roomserverAPI.KindNew,
		[]*types.HeaderedEvent{event},
		device.UserDomain(),
		serverName,
		serverName,
		nil,
		false,
	); err != nil {
		util.GetLogger(ctx).WithError(err).Error("SendEvents failed")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: struct{}{},
	}
}

func SendKick(
	req *http.Request, profileAPI userapi.ClientUserAPI, device *userapi.Device,
	roomID string, cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI,
) util.JSONResponse {
	body, evTime, reqErr := extractRequestData(req)
	if reqErr != nil {
		return *reqErr
	}
	if body.UserID == "" {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("missing user_id"),
		}
	}

	deviceUserID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, bad userID"),
		}
	}
	validRoomID, err := spec.NewRoomID(roomID)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("RoomID is invalid"),
		}
	}
	senderID, err := rsAPI.QuerySenderIDForUser(req.Context(), *validRoomID, *deviceUserID)
	if err != nil || senderID == nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, unknown senderID"),
		}
	}

	errRes := checkMemberInRoom(req.Context(), rsAPI, *deviceUserID, roomID)
	if errRes != nil {
		return *errRes
	}

	bodyUserID, err := spec.NewUserID(body.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("body userID is invalid"),
		}
	}

	pl, errRes := getPowerlevels(req, rsAPI, roomID)
	if errRes != nil {
		return *errRes
	}
	allowedToKick := pl.UserLevel(*senderID) >= pl.Kick || bodyUserID.String() == deviceUserID.String()
	if !allowedToKick {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, power level too low."),
		}
	}

	var queryRes roomserverAPI.QueryMembershipForUserResponse
	err = rsAPI.QueryMembershipForUser(req.Context(), &roomserverAPI.QueryMembershipForUserRequest{
		RoomID: roomID,
		UserID: *bodyUserID,
	}, &queryRes)
	if err != nil {
		return util.ErrorResponse(err)
	}
	// kick is only valid if the user is not currently banned or left (that is, they are joined or invited)
	if queryRes.Membership != spec.Join && queryRes.Membership != spec.Invite {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Unknown("cannot /kick banned or left users"),
		}
	}
	// TODO: should we be using SendLeave instead?
	return sendMembership(req.Context(), profileAPI, device, roomID, spec.Leave, body.Reason, cfg, body.UserID, evTime, rsAPI, asAPI)
}

func SendUnban(
	req *http.Request, profileAPI userapi.ClientUserAPI, device *userapi.Device,
	roomID string, cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI,
) util.JSONResponse {
	body, evTime, reqErr := extractRequestData(req)
	if reqErr != nil {
		return *reqErr
	}
	if body.UserID == "" {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("missing user_id"),
		}
	}

	deviceUserID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, bad userID"),
		}
	}

	errRes := checkMemberInRoom(req.Context(), rsAPI, *deviceUserID, roomID)
	if errRes != nil {
		return *errRes
	}

	bodyUserID, err := spec.NewUserID(body.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("body userID is invalid"),
		}
	}
	var queryRes roomserverAPI.QueryMembershipForUserResponse
	err = rsAPI.QueryMembershipForUser(req.Context(), &roomserverAPI.QueryMembershipForUserRequest{
		RoomID: roomID,
		UserID: *bodyUserID,
	}, &queryRes)
	if err != nil {
		return util.ErrorResponse(err)
	}

	// unban is only valid if the user is currently banned
	if queryRes.Membership != spec.Ban {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.Unknown("can only /unban users that are banned"),
		}
	}
	// TODO: should we be using SendLeave instead?
	return sendMembership(req.Context(), profileAPI, device, roomID, spec.Leave, body.Reason, cfg, body.UserID, evTime, rsAPI, asAPI)
}

func SendInvite(
	req *http.Request, profileAPI userapi.ClientUserAPI, device *userapi.Device,
	roomID string, cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI,
) util.JSONResponse {
	body, evTime, reqErr := extractRequestData(req)
	if reqErr != nil {
		return *reqErr
	}

	inviteStored, jsonErrResp := checkAndProcessThreepid(
		req, device, body, cfg, rsAPI, profileAPI, roomID, evTime,
	)
	if jsonErrResp != nil {
		return *jsonErrResp
	}

	// If an invite has been stored on an identity server, it means that a
	// m.room.third_party_invite event has been emitted and that we shouldn't
	// emit a m.room.member one.
	if inviteStored {
		return util.JSONResponse{
			Code: http.StatusOK,
			JSON: struct{}{},
		}
	}

	if body.UserID == "" {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("missing user_id"),
		}
	}

	deviceUserID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, bad userID"),
		}
	}

	errRes := checkMemberInRoom(req.Context(), rsAPI, *deviceUserID, roomID)
	if errRes != nil {
		return *errRes
	}

	// 检查是否为单聊且邀请第三个人，如果是则创建群聊
	expandedRoomResponse, shouldExpand := checkAndExpandDirectChat(req.Context(), device, roomID, body.UserID, cfg, profileAPI, rsAPI, asAPI, evTime)
	if shouldExpand {
		return expandedRoomResponse
	}

	// 正常邀请流程
	response, _ := sendInvite(req.Context(), device, roomID, body.UserID, body.Reason, cfg, rsAPI, evTime)
	return response
}

// sendInvite sends an invitation to a user. Returns a JSONResponse and an error
func sendInvite(
	ctx context.Context,
	device *userapi.Device,
	roomID, userID, reason string,
	cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	evTime time.Time,
) (util.JSONResponse, error) {
	validRoomID, err := spec.NewRoomID(roomID)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam("RoomID is invalid"),
		}, err
	}
	inviter, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}, err
	}
	invitee, err := spec.NewUserID(userID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam("UserID is invalid"),
		}, err
	}

	identity, err := cfg.Matrix.SigningIdentityFor(device.UserDomain())
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}, err
	}
	err = rsAPI.PerformInvite(ctx, &roomserverAPI.PerformInviteRequest{
		InviteInput: roomserverAPI.InviteInput{
			RoomID:     *validRoomID,
			Inviter:    *inviter,
			Invitee:    *invitee,
			Reason:     reason,
			IsDirect:   false,
			KeyID:      identity.KeyID,
			PrivateKey: identity.PrivateKey,
			EventTime:  evTime,
		},
		InviteRoomState: nil, // ask the roomserver to draw up invite room state for us
		SendAsServer:    string(device.UserDomain()),
	})

	switch e := err.(type) {
	case roomserverAPI.ErrInvalidID:
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.Unknown(e.Error()),
		}, e
	case roomserverAPI.ErrNotAllowed:
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden(e.Error()),
		}, e
	case nil:
	default:
		util.GetLogger(ctx).WithError(err).Error("PerformInvite failed")
		sentry.CaptureException(err)
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}, err
	}

	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: struct{}{},
	}, nil
}

// checkAndExpandDirectChat 检查是否为单聊且邀请第三个人，如果是则创建群聊
func checkAndExpandDirectChat(
	ctx context.Context,
	device *userapi.Device,
	roomID, inviteeUserID string,
	cfg *config.ClientAPI,
	profileAPI userapi.ClientUserAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	asAPI appserviceAPI.AppServiceInternalAPI,
	evTime time.Time,
) (util.JSONResponse, bool) {
	// 1. 获取当前房间的成员信息
	membersReq := &roomserverAPI.QueryMembershipsForRoomRequest{
		RoomID:     roomID,
		JoinedOnly: true,
	}
	membersRes := &roomserverAPI.QueryMembershipsForRoomResponse{}

	if err := rsAPI.QueryMembershipsForRoom(ctx, membersReq, membersRes); err != nil {
		util.GetLogger(ctx).WithError(err).Error("Failed to query room memberships")
		return util.JSONResponse{}, false
	}

	// 如果不是2人房间（单聊），则不需要扩展
	if len(membersRes.JoinEvents) != 2 {
		return util.JSONResponse{}, false
	}

	// 2. 检查房间是否标记为直接消息
	isDirect, err := isDirectMessageRoom(ctx, rsAPI, roomID)
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("Failed to check if room is direct")
		return util.JSONResponse{}, false
	}

	// 如果不是直接消息房间，则不需要扩展
	if !isDirect {
		return util.JSONResponse{}, false
	}

	// 3. 获取当前房间的所有成员用户ID
	currentMembers := make([]string, 0, len(membersRes.JoinEvents))
	roomIDSpec, err := spec.NewRoomID(roomID)
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("Invalid room ID")
		return util.JSONResponse{}, false
	}

	for _, event := range membersRes.JoinEvents {
		if event.StateKey != nil {
			// 将 SenderID 转换为 UserID
			userID, err := rsAPI.QueryUserIDForSender(ctx, *roomIDSpec, spec.SenderID(*event.StateKey))
			if err != nil {
				util.GetLogger(ctx).WithError(err).Error("Failed to get user ID for sender")
				continue
			}
			if userID != nil {
				currentMembers = append(currentMembers, userID.String())
			}
		}
	}

	// 4. 创建新的群聊房间
	newRoomResponse, err := createGroupChatFromDirect(ctx, device, currentMembers, inviteeUserID, cfg, profileAPI, rsAPI, asAPI, evTime)
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("Failed to create group chat from direct")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}, true
	}

	return newRoomResponse, true
}

// isDirectMessageRoom 检查房间是否为直接消息房间
func isDirectMessageRoom(ctx context.Context, rsAPI roomserverAPI.ClientRoomserverAPI, roomID string) (bool, error) {
	// 查询房间的 m.room.create 事件
	stateReq := &roomserverAPI.QueryLatestEventsAndStateRequest{
		RoomID: roomID,
		StateToFetch: []gomatrixserverlib.StateKeyTuple{
			{
				EventType: spec.MRoomCreate,
				StateKey:  "",
			},
		},
	}
	stateRes := &roomserverAPI.QueryLatestEventsAndStateResponse{}

	if err := rsAPI.QueryLatestEventsAndState(ctx, stateReq, stateRes); err != nil {
		return false, err
	}

	// 检查创建事件中的 is_direct 字段
	for _, event := range stateRes.StateEvents {
		if event.Type() == spec.MRoomCreate {
			var createContent map[string]interface{}
			if err := json.Unmarshal(event.Content(), &createContent); err != nil {
				continue
			}
			if isDirect, ok := createContent["is_direct"].(bool); ok {
				return isDirect, nil
			}
		}
	}

	// 如果没有找到 is_direct 标志，检查房间成员数量是否为2
	// 这是一个备用检查方法
	membersReq := &roomserverAPI.QueryMembershipsForRoomRequest{
		RoomID:     roomID,
		JoinedOnly: true,
	}
	membersRes := &roomserverAPI.QueryMembershipsForRoomResponse{}

	if err := rsAPI.QueryMembershipsForRoom(ctx, membersReq, membersRes); err != nil {
		return false, err
	}

	// 如果只有2个成员，可能是直接消息房间
	return len(membersRes.JoinEvents) == 2, nil
}

func buildMembershipEventDirect(
	ctx context.Context,
	targetSenderID spec.SenderID, reason string, userDisplayName, userAvatarURL string,
	sender spec.SenderID, senderDomain spec.ServerName,
	membership, roomID string, isDirect bool,
	keyID gomatrixserverlib.KeyID, privateKey ed25519.PrivateKey, evTime time.Time,
	rsAPI roomserverAPI.ClientRoomserverAPI,
) (*types.HeaderedEvent, error) {
	targetSenderString := string(targetSenderID)
	proto := gomatrixserverlib.ProtoEvent{
		SenderID: string(sender),
		RoomID:   roomID,
		Type:     "m.room.member",
		StateKey: &targetSenderString,
	}

	content := gomatrixserverlib.MemberContent{
		Membership:  membership,
		DisplayName: userDisplayName,
		AvatarURL:   userAvatarURL,
		Reason:      reason,
		IsDirect:    isDirect,
	}

	if err := proto.SetContent(content); err != nil {
		return nil, err
	}

	identity := &fclient.SigningIdentity{
		ServerName: senderDomain,
		KeyID:      keyID,
		PrivateKey: privateKey,
	}
	return eventutil.QueryAndBuildEvent(ctx, &proto, identity, evTime, rsAPI, nil)
}

func buildMembershipEvent(
	ctx context.Context,
	targetUserID, reason string, profileAPI userapi.ClientUserAPI,
	device *userapi.Device,
	membership, roomID string, isDirect bool,
	cfg *config.ClientAPI, evTime time.Time,
	rsAPI roomserverAPI.ClientRoomserverAPI, asAPI appserviceAPI.AppServiceInternalAPI,
) (*types.HeaderedEvent, error) {
	profile, err := loadProfile(ctx, targetUserID, cfg, profileAPI, asAPI)
	if err != nil {
		return nil, err
	}

	userID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return nil, err
	}
	validRoomID, err := spec.NewRoomID(roomID)
	if err != nil {
		return nil, err
	}
	senderID, err := rsAPI.QuerySenderIDForUser(ctx, *validRoomID, *userID)
	if err != nil {
		return nil, err
	} else if senderID == nil {
		return nil, fmt.Errorf("no sender ID for %s in %s", *userID, *validRoomID)
	}

	targetID, err := spec.NewUserID(targetUserID, true)
	if err != nil {
		return nil, err
	}
	targetSenderID, err := rsAPI.QuerySenderIDForUser(ctx, *validRoomID, *targetID)
	if err != nil {
		return nil, err
	} else if targetSenderID == nil {
		return nil, fmt.Errorf("no sender ID for %s in %s", *targetID, *validRoomID)
	}

	identity, err := rsAPI.SigningIdentityFor(ctx, *validRoomID, *userID)
	if err != nil {
		return nil, err
	}

	return buildMembershipEventDirect(ctx, *targetSenderID, reason, profile.DisplayName, profile.AvatarURL,
		*senderID, device.UserDomain(), membership, roomID, isDirect, identity.KeyID, identity.PrivateKey, evTime, rsAPI)
}

// loadProfile lookups the profile of a given user from the database and returns
// it if the user is local to this server, or returns an empty profile if not.
// Returns an error if the retrieval failed or if the first parameter isn't a
// valid Matrix ID.
func loadProfile(
	ctx context.Context,
	userID string,
	cfg *config.ClientAPI,
	profileAPI userapi.ClientUserAPI,
	asAPI appserviceAPI.AppServiceInternalAPI,
) (*authtypes.Profile, error) {
	_, serverName, err := gomatrixserverlib.SplitID('@', userID)
	if err != nil {
		return nil, err
	}

	var profile *authtypes.Profile
	if cfg.Matrix.IsLocalServerName(serverName) {
		profile, err = appserviceAPI.RetrieveUserProfile(ctx, userID, asAPI, profileAPI)
	} else {
		profile = &authtypes.Profile{}
	}

	return profile, err
}

func extractRequestData(req *http.Request) (body *threepid.MembershipRequest, evTime time.Time, resErr *util.JSONResponse) {

	if reqErr := httputil.UnmarshalJSONRequest(req, &body); reqErr != nil {
		resErr = reqErr
		return
	}

	evTime, err := httputil.ParseTSParam(req)
	if err != nil {
		resErr = &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam(err.Error()),
		}
		return
	}
	return
}

func checkAndProcessThreepid(
	req *http.Request,
	device *userapi.Device,
	body *threepid.MembershipRequest,
	cfg *config.ClientAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	profileAPI userapi.ClientUserAPI,
	roomID string,
	evTime time.Time,
) (inviteStored bool, errRes *util.JSONResponse) {

	inviteStored, err := threepid.CheckAndProcessInvite(
		req.Context(), device, body, cfg, rsAPI, profileAPI,
		roomID, evTime,
	)
	switch e := err.(type) {
	case nil:
	case threepid.ErrMissingParameter:
		util.GetLogger(req.Context()).WithError(err).Error("threepid.CheckAndProcessInvite failed")
		return inviteStored, &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON(err.Error()),
		}
	case threepid.ErrNotTrusted:
		util.GetLogger(req.Context()).WithError(err).Error("threepid.CheckAndProcessInvite failed")
		return inviteStored, &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.NotTrusted(body.IDServer),
		}
	case eventutil.ErrRoomNoExists:
		util.GetLogger(req.Context()).WithError(err).Error("threepid.CheckAndProcessInvite failed")
		return inviteStored, &util.JSONResponse{
			Code: http.StatusNotFound,
			JSON: spec.NotFound(err.Error()),
		}
	case gomatrixserverlib.BadJSONError:
		util.GetLogger(req.Context()).WithError(err).Error("threepid.CheckAndProcessInvite failed")
		return inviteStored, &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON(e.Error()),
		}
	default:
		util.GetLogger(req.Context()).WithError(err).Error("threepid.CheckAndProcessInvite failed")
		return inviteStored, &util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	return
}

func checkMemberInRoom(ctx context.Context, rsAPI roomserverAPI.ClientRoomserverAPI, userID spec.UserID, roomID string) *util.JSONResponse {
	var membershipRes roomserverAPI.QueryMembershipForUserResponse
	err := rsAPI.QueryMembershipForUser(ctx, &roomserverAPI.QueryMembershipForUserRequest{
		RoomID: roomID,
		UserID: userID,
	}, &membershipRes)
	if err != nil {
		util.GetLogger(ctx).WithError(err).Error("QueryMembershipForUser: could not query membership for user")
		return &util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	if !membershipRes.IsInRoom {
		return &util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("user does not belong to room"),
		}
	}
	return nil
}

func SendForget(
	req *http.Request, device *userapi.Device,
	roomID string, rsAPI roomserverAPI.ClientRoomserverAPI,
) util.JSONResponse {
	ctx := req.Context()
	logger := util.GetLogger(ctx).WithField("roomID", roomID).WithField("userID", device.UserID)

	deviceUserID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to kick this user, bad userID"),
		}
	}

	var membershipRes roomserverAPI.QueryMembershipForUserResponse
	membershipReq := roomserverAPI.QueryMembershipForUserRequest{
		RoomID: roomID,
		UserID: *deviceUserID,
	}
	err = rsAPI.QueryMembershipForUser(ctx, &membershipReq, &membershipRes)
	if err != nil {
		logger.WithError(err).Error("QueryMembershipForUser: could not query membership for user")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	if !membershipRes.RoomExists {
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("room does not exist"),
		}
	}
	if membershipRes.IsInRoom {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.Unknown(fmt.Sprintf("User %s is in room %s", device.UserID, roomID)),
		}
	}

	request := roomserverAPI.PerformForgetRequest{
		RoomID: roomID,
		UserID: device.UserID,
	}
	response := roomserverAPI.PerformForgetResponse{}
	if err := rsAPI.PerformForget(ctx, &request, &response); err != nil {
		logger.WithError(err).Error("PerformForget: unable to forget room")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: struct{}{},
	}
}

func getPowerlevels(req *http.Request, rsAPI roomserverAPI.ClientRoomserverAPI, roomID string) (*gomatrixserverlib.PowerLevelContent, *util.JSONResponse) {
	plEvent := roomserverAPI.GetStateEvent(req.Context(), rsAPI, roomID, gomatrixserverlib.StateKeyTuple{
		EventType: spec.MRoomPowerLevels,
		StateKey:  "",
	})
	if plEvent == nil {
		return nil, &util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to perform this action, no power_levels event in this room."),
		}
	}
	pl, err := plEvent.PowerLevels()
	if err != nil {
		return nil, &util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden("You don't have permission to perform this action, the power_levels event for this room is malformed so auth checks cannot be performed."),
		}
	}
	return pl, nil
}

// createGroupChatFromDirect 从单聊创建群聊房间
func createGroupChatFromDirect(
	ctx context.Context,
	device *userapi.Device,
	currentMembers []string,
	inviteeUserID string,
	cfg *config.ClientAPI,
	profileAPI userapi.ClientUserAPI,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	asAPI appserviceAPI.AppServiceInternalAPI,
	evTime time.Time,
) (util.JSONResponse, error) {
	// 1. 准备创建群聊房间的请求
	allMembers := append(currentMembers, inviteeUserID)

	// 创建房间请求
	createRequest := createRoomRequest{
		Invite:     allMembers,
		IsDirect:   false,                  // 群聊不是直接消息
		Preset:     spec.PresetPrivateChat, // 私有聊天预设
		Visibility: "private",              // 私有房间
		Name:       "群聊",                   // 默认房间名称
	}

	// 2. 创建新的群聊房间
	response := createRoom(ctx, createRequest, device, cfg, profileAPI, rsAPI, asAPI, evTime)

	// 3. 检查创建是否成功
	if response.Code != http.StatusOK {
		return response, fmt.Errorf("failed to create group chat room")
	}

	// 4. 返回成功响应，包含新房间信息
	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: map[string]interface{}{
			"room_id": response.JSON,
			"message": "群聊房间已创建，所有成员已被邀请",
		},
	}, nil
}
