// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package routing

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	appserviceAPI "github.com/element-hq/dendrite/appservice/api"
	"github.com/element-hq/dendrite/internal"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	roomserverVersion "github.com/element-hq/dendrite/roomserver/version"
	"github.com/element-hq/dendrite/userapi/api"
	"github.com/matrix-org/gomatrixserverlib/spec"

	"github.com/element-hq/dendrite/clientapi/httputil"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/util"
	log "github.com/sirupsen/logrus"
)

// https://matrix.org/docs/spec/client_server/r0.2.0.html#post-matrix-client-r0-createroom
type createRoomRequest struct {
	Invite                    []string                           `json:"invite"`
	Name                      string                             `json:"name"`
	Visibility                string                             `json:"visibility"`
	Topic                     string                             `json:"topic"`
	Preset                    string                             `json:"preset"`
	CreationContent           json.RawMessage                    `json:"creation_content"`
	InitialState              []gomatrixserverlib.FledglingEvent `json:"initial_state"`
	RoomAliasName             string                             `json:"room_alias_name"`
	RoomVersion               gomatrixserverlib.RoomVersion      `json:"room_version"`
	PowerLevelContentOverride json.RawMessage                    `json:"power_level_content_override"`
	IsDirect                  bool                               `json:"is_direct"`
}

func (r createRoomRequest) Validate() *util.JSONResponse {
	whitespace := "\t\n\x0b\x0c\r " // https://docs.python.org/2/library/string.html#string.whitespace
	// https://github.com/matrix-org/synapse/blob/v0.19.2/synapse/handlers/room.py#L81
	// Synapse doesn't check for ':' but we will else it will break parsers badly which split things into 2 segments.
	if strings.ContainsAny(r.RoomAliasName, whitespace+":") {
		return &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("room_alias_name cannot contain whitespace or ':'"),
		}
	}
	for _, userID := range r.Invite {
		if _, err := spec.NewUserID(userID, true); err != nil {
			return &util.JSONResponse{
				Code: http.StatusBadRequest,
				JSON: spec.BadJSON("user id must be in the form @localpart:domain"),
			}
		}
	}
	switch r.Preset {
	case spec.PresetPrivateChat, spec.PresetTrustedPrivateChat, spec.PresetPublicChat, "":
	default:
		return &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("preset must be any of 'private_chat', 'trusted_private_chat', 'public_chat'"),
		}
	}

	// Validate creation_content fields defined in the spec by marshalling the
	// creation_content map into bytes and then unmarshalling the bytes into
	// eventutil.CreateContent.

	creationContentBytes, err := json.Marshal(r.CreationContent)
	if err != nil {
		return &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("malformed creation_content"),
		}
	}

	var CreationContent gomatrixserverlib.CreateContent
	err = json.Unmarshal(creationContentBytes, &CreationContent)
	if err != nil {
		return &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("malformed creation_content"),
		}
	}

	return nil
}

// https://matrix.org/docs/spec/client_server/r0.2.0.html#post-matrix-client-r0-createroom
type createRoomResponse struct {
	RoomID    string `json:"room_id"`
	RoomAlias string `json:"room_alias,omitempty"` // in synapse not spec
}

// CreateRoom implements /createRoom
func CreateRoom(
	req *http.Request, device *api.Device,
	cfg *config.ClientAPI,
	profileAPI api.ClientUserAPI, rsAPI roomserverAPI.ClientRoomserverAPI,
	asAPI appserviceAPI.AppServiceInternalAPI,
) util.JSONResponse {
	// 🔍 开始链路跟踪
	trace, ctx := internal.StartRegion(req.Context(), "CreateRoom")
	trace.SetTag("user_id", device.UserID)
	trace.SetTag("device_id", device.ID)
	defer trace.EndRegion()

	var createRequest createRoomRequest
	resErr := httputil.UnmarshalJSONRequest(req, &createRequest)
	if resErr != nil {
		trace.SetTag("error", "unmarshal_failed")
		return *resErr
	}
	if resErr = createRequest.Validate(); resErr != nil {
		trace.SetTag("error", "validation_failed")
		return *resErr
	}

	// 添加请求参数到跟踪
	trace.SetTag("room_alias", createRequest.RoomAliasName)
	trace.SetTag("preset", createRequest.Preset)
	trace.SetTag("visibility", createRequest.Visibility)
	trace.SetTag("invite_count", len(createRequest.Invite))
	trace.SetTag("is_direct", createRequest.IsDirect)

	fmt.Println("createRequest--------1", createRequest)
	evTime, err := httputil.ParseTSParam(req)
	if err != nil {
		trace.SetTag("error", "parse_time_failed")
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam(err.Error()),
		}
	}
	fmt.Println("createRequest--------2", createRequest)

	return createRoom(ctx, createRequest, device, cfg, profileAPI, rsAPI, asAPI, evTime)
}

// createRoom implements /createRoom
func createRoom(
	ctx context.Context,
	createRequest createRoomRequest, device *api.Device,
	cfg *config.ClientAPI,
	profileAPI api.ClientUserAPI, rsAPI roomserverAPI.ClientRoomserverAPI,
	asAPI appserviceAPI.AppServiceInternalAPI,
	evTime time.Time,
) util.JSONResponse {
	// 🔍 子链路跟踪
	trace, ctx := internal.StartRegion(ctx, "createRoom.internal")
	defer trace.EndRegion()

	userID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		trace.SetTag("error", "invalid_user_id")
		util.GetLogger(ctx).WithError(err).Error("invalid userID")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	if !cfg.Matrix.IsLocalServerName(userID.Domain()) {
		trace.SetTag("error", "invalid_domain")
		return util.JSONResponse{
			Code: http.StatusForbidden,
			JSON: spec.Forbidden(fmt.Sprintf("User domain %q not configured locally", userID.Domain())),
		}
	}

	trace.SetTag("room_creator", userID.String())
	logger := util.GetLogger(ctx)

	// Check if this is a direct message room and if one already exists
	// 若存在则返回一个
	if createRequest.IsDirect && len(createRequest.Invite) == 1 {
		// 🔍 直接消息检查跟踪
		dmTrace, dmCtx := internal.StartRegion(ctx, "createRoom.checkDirectMessage")
		inviteeUserID := createRequest.Invite[0]
		dmTrace.SetTag("invitee", inviteeUserID)

		existingRoomID, roomExists, err := rsAPI.FindOrCreateDirectMessageRoom(dmCtx, userID.String(), inviteeUserID)
		if err != nil {
			dmTrace.SetTag("error", "find_dm_failed")
			dmTrace.EndRegion()
			util.GetLogger(ctx).WithError(err).Error("Failed to check for existing direct message room")
			return util.JSONResponse{
				Code: http.StatusInternalServerError,
				JSON: spec.InternalServerError{},
			}
		}

		dmTrace.SetTag("room_exists", roomExists)
		dmTrace.SetTag("existing_room_id", existingRoomID)

		if roomExists && existingRoomID != "" {
			// Check if the current user is invited to this room
			// If so, join the room instead of creating a new invite
			queryReq := &roomserverAPI.QueryMembershipForUserRequest{
				RoomID: existingRoomID,
				UserID: *userID,
			}
			queryRes := &roomserverAPI.QueryMembershipForUserResponse{}
			if err := rsAPI.QueryMembershipForUser(dmCtx, queryReq, queryRes); err != nil {
				dmTrace.SetTag("error", "query_membership_failed")
				logger.WithError(err).Warn("Failed to query membership for user")
			} else if queryRes.Membership == "invite" {
				dmTrace.SetTag("action", "joining_existing_room")
				// User is invited, so join the room
				logger.WithFields(log.Fields{
					"userID":         userID.String(),
					"inviteeUserID":  inviteeUserID,
					"existingRoomID": existingRoomID,
				}).Info("User is invited to existing DM room, joining instead of creating")

				joinReq := &roomserverAPI.PerformJoinRequest{
					RoomIDOrAlias: existingRoomID,
					UserID:        userID.String(),
				}
				joinedRoomID, _, joinErr := rsAPI.PerformJoin(dmCtx, joinReq)
				if joinErr != nil {
					dmTrace.SetTag("error", "join_existing_room_failed")
					dmTrace.EndRegion()
					logger.WithError(joinErr).Error("Failed to join existing DM room")
					return util.JSONResponse{
						Code: http.StatusInternalServerError,
						JSON: spec.InternalServerError{},
					}
				}

				dmTrace.SetTag("result", "joined_existing_room")
				dmTrace.EndRegion()
				response := createRoomResponse{
					RoomID: joinedRoomID,
				}
				return util.JSONResponse{
					Code: 200,
					JSON: response,
				}
			}

			// Return the existing room (user is already in room or other state)
			dmTrace.SetTag("result", "returned_existing_room")
			dmTrace.EndRegion()
			logger.WithFields(log.Fields{
				"userID":         userID.String(),
				"inviteeUserID":  inviteeUserID,
				"existingRoomID": existingRoomID,
			}).Info("Returning existing direct message room")

			response := createRoomResponse{
				RoomID: existingRoomID,
			}
			return util.JSONResponse{
				Code: 200,
				JSON: response,
			}
		}
		dmTrace.EndRegion()
	}

	// 🔍 新房间创建跟踪
	newRoomTrace, newRoomCtx := internal.StartRegion(ctx, "createRoom.newRoom")
	defer newRoomTrace.EndRegion()

	// TODO: Check room ID doesn't clash with an existing one, and we
	//       probably shouldn't be using pseudo-random strings, maybe GUIDs?
	roomID, err := spec.NewRoomID(fmt.Sprintf("!%s:%s", util.RandomString(16), userID.Domain()))
	if err != nil {
		newRoomTrace.SetTag("error", "invalid_room_id")
		util.GetLogger(ctx).WithError(err).Error("invalid roomID")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	newRoomTrace.SetTag("room_id", roomID.String())
	// Clobber keys: creator, room_version
	// 到了这里吗
	fmt.Println("createRequest--------3", createRequest)

	roomVersion := rsAPI.DefaultRoomVersion()
	if createRequest.RoomVersion != "" {
		candidateVersion := gomatrixserverlib.RoomVersion(createRequest.RoomVersion)
		_, roomVersionError := roomserverVersion.SupportedRoomVersion(candidateVersion)
		if roomVersionError != nil {
			newRoomTrace.SetTag("error", "unsupported_room_version")
			return util.JSONResponse{
				Code: http.StatusBadRequest,
				JSON: spec.UnsupportedRoomVersion(roomVersionError.Error()),
			}
		}
		roomVersion = candidateVersion
	}
	newRoomTrace.SetTag("room_version", string(roomVersion))

	logger.WithFields(log.Fields{
		"userID":      userID.String(),
		"roomID":      roomID.String(),
		"roomVersion": roomVersion,
	}).Info("Creating new room")

	// 🔍 用户资料获取跟踪
	profileTrace, profileCtx := internal.StartRegion(newRoomCtx, "createRoom.getUserProfile")
	profile, err := appserviceAPI.RetrieveUserProfile(profileCtx, userID.String(), asAPI, profileAPI)
	if err != nil {
		profileTrace.SetTag("error", "retrieve_profile_failed")
		profileTrace.EndRegion()
		util.GetLogger(ctx).WithError(err).Error("appserviceAPI.RetrieveUserProfile failed")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}
	profileTrace.SetTag("display_name", profile.DisplayName)
	profileTrace.SetTag("avatar_url", profile.AvatarURL)
	profileTrace.EndRegion()

	userDisplayName := profile.DisplayName
	userAvatarURL := profile.AvatarURL

	keyID := cfg.Matrix.KeyID
	privateKey := cfg.Matrix.PrivateKey

	req := roomserverAPI.PerformCreateRoomRequest{
		InvitedUsers:              createRequest.Invite,
		RoomName:                  createRequest.Name,
		Visibility:                createRequest.Visibility,
		Topic:                     createRequest.Topic,
		StatePreset:               createRequest.Preset,
		CreationContent:           createRequest.CreationContent,
		InitialState:              createRequest.InitialState,
		RoomAliasName:             createRequest.RoomAliasName,
		RoomVersion:               roomVersion,
		PowerLevelContentOverride: createRequest.PowerLevelContentOverride,
		IsDirect:                  createRequest.IsDirect,

		UserDisplayName: userDisplayName,
		UserAvatarURL:   userAvatarURL,
		KeyID:           keyID,
		PrivateKey:      privateKey,
		EventTime:       evTime,
	}

	// 🔍 核心房间创建跟踪
	performTrace, performCtx := internal.StartRegion(newRoomCtx, "createRoom.performCreateRoom")
	performTrace.SetTag("invited_users_count", len(createRequest.Invite))
	performTrace.SetTag("room_name", createRequest.Name)

	fmt.Println("createRequest--------4", createRequest)
	roomAlias, createRes := rsAPI.PerformCreateRoom(performCtx, *userID, *roomID, &req)
	if createRes != nil {
		performTrace.SetTag("error", "perform_create_room_failed")
		performTrace.SetTag("error_code", createRes.Code)
		performTrace.EndRegion()
		return *createRes
	}
	performTrace.SetTag("room_alias", roomAlias)
	performTrace.EndRegion()
	fmt.Println("createRequest--------5", createRequest)

	// If this is a direct message room, record the relationship
	if createRequest.IsDirect && len(createRequest.Invite) == 1 {
		inviteeUserID := createRequest.Invite[0]
		err := rsAPI.SetDirectMessageRoom(ctx, userID.String(), inviteeUserID, roomID.String())
		if err != nil {
			// Log the error but don't fail the room creation
			logger.WithError(err).Warn("Failed to record direct message room relationship")
		}
	}

	response := createRoomResponse{
		RoomID:    roomID.String(),
		RoomAlias: roomAlias,
	}

	return util.JSONResponse{
		Code: 200,
		JSON: response,
	}
}
