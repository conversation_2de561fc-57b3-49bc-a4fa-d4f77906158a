// Copyright 2024 New Vector Ltd.
// Copyright 2021 The Matrix.org Foundation C.I.C.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package auth

import (
	"encoding/json"
	"io"
	"net/http"

	"github.com/element-hq/dendrite/clientapi/auth/authtypes"
	"github.com/element-hq/dendrite/setup/config"
	uapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/matrix-org/util"
)

// LoginFromJSONReader performs authentication given a login request body reader and
// some context. It returns the basic login information and a cleanup function to be
// called after authorization has completed, with the result of the authorization.
// If the final return value is non-nil, an error occurred and the cleanup function
// is nil.

// zhou
// 密码登录(LoginTypePassword):
// 使用用户名+密码验证
// 调用QueryAccountByPassword检查凭证
// 最基础的认证方式
// 令牌登录(LoginTypeToken):
// 使用已有access_token验证
// 适合客户端持久化登录
// 不涉及密码传输
// 应用服务登录(LoginTypeApplicationService):
// 使用应用服务注册的token
// 跳过普通用户认证流程
// 需要预先配置AS
// 密钥处理说明：

// 此阶段仅完成身份认证
// 不处理设备密钥相关逻辑
// 密钥上传是独立流程(/keys/upload)
// 认证通过后返回的token用于后续API调用
// 关键设计：

// 认证与密钥分离
// 插件式认证处理器
// 统一的错误处理
// 清晰的类型分发
func LoginFromJSONReader(
	req *http.Request,
	useraccountAPI uapi.UserLoginAPI,
	userAPI UserInternalAPIForLogin,
	cfg *config.ClientAPI,
) (*Login, LoginCleanupFunc, *util.JSONResponse) {
	reqBytes, err := io.ReadAll(req.Body)
	if err != nil {
		err := &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("Reading request body failed: " + err.Error()),
		}
		return nil, nil, err
	}

	var header struct {
		Type string `json:"type"`
	}
	if err := json.Unmarshal(reqBytes, &header); err != nil {
		err := &util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("Reading request body failed: " + err.Error()),
		}
		return nil, nil, err
	}

	var typ Type
	switch header.Type { // 也就是有密钥登陆 // 有token 登陆
	case authtypes.LoginTypePassword:
		typ = &LoginTypePassword{
			GetAccountByPassword: useraccountAPI.QueryAccountByPassword,
			Config:               cfg,
		}
	case authtypes.LoginTypeToken:
		typ = &LoginTypeToken{
			UserAPI: userAPI,
			Config:  cfg,
		}
	case authtypes.LoginTypeApplicationService:
		token, err := ExtractAccessToken(req)
		if err != nil {
			err := &util.JSONResponse{
				Code: http.StatusForbidden,
				JSON: spec.MissingToken(err.Error()),
			}
			return nil, nil, err
		}

		typ = &LoginTypeApplicationService{
			Config: cfg,
			Token:  token,
		}
	// case authtypes.LoginTypeUserCenter:
	// 	typ = &LoginTypeSharedSecret{
	// 		Config:   cfg,
	// 		TypeName: authtypes.LoginTypeUserCenter,
	// 	}
	// case authtypes.LoginTypeBot:
	// 	typ = &LoginTypeSharedSecret{
	// 		Config:   cfg,
	// 		TypeName: authtypes.LoginTypeBot,
	// 	}
	default:
		err := util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam("unhandled login type: " + header.Type),
		}
		return nil, nil, &err
	}

	return typ.LoginFromJSON(req.Context(), reqBytes)
}

// UserInternalAPIForLogin contains the aspects of UserAPI required for logging in.
type UserInternalAPIForLogin interface {
	uapi.LoginTokenInternalAPI
}
