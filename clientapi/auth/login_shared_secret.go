// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package auth

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/hex"
	"net/http"
	"strings"

	"github.com/element-hq/dendrite/clientapi/httputil"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/matrix-org/util"
)

// SharedSecretRequest 共享密钥登录请求结构
// 继承标准登录请求，添加共享密钥验证字段
type SharedSecretRequest struct {
	Login
	Password string `json:"password"` // 密码（可选）
	Nonce    string `json:"nonce"`    // 随机数
	Mac      string `json:"mac"`      // HMAC签名
}

// LoginTypeSharedSecret 实现基于共享密钥的登录认证
// 用于 LoginTypeUserCenter 和 LoginTypeBot 类型
type LoginTypeSharedSecret struct {
	Config   *config.ClientAPI
	TypeName string // 具体的登录类型名称
}

// Name 返回认证类型名称
func (t *LoginTypeSharedSecret) Name() string {
	return t.TypeName
}

// LoginFromJSON 从JSON请求中执行共享密钥登录
func (t *LoginTypeSharedSecret) LoginFromJSON(
	ctx context.Context, reqBytes []byte,
) (*Login, LoginCleanupFunc, *util.JSONResponse) {
	var r SharedSecretRequest
	if err := httputil.UnmarshalJSON(reqBytes, &r); err != nil {
		return nil, nil, err
	}

	// 验证必需字段
	username := r.Username()
	if username == "" {
		return nil, nil, &util.JSONResponse{
			Code: http.StatusUnauthorized,
			JSON: spec.BadJSON("A username must be supplied."),
		}
	}

	// 对于共享密钥认证，nonce和mac是必需的
	if r.Nonce == "" {
		return nil, nil, &util.JSONResponse{
			Code: http.StatusUnauthorized,
			JSON: spec.BadJSON("A nonce must be supplied for shared secret authentication."),
		}
	}

	if r.Mac == "" {
		return nil, nil, &util.JSONResponse{
			Code: http.StatusUnauthorized,
			JSON: spec.BadJSON("A mac must be supplied for shared secret authentication."),
		}
	}

	// 验证共享密钥签名
	if !t.validateSharedSecret(username, r.Password, r.Nonce, r.Mac) {
		return nil, nil, &util.JSONResponse{
			Code: http.StatusUnauthorized,
			JSON: spec.Forbidden("Invalid shared secret authentication"),
		}
	}

	cleanup := func(ctx context.Context, j *util.JSONResponse) {}
	return &r.Login, cleanup, nil
}

// validateSharedSecret 验证共享密钥签名
func (t *LoginTypeSharedSecret) validateSharedSecret(username, password, nonce, givenMac string) bool {
	// 检查是否配置了共享密钥
	if t.Config.RegistrationSharedSecret == "" {
		return false
	}

	// 检查用户名和密码是否包含分隔符
	if strings.Contains(username, "\x00") || strings.Contains(password, "\x00") {
		return false
	}

	// 构建HMAC输入字符串
	// 格式: nonce + username + password + "notadmin"
	// 这里使用 "notadmin" 因为登录时不需要管理员权限
	adminString := "notadmin"
	joined := strings.Join([]string{nonce, username, password, adminString}, "\x00")

	// 计算HMAC-SHA1签名
	mac := hmac.New(sha1.New, []byte(t.Config.RegistrationSharedSecret))
	mac.Write([]byte(joined))
	expectedMAC := mac.Sum(nil)

	// 解码给定的MAC
	givenMacBytes, err := hex.DecodeString(givenMac)
	if err != nil {
		return false
	}

	// 使用恒定时间比较防止时序攻击
	return hmac.Equal(givenMacBytes, expectedMAC)
}
