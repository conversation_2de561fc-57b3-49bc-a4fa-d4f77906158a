// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包服务API接口定义
package api

import (
	"context"
	"time"

	"github.com/matrix-org/gomatrixserverlib/spec"
)

// RedPacketType 红包类型
type RedPacketType string

const (
	RedPacketTypeNormal RedPacketType = "normal" // 普通红包
	RedPacketTypeLucky  RedPacketType = "lucky"  // 拼手气红包
)

// RedPacketStatus 红包状态
type RedPacketStatus string

const (
	RedPacketStatusActive   RedPacketStatus = "active"   // 活跃状态
	RedPacketStatusFinished RedPacketStatus = "finished" // 已抢完
	RedPacketStatusExpired  RedPacketStatus = "expired"  // 已过期
)

// RedPacket 红包信息
type RedPacket struct {
	ID              string          `json:"id"`
	EventID         string          `json:"event_id"`
	RoomID          string          `json:"room_id"`
	SenderID        string          `json:"sender_id"`
	Type            RedPacketType   `json:"type"`
	TotalAmount     float64         `json:"total_amount"`
	TotalCount      int             `json:"total_count"`
	RemainingAmount float64         `json:"remaining_amount"`
	RemainingCount  int             `json:"remaining_count"`
	Message         string          `json:"message"`
	Status          RedPacketStatus `json:"status"`
	CreatedAt       time.Time       `json:"created_at"`
	ExpiresAt       time.Time       `json:"expires_at"`
}

// RedPacketGrab 红包领取记录
type RedPacketGrab struct {
	ID           string    `json:"id"`
	RedPacketID  string    `json:"redpacket_id"`
	UserID       string    `json:"user_id"`
	Amount       float64   `json:"amount"`
	GrabbedAt    time.Time `json:"grabbed_at"`
	IsLuckiest   bool      `json:"is_luckiest"`   // 是否是手气最佳
	UserNickname string    `json:"user_nickname"` // 用户昵称
}

// SendRedPacketRequest 发送红包请求
type SendRedPacketRequest struct {
	RoomID      string        `json:"room_id"`
	Type        RedPacketType `json:"type"`
	TotalAmount float64       `json:"total_amount"`
	TotalCount  int           `json:"total_count"`
	Message     string        `json:"message"`
	ExpiresIn   int           `json:"expires_in"` // 过期时间（秒）
}

// SendRedPacketResponse 发送红包响应
type SendRedPacketResponse struct {
	RedPacket *RedPacket `json:"redpacket"`
	EventID   string     `json:"event_id"`
}

// GrabRedPacketRequest 抢红包请求
type GrabRedPacketRequest struct {
	EventID string `json:"event_id"`
}

// GrabRedPacketResponse 抢红包响应
type GrabRedPacketResponse struct {
	Success   bool           `json:"success"`
	Amount    float64        `json:"amount"`
	Message   string         `json:"message"`
	RedPacket *RedPacket     `json:"redpacket"`
	Grab      *RedPacketGrab `json:"grab"`
}

// GetRedPacketRequest 获取红包信息请求
type GetRedPacketRequest struct {
	EventID string `json:"event_id"`
}

// GetRedPacketResponse 获取红包信息响应
type GetRedPacketResponse struct {
	RedPacket *RedPacket       `json:"redpacket"`
	Grabs     []*RedPacketGrab `json:"grabs"`
}

// RedPacketServiceInternalAPI 红包服务内部API接口
type RedPacketServiceInternalAPI interface {
	// SendRedPacket 发送红包
	SendRedPacket(ctx context.Context, req *SendRedPacketRequest, senderID spec.UserID) (*SendRedPacketResponse, error)

	// GrabRedPacket 抢红包
	GrabRedPacket(ctx context.Context, req *GrabRedPacketRequest, userID spec.UserID) (*GrabRedPacketResponse, error)

	// GetRedPacket 获取红包信息
	GetRedPacket(ctx context.Context, req *GetRedPacketRequest) (*GetRedPacketResponse, error)

	// GetRedPacketByEventID 通过事件ID获取红包
	GetRedPacketByEventID(ctx context.Context, eventID string) (*RedPacket, error)

	// ExpireRedPackets 处理过期红包
	ExpireRedPackets(ctx context.Context) error
}

// RedPacketContent 红包消息内容
type RedPacketContent struct {
	MsgType     string        `json:"msgtype"`
	Body        string        `json:"body"`
	RedPacketID string        `json:"redpacket_id"`
	Type        RedPacketType `json:"redpacket_type"`
	Amount      float64       `json:"amount"`
	Count       int           `json:"count"`
	Message     string        `json:"redpacket_message"`
}
