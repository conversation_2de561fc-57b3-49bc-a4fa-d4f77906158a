// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包服务路由处理包
package routing

import (
	"encoding/json"
	"net/http"

	"github.com/element-hq/dendrite/internal/httputil"
	"github.com/element-hq/dendrite/redpacketapi/api"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/gorilla/mux"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/matrix-org/util"
	"github.com/sirupsen/logrus"
)

// Setup 设置红包服务的HTTP路由
func Setup(
	publicAPIMux *mux.Router,
	redpacketAPI api.RedPacketServiceInternalAPI,
	userAPI userapi.ClientUserAPI,
) {
	// 发送红包 - 注意：publicAPIMux已经有/_matrix/client/前缀，所以只需要添加剩余部分
	publicAPIMux.Handle("/r0/rooms/{roomID}/redpacket",
		httputil.MakeAuthAPI("send_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
			return SendRedPacket(req, device, redpacketAPI)
		}),
	).Methods(http.MethodPost, http.MethodOptions)

	// 抢红包
	publicAPIMux.Handle("/r0/redpacket/{eventID}/grab",
		httputil.MakeAuthAPI("grab_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
			return GrabRedPacket(req, device, redpacketAPI)
		}),
	).Methods(http.MethodPost, http.MethodOptions)

	// 获取红包信息
	publicAPIMux.Handle("/r0/redpacket/{eventID}",
		httputil.MakeAuthAPI("get_redpacket", userAPI, func(req *http.Request, device *userapi.Device) util.JSONResponse {
			return GetRedPacket(req, device, redpacketAPI)
		}),
	).Methods(http.MethodGet, http.MethodOptions)
}

// SendRedPacket 处理发送红包请求
func SendRedPacket(
	req *http.Request,
	device *userapi.Device,
	redpacketAPI api.RedPacketServiceInternalAPI,
) util.JSONResponse {
	vars := mux.Vars(req)
	roomID := vars["roomID"]

	var sendReq api.SendRedPacketRequest
	if err := json.NewDecoder(req.Body).Decode(&sendReq); err != nil {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.BadJSON("Invalid JSON"),
		}
	}

	sendReq.RoomID = roomID

	// 验证请求参数
	if sendReq.TotalAmount <= 0 {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam("total_amount must be positive"),
		}
	}
	if sendReq.TotalCount <= 0 {
		return util.JSONResponse{
			Code: http.StatusBadRequest,
			JSON: spec.InvalidParam("total_count must be positive"),
		}
	}

	userID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	response, err := redpacketAPI.SendRedPacket(req.Context(), &sendReq, *userID)
	if err != nil {
		logrus.WithError(err).Error("发送红包失败")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: response,
	}
}

// GrabRedPacket 处理抢红包请求
func GrabRedPacket(
	req *http.Request,
	device *userapi.Device,
	redpacketAPI api.RedPacketServiceInternalAPI,
) util.JSONResponse {
	vars := mux.Vars(req)
	eventID := vars["eventID"]

	grabReq := api.GrabRedPacketRequest{
		EventID: eventID,
	}

	userID, err := spec.NewUserID(device.UserID, true)
	if err != nil {
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	response, err := redpacketAPI.GrabRedPacket(req.Context(), &grabReq, *userID)
	if err != nil {
		logrus.WithError(err).Error("抢红包失败")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: response,
	}
}

// GetRedPacket 处理获取红包信息请求
func GetRedPacket(
	req *http.Request,
	device *userapi.Device,
	redpacketAPI api.RedPacketServiceInternalAPI,
) util.JSONResponse {
	vars := mux.Vars(req)
	eventID := vars["eventID"]

	getReq := api.GetRedPacketRequest{
		EventID: eventID,
	}

	response, err := redpacketAPI.GetRedPacket(req.Context(), &getReq)
	if err != nil {
		logrus.WithError(err).Error("获取红包信息失败")
		return util.JSONResponse{
			Code: http.StatusInternalServerError,
			JSON: spec.InternalServerError{},
		}
	}

	return util.JSONResponse{
		Code: http.StatusOK,
		JSON: response,
	}
}