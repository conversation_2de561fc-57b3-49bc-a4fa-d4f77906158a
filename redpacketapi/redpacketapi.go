// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包服务主包
package redpacketapi

import (
	"context"
	"fmt"
	"time"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/redpacketapi/api"
	"github.com/element-hq/dendrite/redpacketapi/internal"
	"github.com/element-hq/dendrite/redpacketapi/storage/postgres"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/gorilla/mux"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// AddInternalRoutes 添加内部路由（用于组件间通信）
func AddInternalRoutes(router *mux.Router, intAPI api.RedPacketServiceInternalAPI) {
	// 这里可以添加内部路由，如果需要的话
}

// NewInternalAPI 创建新的红包服务内部API实例
func NewInternalAPI(
	cfg *config.RedPacket,
	cm *sqlutil.Connections,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
) api.RedPacketServiceInternalAPI {
	if !cfg.Enabled {
		// 如果红包服务未启用，返回一个空的实现
		return &disabledRedPacketAPI{}
	}

	// 创建数据库连接 - 使用独立的数据库配置
	redpacketDB, err := postgres.NewDatabase(context.Background(), cm, &cfg.Database)
	if err != nil {
		logrus.WithError(err).Panic("无法连接到红包数据库")
	}

	// 创建红包服务实例
	redpacketService := internal.NewRedPacketService(cfg, redpacketDB, rsAPI, userAPI)

	// 启动过期红包清理任务
	go startExpiredRedPacketCleaner(redpacketService)

	return redpacketService
}

// startExpiredRedPacketCleaner 启动过期红包清理任务
func startExpiredRedPacketCleaner(service api.RedPacketServiceInternalAPI) {
	ticker := time.NewTicker(time.Hour) // 每小时检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			if err := service.ExpireRedPackets(ctx); err != nil {
				logrus.WithError(err).Error("清理过期红包失败")
			}
			cancel()
		}
	}
}

// disabledRedPacketAPI 禁用的红包API实现
type disabledRedPacketAPI struct{}

func (d *disabledRedPacketAPI) SendRedPacket(ctx context.Context, req *api.SendRedPacketRequest, senderID spec.UserID) (*api.SendRedPacketResponse, error) {
	return nil, fmt.Errorf("红包服务未启用")
}

func (d *disabledRedPacketAPI) GrabRedPacket(ctx context.Context, req *api.GrabRedPacketRequest, userID spec.UserID) (*api.GrabRedPacketResponse, error) {
	return nil, fmt.Errorf("红包服务未启用")
}

func (d *disabledRedPacketAPI) GetRedPacket(ctx context.Context, req *api.GetRedPacketRequest) (*api.GetRedPacketResponse, error) {
	return nil, fmt.Errorf("红包服务未启用")
}

func (d *disabledRedPacketAPI) GetRedPacketByEventID(ctx context.Context, eventID string) (*api.RedPacket, error) {
	return nil, fmt.Errorf("红包服务未启用")
}

func (d *disabledRedPacketAPI) ExpireRedPackets(ctx context.Context) error {
	return fmt.Errorf("红包服务未启用")
}