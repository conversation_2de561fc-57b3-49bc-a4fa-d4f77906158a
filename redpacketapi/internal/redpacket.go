// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包服务内部实现包
package internal

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math"
	"math/big"
	"time"

	"github.com/element-hq/dendrite/redpacketapi/api"
	"github.com/element-hq/dendrite/redpacketapi/storage"
	roomserverAPI "github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/setup/config"
	userapi "github.com/element-hq/dendrite/userapi/api"
	"github.com/google/uuid"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// RedPacketService 红包服务的内部实现
type RedPacketService struct {
	cfg     *config.RedPacket
	db      storage.Database
	rsAPI   roomserverAPI.ClientRoomserverAPI
	userAPI userapi.ClientUserAPI
}

// NewRedPacketService 创建新的红包服务实例
func NewRedPacketService(
	cfg *config.RedPacket,
	db storage.Database,
	rsAPI roomserverAPI.ClientRoomserverAPI,
	userAPI userapi.ClientUserAPI,
) *RedPacketService {
	return &RedPacketService{
		cfg:     cfg,
		db:      db,
		rsAPI:   rsAPI,
		userAPI: userAPI,
	}
}

// SendRedPacket 实现API接口 - 发送红包
func (rp *RedPacketService) SendRedPacket(ctx context.Context, req *api.SendRedPacketRequest, senderID spec.UserID) (*api.SendRedPacketResponse, error) {
	// 验证请求参数
	if err := rp.validateSendRequest(req); err != nil {
		return nil, err
	}

	// 先生成事件ID（模拟的）
	eventID := fmt.Sprintf("$%s:localhost", uuid.New().String())

	// 创建红包记录
	redpacket := &api.RedPacket{
		ID:              uuid.New().String(),
		EventID:         eventID, // 先设置事件ID
		RoomID:          req.RoomID,
		SenderID:        senderID.String(),
		Type:            req.Type,
		TotalAmount:     req.TotalAmount,
		TotalCount:      req.TotalCount,
		RemainingAmount: req.TotalAmount,
		RemainingCount:  req.TotalCount,
		Message:         req.Message,
		Status:          api.RedPacketStatusActive,
		CreatedAt:       time.Now(),
		ExpiresAt:       time.Now().Add(time.Duration(req.ExpiresIn) * time.Second),
	}

	// 保存到数据库
	if err := rp.db.InsertRedPacket(ctx, redpacket); err != nil {
		logrus.WithError(err).Error("保存红包记录失败")
		return nil, fmt.Errorf("保存红包记录失败: %w", err)
	}

	// 发送红包消息到房间（这里可以使用真实的房间服务器API）
	_, err := rp.sendRedPacketMessage(ctx, redpacket)
	if err != nil {
		logrus.WithError(err).Error("发送红包消息失败")
		return nil, fmt.Errorf("发送红包消息失败: %w", err)
	}

	return &api.SendRedPacketResponse{
		RedPacket: redpacket,
		EventID:   eventID,
	}, nil
}

// GrabRedPacket 实现API接口 - 抢红包
func (rp *RedPacketService) GrabRedPacket(ctx context.Context, req *api.GrabRedPacketRequest, userID spec.UserID) (*api.GrabRedPacketResponse, error) {
	// 获取红包信息
	redpacket, err := rp.db.GetRedPacketByEventID(ctx, req.EventID)
	if err != nil {
		return nil, fmt.Errorf("获取红包信息失败: %w", err)
	}
	if redpacket == nil {
		return &api.GrabRedPacketResponse{
			Success: false,
			Message: "红包不存在",
		}, nil
	}

	// 检查红包状态
	if redpacket.Status != api.RedPacketStatusActive {
		var message string
		switch redpacket.Status {
		case api.RedPacketStatusFinished:
			message = "红包已被抢完"
		case api.RedPacketStatusExpired:
			message = "红包已过期"
		default:
			message = "红包不可用"
		}
		return &api.GrabRedPacketResponse{
			Success:   false,
			Message:   message,
			RedPacket: redpacket,
		}, nil
	}

	// 检查是否已过期
	if time.Now().After(redpacket.ExpiresAt) {
		// 更新红包状态为过期
		rp.db.UpdateRedPacketStatus(ctx, redpacket.ID, api.RedPacketStatusExpired, redpacket.RemainingAmount, redpacket.RemainingCount)
		return &api.GrabRedPacketResponse{
			Success: false,
			Message: "红包已过期",
		}, nil
	}

	// 检查用户是否已经抢过
	existingGrab, err := rp.db.GetRedPacketGrabByUserAndRedPacket(ctx, userID.String(), redpacket.ID)
	if err != nil {
		return nil, fmt.Errorf("检查用户抢红包记录失败: %w", err)
	}
	if existingGrab != nil {
		return &api.GrabRedPacketResponse{
			Success:   false,
			Message:   "您已经抢过这个红包了",
			RedPacket: redpacket,
			Grab:      existingGrab,
		}, nil
	}

	// 检查红包是否还有剩余
	if redpacket.RemainingCount <= 0 || redpacket.RemainingAmount <= 0 {
		// 更新红包状态为已抢完
		rp.db.UpdateRedPacketStatus(ctx, redpacket.ID, api.RedPacketStatusFinished, 0, 0)
		return &api.GrabRedPacketResponse{
			Success: false,
			Message: "红包已被抢完",
		}, nil
	}

	// 计算抢到的金额
	amount := rp.calculateGrabAmount(redpacket)

	// 获取用户昵称
	userNickname := rp.getUserNickname(ctx, userID)

	// 创建抢红包记录
	grab := &api.RedPacketGrab{
		ID:           uuid.New().String(),
		RedPacketID:  redpacket.ID,
		UserID:       userID.String(),
		Amount:       amount,
		GrabbedAt:    time.Now(),
		IsLuckiest:   false,
		UserNickname: userNickname,
	}

	// 保存抢红包记录
	if err := rp.db.InsertRedPacketGrab(ctx, grab); err != nil {
		return nil, fmt.Errorf("保存抢红包记录失败: %w", err)
	}

	// 更新红包剩余金额和数量
	newRemainingAmount := redpacket.RemainingAmount - amount
	newRemainingCount := redpacket.RemainingCount - 1
	var newStatus api.RedPacketStatus = api.RedPacketStatusActive

	if newRemainingCount <= 0 || newRemainingAmount <= 0.01 {
		newStatus = api.RedPacketStatusFinished
		newRemainingAmount = 0
		newRemainingCount = 0

		// 如果红包被抢完，计算手气最佳
		go rp.calculateLuckiest(context.Background(), redpacket.ID)
	}

	if err := rp.db.UpdateRedPacketStatus(ctx, redpacket.ID, newStatus, newRemainingAmount, newRemainingCount); err != nil {
		logrus.WithError(err).Error("更新红包状态失败")
	}

	// 更新红包信息
	redpacket.RemainingAmount = newRemainingAmount
	redpacket.RemainingCount = newRemainingCount
	redpacket.Status = newStatus

	return &api.GrabRedPacketResponse{
		Success:   true,
		Amount:    amount,
		Message:   fmt.Sprintf("恭喜您抢到了 %.2f 元", amount),
		RedPacket: redpacket,
		Grab:      grab,
	}, nil
}

// GetRedPacket 实现API接口 - 获取红包信息
func (rp *RedPacketService) GetRedPacket(ctx context.Context, req *api.GetRedPacketRequest) (*api.GetRedPacketResponse, error) {
	redpacket, err := rp.db.GetRedPacketByEventID(ctx, req.EventID)
	if err != nil {
		return nil, fmt.Errorf("获取红包信息失败: %w", err)
	}
	if redpacket == nil {
		return nil, fmt.Errorf("红包不存在")
	}

	grabs, err := rp.db.GetRedPacketGrabsByRedPacketID(ctx, redpacket.ID)
	if err != nil {
		return nil, fmt.Errorf("获取红包领取记录失败: %w", err)
	}

	return &api.GetRedPacketResponse{
		RedPacket: redpacket,
		Grabs:     grabs,
	}, nil
}

// GetRedPacketByEventID 实现API接口 - 通过事件ID获取红包
func (rp *RedPacketService) GetRedPacketByEventID(ctx context.Context, eventID string) (*api.RedPacket, error) {
	return rp.db.GetRedPacketByEventID(ctx, eventID)
}

// ExpireRedPackets 实现API接口 - 处理过期红包
func (rp *RedPacketService) ExpireRedPackets(ctx context.Context) error {
	expiredRedPackets, err := rp.db.GetExpiredRedPackets(ctx, time.Now())
	if err != nil {
		return fmt.Errorf("获取过期红包失败: %w", err)
	}

	for _, redpacket := range expiredRedPackets {
		if err := rp.db.UpdateRedPacketStatus(ctx, redpacket.ID, api.RedPacketStatusExpired, redpacket.RemainingAmount, redpacket.RemainingCount); err != nil {
			logrus.WithError(err).WithField("redpacket_id", redpacket.ID).Error("更新过期红包状态失败")
		}
	}

	return nil
}

// validateSendRequest 验证发送红包请求
func (rp *RedPacketService) validateSendRequest(req *api.SendRedPacketRequest) error {
	if req.TotalAmount <= 0 {
		return fmt.Errorf("红包金额必须大于0")
	}
	if req.TotalCount <= 0 {
		return fmt.Errorf("红包个数必须大于0")
	}
	if req.TotalAmount < float64(req.TotalCount)*0.01 {
		return fmt.Errorf("红包金额太小，每个红包至少0.01元")
	}
	if req.ExpiresIn <= 0 {
		req.ExpiresIn = 86400 // 默认24小时过期
	}
	if req.Type != api.RedPacketTypeNormal && req.Type != api.RedPacketTypeLucky {
		req.Type = api.RedPacketTypeNormal // 默认普通红包
	}
	return nil
}

// calculateGrabAmount 计算抢红包金额
func (rp *RedPacketService) calculateGrabAmount(redpacket *api.RedPacket) float64 {
	if redpacket.Type == api.RedPacketTypeNormal {
		// 普通红包：平均分配
		return math.Round(redpacket.RemainingAmount/float64(redpacket.RemainingCount)*100) / 100
	}

	// 拼手气红包：随机分配
	if redpacket.RemainingCount == 1 {
		// 最后一个红包，返回剩余金额
		return redpacket.RemainingAmount
	}

	// 随机金额，确保剩余红包每个至少0.01元
	minAmount := 0.01
	maxAmount := redpacket.RemainingAmount - float64(redpacket.RemainingCount-1)*minAmount

	if maxAmount <= minAmount {
		return minAmount
	}

	// 生成随机金额
	randomFloat, _ := rand.Int(rand.Reader, big.NewInt(int64((maxAmount-minAmount)*100)))
	amount := minAmount + float64(randomFloat.Int64())/100

	return math.Round(amount*100) / 100
}

// getUserNickname 获取用户昵称
func (rp *RedPacketService) getUserNickname(ctx context.Context, userID spec.UserID) string {
	// 这里可以调用用户API获取用户信息
	// 暂时返回用户ID的本地部分
	return userID.Local()
}

// sendRedPacketMessage 发送红包消息到房间
func (rp *RedPacketService) sendRedPacketMessage(ctx context.Context, redpacket *api.RedPacket) (string, error) {
	// 构造红包消息内容
	content := api.RedPacketContent{
		MsgType:     "m.redpacket",
		Body:        fmt.Sprintf("🧧 %s", redpacket.Message),
		RedPacketID: redpacket.ID,
		Type:        redpacket.Type,
		Amount:      redpacket.TotalAmount,
		Count:       redpacket.TotalCount,
		Message:     redpacket.Message,
	}

	contentBytes, err := json.Marshal(content)
	if err != nil {
		return "", fmt.Errorf("序列化红包消息内容失败: %w", err)
	}

	// 这里需要调用房间服务器API发送消息
	// 暂时返回一个模拟的事件ID
	eventID := fmt.Sprintf("$%s:localhost", uuid.New().String())

	logrus.WithFields(logrus.Fields{
		"redpacket_id": redpacket.ID,
		"room_id":      redpacket.RoomID,
		"sender_id":    redpacket.SenderID,
		"content":      string(contentBytes),
	}).Info("发送红包消息")

	return eventID, nil
}

// calculateLuckiest 计算手气最佳
func (rp *RedPacketService) calculateLuckiest(ctx context.Context, redpacketID string) {
	luckiestGrab, err := rp.db.GetLuckiestGrab(ctx, redpacketID)
	if err != nil {
		logrus.WithError(err).Error("获取手气最佳记录失败")
		return
	}
	if luckiestGrab == nil {
		return
	}

	if err := rp.db.UpdateGrabLuckiest(ctx, luckiestGrab.ID, true); err != nil {
		logrus.WithError(err).Error("更新手气最佳标记失败")
	}
}
