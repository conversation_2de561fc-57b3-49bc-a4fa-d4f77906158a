// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// 红包存储接口定义
package storage

import (
	"context"
	"time"

	"github.com/element-hq/dendrite/redpacketapi/api"
)

// Database 红包数据库接口
type Database interface {
	// RedPackets 红包相关操作
	RedPackets
	// RedPacketGrabs 红包领取相关操作
	RedPacketGrabs
}

// RedPackets 红包表操作接口
type RedPackets interface {
	// InsertRedPacket 插入红包记录
	InsertRedPacket(ctx context.Context, redpacket *api.RedPacket) error
	
	// GetRedPacketByID 通过ID获取红包
	GetRedPacketByID(ctx context.Context, id string) (*api.RedPacket, error)
	
	// GetRedPacketByEventID 通过事件ID获取红包
	GetRedPacketByEventID(ctx context.Context, eventID string) (*api.RedPacket, error)
	
	// UpdateRedPacketStatus 更新红包状态
	UpdateRedPacketStatus(ctx context.Context, id string, status api.RedPacketStatus, remainingAmount float64, remainingCount int) error
	
	// GetExpiredRedPackets 获取过期的红包
	GetExpiredRedPackets(ctx context.Context, before time.Time) ([]*api.RedPacket, error)
	
	// GetRedPacketsByRoomID 获取房间内的红包列表
	GetRedPacketsByRoomID(ctx context.Context, roomID string, limit int, offset int) ([]*api.RedPacket, error)
}

// RedPacketGrabs 红包领取表操作接口
type RedPacketGrabs interface {
	// InsertRedPacketGrab 插入红包领取记录
	InsertRedPacketGrab(ctx context.Context, grab *api.RedPacketGrab) error
	
	// GetRedPacketGrabsByRedPacketID 获取红包的所有领取记录
	GetRedPacketGrabsByRedPacketID(ctx context.Context, redpacketID string) ([]*api.RedPacketGrab, error)
	
	// GetRedPacketGrabByUserAndRedPacket 获取用户在特定红包的领取记录
	GetRedPacketGrabByUserAndRedPacket(ctx context.Context, userID, redpacketID string) (*api.RedPacketGrab, error)
	
	// CountRedPacketGrabs 统计红包领取次数
	CountRedPacketGrabs(ctx context.Context, redpacketID string) (int, error)
	
	// GetLuckiestGrab 获取手气最佳的领取记录
	GetLuckiestGrab(ctx context.Context, redpacketID string) (*api.RedPacketGrab, error)
	
	// UpdateGrabLuckiest 更新手气最佳标记
	UpdateGrabLuckiest(ctx context.Context, grabID string, isLuckiest bool) error
}