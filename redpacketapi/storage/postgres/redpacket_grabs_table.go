// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package postgres

import (
	"context"
	"database/sql"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/redpacketapi/api"
)

const redpacketGrabsSchema = `
CREATE TABLE IF NOT EXISTS redpacketapi_redpacket_grabs (
    id TEXT PRIMARY KEY,
    redpacket_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    grabbed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    is_luckiest BOOLEAN NOT NULL DEFAULT FALSE,
    user_nickname TEXT NOT NULL DEFAULT '',
    FOREIGN KEY (redpacket_id) REFERENCES redpacketapi_redpackets(id) ON DELETE CASCADE,
    UNIQUE(redpacket_id, user_id)
);

CREATE INDEX IF NOT EXISTS redpacketapi_redpacket_grabs_redpacket_id_idx ON redpacketapi_redpacket_grabs(redpacket_id);
CREATE INDEX IF NOT EXISTS redpacketapi_redpacket_grabs_user_id_idx ON redpacketapi_redpacket_grabs(user_id);
CREATE INDEX IF NOT EXISTS redpacketapi_redpacket_grabs_amount_idx ON redpacketapi_redpacket_grabs(amount);
CREATE INDEX IF NOT EXISTS redpacketapi_redpacket_grabs_grabbed_at_idx ON redpacketapi_redpacket_grabs(grabbed_at);
`

const insertRedPacketGrabSQL = `
INSERT INTO redpacketapi_redpacket_grabs (
    id, redpacket_id, user_id, amount, grabbed_at, is_luckiest, user_nickname
) VALUES ($1, $2, $3, $4, $5, $6, $7)
`

const selectRedPacketGrabsByRedPacketIDSQL = `
SELECT id, redpacket_id, user_id, amount, grabbed_at, is_luckiest, user_nickname
FROM redpacketapi_redpacket_grabs 
WHERE redpacket_id = $1 
ORDER BY grabbed_at ASC
`

const selectRedPacketGrabByUserAndRedPacketSQL = `
SELECT id, redpacket_id, user_id, amount, grabbed_at, is_luckiest, user_nickname
FROM redpacketapi_redpacket_grabs 
WHERE user_id = $1 AND redpacket_id = $2
`

const countRedPacketGrabsSQL = `
SELECT COUNT(*) FROM redpacketapi_redpacket_grabs WHERE redpacket_id = $1
`

const selectLuckiestGrabSQL = `
SELECT id, redpacket_id, user_id, amount, grabbed_at, is_luckiest, user_nickname
FROM redpacketapi_redpacket_grabs 
WHERE redpacket_id = $1 
ORDER BY amount DESC 
LIMIT 1
`

const updateGrabLuckiestSQL = `
UPDATE redpacketapi_redpacket_grabs 
SET is_luckiest = $2 
WHERE id = $1
`

type redpacketGrabsStatements struct {
	insertRedPacketGrabStmt                   *sql.Stmt
	selectRedPacketGrabsByRedPacketIDStmt     *sql.Stmt
	selectRedPacketGrabByUserAndRedPacketStmt *sql.Stmt
	countRedPacketGrabsStmt                   *sql.Stmt
	selectLuckiestGrabStmt                    *sql.Stmt
	updateGrabLuckiestStmt                    *sql.Stmt
}

func CreateRedPacketGrabsTable(db *sql.DB) error {
	_, err := db.Exec(redpacketGrabsSchema)
	return err
}

func PrepareRedPacketGrabsTable(db *sql.DB) (tables *redpacketGrabsStatements, err error) {
	tables = &redpacketGrabsStatements{}

	return tables, sqlutil.StatementList{
		{&tables.insertRedPacketGrabStmt, insertRedPacketGrabSQL},
		{&tables.selectRedPacketGrabsByRedPacketIDStmt, selectRedPacketGrabsByRedPacketIDSQL},
		{&tables.selectRedPacketGrabByUserAndRedPacketStmt, selectRedPacketGrabByUserAndRedPacketSQL},
		{&tables.countRedPacketGrabsStmt, countRedPacketGrabsSQL},
		{&tables.selectLuckiestGrabStmt, selectLuckiestGrabSQL},
		{&tables.updateGrabLuckiestStmt, updateGrabLuckiestSQL},
	}.Prepare(db)
}

func (s *redpacketGrabsStatements) InsertRedPacketGrab(
	ctx context.Context, grab *api.RedPacketGrab,
) error {
	_, err := s.insertRedPacketGrabStmt.ExecContext(
		ctx,
		grab.ID,
		grab.RedPacketID,
		grab.UserID,
		grab.Amount,
		grab.GrabbedAt,
		grab.IsLuckiest,
		grab.UserNickname,
	)
	return err
}

func (s *redpacketGrabsStatements) GetRedPacketGrabsByRedPacketID(
	ctx context.Context, redpacketID string,
) ([]*api.RedPacketGrab, error) {
	rows, err := s.selectRedPacketGrabsByRedPacketIDStmt.QueryContext(ctx, redpacketID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var grabs []*api.RedPacketGrab
	for rows.Next() {
		var grab api.RedPacketGrab
		if err := rows.Scan(
			&grab.ID,
			&grab.RedPacketID,
			&grab.UserID,
			&grab.Amount,
			&grab.GrabbedAt,
			&grab.IsLuckiest,
			&grab.UserNickname,
		); err != nil {
			return nil, err
		}
		grabs = append(grabs, &grab)
	}
	return grabs, rows.Err()
}

func (s *redpacketGrabsStatements) GetRedPacketGrabByUserAndRedPacket(
	ctx context.Context, userID, redpacketID string,
) (*api.RedPacketGrab, error) {
	var grab api.RedPacketGrab
	err := s.selectRedPacketGrabByUserAndRedPacketStmt.QueryRowContext(ctx, userID, redpacketID).Scan(
		&grab.ID,
		&grab.RedPacketID,
		&grab.UserID,
		&grab.Amount,
		&grab.GrabbedAt,
		&grab.IsLuckiest,
		&grab.UserNickname,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &grab, nil
}

func (s *redpacketGrabsStatements) CountRedPacketGrabs(
	ctx context.Context, redpacketID string,
) (int, error) {
	var count int
	err := s.countRedPacketGrabsStmt.QueryRowContext(ctx, redpacketID).Scan(&count)
	return count, err
}

func (s *redpacketGrabsStatements) GetLuckiestGrab(
	ctx context.Context, redpacketID string,
) (*api.RedPacketGrab, error) {
	var grab api.RedPacketGrab
	err := s.selectLuckiestGrabStmt.QueryRowContext(ctx, redpacketID).Scan(
		&grab.ID,
		&grab.RedPacketID,
		&grab.UserID,
		&grab.Amount,
		&grab.GrabbedAt,
		&grab.IsLuckiest,
		&grab.UserNickname,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &grab, nil
}

func (s *redpacketGrabsStatements) UpdateGrabLuckiest(
	ctx context.Context, grabID string, isLuckiest bool,
) error {
	_, err := s.updateGrabLuckiestStmt.ExecContext(ctx, grabID, isLuckiest)
	return err
}