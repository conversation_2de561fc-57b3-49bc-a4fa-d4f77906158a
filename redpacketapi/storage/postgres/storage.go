// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/redpacketapi/api"
	"github.com/element-hq/dendrite/redpacketapi/storage"
	"github.com/element-hq/dendrite/setup/config"
)

// Database 红包数据库PostgreSQL实现
type Database struct {
	db                    *sql.DB
	writer                sqlutil.Writer
	redpacketsStatements  *redpacketsStatements
	redpacketGrabsStatements *redpacketGrabsStatements
}

// NewDatabase 创建新的PostgreSQL数据库实例
func NewDatabase(ctx context.Context, conMan *sqlutil.Connections, dbProperties *config.DatabaseOptions) (storage.Database, error) {
	db, writer, err := conMan.Connection(dbProperties)
	if err != nil {
		return nil, err
	}

	// 创建表
	if err := CreateRedPacketsTable(db); err != nil {
		return nil, err
	}
	if err := CreateRedPacketGrabsTable(db); err != nil {
		return nil, err
	}

	// 准备语句
	redpacketsStatements, err := PrepareRedPacketsTable(db)
	if err != nil {
		return nil, err
	}
	redpacketGrabsStatements, err := PrepareRedPacketGrabsTable(db)
	if err != nil {
		return nil, err
	}

	return &Database{
		db:                       db,
		writer:                   writer,
		redpacketsStatements:     redpacketsStatements,
		redpacketGrabsStatements: redpacketGrabsStatements,
	}, nil
}

// InsertRedPacket 插入红包记录
func (d *Database) InsertRedPacket(ctx context.Context, redpacket *api.RedPacket) error {
	return d.writer.Do(d.db, nil, func(txn *sql.Tx) error {
		return d.redpacketsStatements.InsertRedPacket(ctx, redpacket)
	})
}

// GetRedPacketByID 通过ID获取红包
func (d *Database) GetRedPacketByID(ctx context.Context, id string) (*api.RedPacket, error) {
	return d.redpacketsStatements.GetRedPacketByID(ctx, id)
}

// GetRedPacketByEventID 通过事件ID获取红包
func (d *Database) GetRedPacketByEventID(ctx context.Context, eventID string) (*api.RedPacket, error) {
	return d.redpacketsStatements.GetRedPacketByEventID(ctx, eventID)
}

// UpdateRedPacketStatus 更新红包状态
func (d *Database) UpdateRedPacketStatus(ctx context.Context, id string, status api.RedPacketStatus, remainingAmount float64, remainingCount int) error {
	return d.writer.Do(d.db, nil, func(txn *sql.Tx) error {
		return d.redpacketsStatements.UpdateRedPacketStatus(ctx, id, status, remainingAmount, remainingCount)
	})
}

// GetExpiredRedPackets 获取过期的红包
func (d *Database) GetExpiredRedPackets(ctx context.Context, before time.Time) ([]*api.RedPacket, error) {
	return d.redpacketsStatements.GetExpiredRedPackets(ctx, before)
}

// GetRedPacketsByRoomID 获取房间内的红包列表
func (d *Database) GetRedPacketsByRoomID(ctx context.Context, roomID string, limit int, offset int) ([]*api.RedPacket, error) {
	return d.redpacketsStatements.GetRedPacketsByRoomID(ctx, roomID, limit, offset)
}

// InsertRedPacketGrab 插入红包领取记录
func (d *Database) InsertRedPacketGrab(ctx context.Context, grab *api.RedPacketGrab) error {
	return d.writer.Do(d.db, nil, func(txn *sql.Tx) error {
		return d.redpacketGrabsStatements.InsertRedPacketGrab(ctx, grab)
	})
}

// GetRedPacketGrabsByRedPacketID 获取红包的所有领取记录
func (d *Database) GetRedPacketGrabsByRedPacketID(ctx context.Context, redpacketID string) ([]*api.RedPacketGrab, error) {
	return d.redpacketGrabsStatements.GetRedPacketGrabsByRedPacketID(ctx, redpacketID)
}

// GetRedPacketGrabByUserAndRedPacket 获取用户在特定红包的领取记录
func (d *Database) GetRedPacketGrabByUserAndRedPacket(ctx context.Context, userID, redpacketID string) (*api.RedPacketGrab, error) {
	return d.redpacketGrabsStatements.GetRedPacketGrabByUserAndRedPacket(ctx, userID, redpacketID)
}

// CountRedPacketGrabs 统计红包领取次数
func (d *Database) CountRedPacketGrabs(ctx context.Context, redpacketID string) (int, error) {
	return d.redpacketGrabsStatements.CountRedPacketGrabs(ctx, redpacketID)
}

// GetLuckiestGrab 获取手气最佳的领取记录
func (d *Database) GetLuckiestGrab(ctx context.Context, redpacketID string) (*api.RedPacketGrab, error) {
	return d.redpacketGrabsStatements.GetLuckiestGrab(ctx, redpacketID)
}

// UpdateGrabLuckiest 更新手气最佳标记
func (d *Database) UpdateGrabLuckiest(ctx context.Context, grabID string, isLuckiest bool) error {
	return d.writer.Do(d.db, nil, func(txn *sql.Tx) error {
		return d.redpacketGrabsStatements.UpdateGrabLuckiest(ctx, grabID, isLuckiest)
	})
}