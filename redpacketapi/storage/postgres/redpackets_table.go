// Copyright 2024 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/redpacketapi/api"
)

const redpacketsSchema = `
CREATE TABLE IF NOT EXISTS redpacketapi_redpackets (
    id TEXT PRIMARY KEY,
    event_id TEXT NOT NULL UNIQUE,
    room_id TEXT NOT NULL,
    sender_id TEXT NOT NULL,
    type TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    total_count INTEGER NOT NULL,
    remaining_amount DECIMAL(10,2) NOT NULL,
    remaining_count INTEGER NOT NULL,
    message TEXT NOT NULL DEFAULT '',
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL
);

CREATE INDEX IF NOT EXISTS redpacketapi_redpackets_event_id_idx ON redpacketapi_redpackets(event_id);
CREATE INDEX IF NOT EXISTS redpacketapi_redpackets_room_id_idx ON redpacketapi_redpackets(room_id);
CREATE INDEX IF NOT EXISTS redpacketapi_redpackets_sender_id_idx ON redpacketapi_redpackets(sender_id);
CREATE INDEX IF NOT EXISTS redpacketapi_redpackets_status_idx ON redpacketapi_redpackets(status);
CREATE INDEX IF NOT EXISTS redpacketapi_redpackets_expires_at_idx ON redpacketapi_redpackets(expires_at);
`

const insertRedPacketSQL = `
INSERT INTO redpacketapi_redpackets (
    id, event_id, room_id, sender_id, type, total_amount, total_count,
    remaining_amount, remaining_count, message, status, created_at, expires_at
) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
`

const selectRedPacketByIDSQL = `
SELECT id, event_id, room_id, sender_id, type, total_amount, total_count,
       remaining_amount, remaining_count, message, status, created_at, expires_at
FROM redpacketapi_redpackets WHERE id = $1
`

const selectRedPacketByEventIDSQL = `
SELECT id, event_id, room_id, sender_id, type, total_amount, total_count,
       remaining_amount, remaining_count, message, status, created_at, expires_at
FROM redpacketapi_redpackets WHERE event_id = $1
`

const updateRedPacketStatusSQL = `
UPDATE redpacketapi_redpackets 
SET status = $2, remaining_amount = $3, remaining_count = $4
WHERE id = $1
`

const selectExpiredRedPacketsSQL = `
SELECT id, event_id, room_id, sender_id, type, total_amount, total_count,
       remaining_amount, remaining_count, message, status, created_at, expires_at
FROM redpacketapi_redpackets 
WHERE expires_at < $1 AND status = 'active'
`

const selectRedPacketsByRoomIDSQL = `
SELECT id, event_id, room_id, sender_id, type, total_amount, total_count,
       remaining_amount, remaining_count, message, status, created_at, expires_at
FROM redpacketapi_redpackets 
WHERE room_id = $1 
ORDER BY created_at DESC 
LIMIT $2 OFFSET $3
`

type redpacketsStatements struct {
	insertRedPacketStmt           *sql.Stmt
	selectRedPacketByIDStmt       *sql.Stmt
	selectRedPacketByEventIDStmt  *sql.Stmt
	updateRedPacketStatusStmt     *sql.Stmt
	selectExpiredRedPacketsStmt   *sql.Stmt
	selectRedPacketsByRoomIDStmt  *sql.Stmt
}

func CreateRedPacketsTable(db *sql.DB) error {
	_, err := db.Exec(redpacketsSchema)
	return err
}

func PrepareRedPacketsTable(db *sql.DB) (tables *redpacketsStatements, err error) {
	tables = &redpacketsStatements{}

	return tables, sqlutil.StatementList{
		{&tables.insertRedPacketStmt, insertRedPacketSQL},
		{&tables.selectRedPacketByIDStmt, selectRedPacketByIDSQL},
		{&tables.selectRedPacketByEventIDStmt, selectRedPacketByEventIDSQL},
		{&tables.updateRedPacketStatusStmt, updateRedPacketStatusSQL},
		{&tables.selectExpiredRedPacketsStmt, selectExpiredRedPacketsSQL},
		{&tables.selectRedPacketsByRoomIDStmt, selectRedPacketsByRoomIDSQL},
	}.Prepare(db)
}

func (s *redpacketsStatements) InsertRedPacket(
	ctx context.Context, redpacket *api.RedPacket,
) error {
	_, err := s.insertRedPacketStmt.ExecContext(
		ctx,
		redpacket.ID,
		redpacket.EventID,
		redpacket.RoomID,
		redpacket.SenderID,
		redpacket.Type,
		redpacket.TotalAmount,
		redpacket.TotalCount,
		redpacket.RemainingAmount,
		redpacket.RemainingCount,
		redpacket.Message,
		redpacket.Status,
		redpacket.CreatedAt,
		redpacket.ExpiresAt,
	)
	return err
}

func (s *redpacketsStatements) GetRedPacketByID(
	ctx context.Context, id string,
) (*api.RedPacket, error) {
	var redpacket api.RedPacket
	err := s.selectRedPacketByIDStmt.QueryRowContext(ctx, id).Scan(
		&redpacket.ID,
		&redpacket.EventID,
		&redpacket.RoomID,
		&redpacket.SenderID,
		&redpacket.Type,
		&redpacket.TotalAmount,
		&redpacket.TotalCount,
		&redpacket.RemainingAmount,
		&redpacket.RemainingCount,
		&redpacket.Message,
		&redpacket.Status,
		&redpacket.CreatedAt,
		&redpacket.ExpiresAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &redpacket, nil
}

func (s *redpacketsStatements) GetRedPacketByEventID(
	ctx context.Context, eventID string,
) (*api.RedPacket, error) {
	var redpacket api.RedPacket
	err := s.selectRedPacketByEventIDStmt.QueryRowContext(ctx, eventID).Scan(
		&redpacket.ID,
		&redpacket.EventID,
		&redpacket.RoomID,
		&redpacket.SenderID,
		&redpacket.Type,
		&redpacket.TotalAmount,
		&redpacket.TotalCount,
		&redpacket.RemainingAmount,
		&redpacket.RemainingCount,
		&redpacket.Message,
		&redpacket.Status,
		&redpacket.CreatedAt,
		&redpacket.ExpiresAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, err
	}
	return &redpacket, nil
}

func (s *redpacketsStatements) UpdateRedPacketStatus(
	ctx context.Context, id string, status api.RedPacketStatus, remainingAmount float64, remainingCount int,
) error {
	_, err := s.updateRedPacketStatusStmt.ExecContext(ctx, id, status, remainingAmount, remainingCount)
	return err
}

func (s *redpacketsStatements) GetExpiredRedPackets(
	ctx context.Context, before time.Time,
) ([]*api.RedPacket, error) {
	rows, err := s.selectExpiredRedPacketsStmt.QueryContext(ctx, before)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var redpackets []*api.RedPacket
	for rows.Next() {
		var redpacket api.RedPacket
		if err := rows.Scan(
			&redpacket.ID,
			&redpacket.EventID,
			&redpacket.RoomID,
			&redpacket.SenderID,
			&redpacket.Type,
			&redpacket.TotalAmount,
			&redpacket.TotalCount,
			&redpacket.RemainingAmount,
			&redpacket.RemainingCount,
			&redpacket.Message,
			&redpacket.Status,
			&redpacket.CreatedAt,
			&redpacket.ExpiresAt,
		); err != nil {
			return nil, err
		}
		redpackets = append(redpackets, &redpacket)
	}
	return redpackets, rows.Err()
}

func (s *redpacketsStatements) GetRedPacketsByRoomID(
	ctx context.Context, roomID string, limit int, offset int,
) ([]*api.RedPacket, error) {
	rows, err := s.selectRedPacketsByRoomIDStmt.QueryContext(ctx, roomID, limit, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var redpackets []*api.RedPacket
	for rows.Next() {
		var redpacket api.RedPacket
		if err := rows.Scan(
			&redpacket.ID,
			&redpacket.EventID,
			&redpacket.RoomID,
			&redpacket.SenderID,
			&redpacket.Type,
			&redpacket.TotalAmount,
			&redpacket.TotalCount,
			&redpacket.RemainingAmount,
			&redpacket.RemainingCount,
			&redpacket.Message,
			&redpacket.Status,
			&redpacket.CreatedAt,
			&redpacket.ExpiresAt,
		); err != nil {
			return nil, err
		}
		redpackets = append(redpackets, &redpacket)
	}
	return redpackets, rows.Err()
}