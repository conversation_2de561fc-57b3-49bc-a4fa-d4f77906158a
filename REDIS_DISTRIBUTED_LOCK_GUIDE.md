# Redis 分布式锁生产部署指南

## 🚀 **Redis 分布式锁优势**

相比数据库锁，Redis 分布式锁具有以下优势：

### **性能优势**
- ✅ **高性能**: Redis 内存操作，延迟极低
- ✅ **高并发**: 支持大量并发锁操作
- ✅ **原子操作**: SET NX EX 原子获取锁

### **可靠性优势**
- ✅ **自动过期**: 锁会自动过期，防止死锁
- ✅ **唯一标识**: 每个锁有唯一值，防止误释放
- ✅ **Lua 脚本**: 原子性释放和延长锁

### **运维优势**
- ✅ **独立部署**: Redis 可独立扩展
- ✅ **监控友好**: 丰富的监控指标
- ✅ **故障隔离**: Redis 故障不影响主业务

## 🔧 **配置示例**

### **1. dendrite.yaml 配置**

```yaml
# 全局配置
global:
  server_name: "your-server.com"
  private_key: "matrix_key.pem"
  key_id: "ed25519:1"

# 房间服务器配置
room_server:
  database:
    connection_string: "postgresql://user:pass@localhost/dendrite_roomserver"
  
  # 阅后即焚消息配置
  expiring_messages:
    enabled: true
    check_interval: "30s"
    batch_size: 100
    cleanup_days: 7
    max_expire_days: 30
    
    # Redis 分布式锁配置
    redis:
      enabled: true
      address: "localhost:6379"
      password: ""
      database: 0
```

### **2. 生产环境配置**

```yaml
room_server:
  expiring_messages:
    enabled: true
    check_interval: "10s"    # 更频繁的检查
    batch_size: 200          # 更大的批量处理
    cleanup_days: 3          # 更短的保留期
    max_expire_days: 7       # 更短的最大过期时间
    
    redis:
      enabled: true
      address: "redis-cluster.internal:6379"
      password: "your-redis-password"
      database: 1             # 使用专用数据库
```

### **3. 高可用配置**

```yaml
room_server:
  expiring_messages:
    redis:
      enabled: true
      # Redis Sentinel 配置
      address: "redis-sentinel-1:26379,redis-sentinel-2:26379,redis-sentinel-3:26379"
      password: "your-redis-password"
      database: 1
      # 或者 Redis Cluster 配置
      # address: "redis-cluster-1:6379,redis-cluster-2:6379,redis-cluster-3:6379"
```

## 📊 **部署架构**

### **单机部署**
```
┌─────────────┐    ┌─────────────┐
│  Dendrite   │    │    Redis    │
│   Server    │───▶│   Server    │
│             │    │             │
└─────────────┘    └─────────────┘
```

### **多机部署**
```
┌─────────────┐    ┌─────────────┐
│ Dendrite-1  │───▶│             │
└─────────────┘    │             │
┌─────────────┐    │    Redis    │
│ Dendrite-2  │───▶│   Cluster   │
└─────────────┘    │             │
┌─────────────┐    │             │
│ Dendrite-3  │───▶│             │
└─────────────┘    └─────────────┘
```

### **高可用部署**
```
┌─────────────┐    ┌─────────────┐
│ Dendrite-1  │───▶│ Redis-M-1   │
└─────────────┘    └─────────────┘
┌─────────────┐    ┌─────────────┐
│ Dendrite-2  │───▶│ Redis-S-1   │
└─────────────┘    └─────────────┘
┌─────────────┐    ┌─────────────┐
│ Dendrite-3  │───▶│ Redis-S-2   │
└─────────────┘    └─────────────┘
```

## 🔍 **工作流程**

### **1. 锁获取流程**
```
1. 生成唯一锁值: server-1-12345-abc123
2. 执行 SET lock_key lock_value NX EX 300
3. 如果返回 OK，获取锁成功
4. 如果返回 nil，锁被其他实例持有
```

### **2. 锁释放流程**
```lua
-- Lua 脚本确保原子性
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("DEL", KEYS[1])
else
    return 0
end
```

### **3. 锁延长流程**
```lua
-- 延长锁的过期时间
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("EXPIRE", KEYS[1], ARGV[2])
else
    return 0
end
```

## 📋 **预期日志输出**

### **启动日志**
```
INFO Redis 分布式锁管理器初始化成功 redis_addr="localhost:6379"
INFO Redis 分布式锁已启用
INFO 创建阅后即焚消息服务 redis_enabled=true
```

### **运行日志**
```
DEBUG 成功获取 Redis 分布式锁 lock_key="expiring_messages_processing" instance_id="server-1-12345"
INFO 发现过期消息，开始处理 count=5 server_id="server-1-12345"
DEBUG 成功获取分布式锁 lock_key="expiring_message_$abc123" instance_id="server-1-12345"
DEBUG 成功释放 Redis 分布式锁 lock_key="expiring_message_$abc123"
```

### **多机协调日志**
```
# 实例1
DEBUG 成功获取 Redis 分布式锁 lock_key="expiring_messages_processing"
INFO 发现过期消息，开始处理 count=3

# 实例2
DEBUG 锁已被其他实例持有 lock_key="expiring_messages_processing" instance_id="server-2-12346"
DEBUG 其他实例正在处理过期消息，跳过本次处理

# 实例3
DEBUG 消息正在被其他实例处理 event_id="$abc123"
```

## 🧪 **测试验证**

### **1. 功能测试**
```bash
# 启动多个 Dendrite 实例
./dendrite -config dendrite1.yaml &
./dendrite -config dendrite2.yaml &
./dendrite -config dendrite3.yaml &

# 发送过期消息
curl -X PUT 'http://localhost:8008/_matrix/client/v3/rooms/!room:server.com/send/m.room.message/test' \
  -H 'Authorization: Bearer TOKEN' \
  -d '{"msgtype": "m.text", "body": "test", "expire_ts": 1750672109}'
```

### **2. Redis 监控**
```bash
# 查看当前锁
redis-cli KEYS "expiring_*"

# 监控锁操作
redis-cli MONITOR | grep expiring

# 查看锁信息
redis-cli GET expiring_messages_processing
redis-cli TTL expiring_messages_processing
```

### **3. 性能测试**
```bash
# 发送大量过期消息
for i in {1..1000}; do
  curl -X PUT "http://localhost:8008/_matrix/client/v3/rooms/!room:server.com/send/m.room.message/test$i" \
    -H 'Authorization: Bearer TOKEN' \
    -d "{\"msgtype\": \"m.text\", \"body\": \"test $i\", \"expire_ts\": $(date +%s)}"
done
```

## ⚠️ **注意事项**

### **1. Redis 高可用**
- 使用 Redis Sentinel 或 Cluster 模式
- 配置适当的故障转移时间
- 监控 Redis 连接状态

### **2. 锁超时设置**
- 默认锁超时 5 分钟
- 根据消息处理时间调整
- 避免锁超时过短导致重复处理

### **3. 网络分区处理**
- Redis 不可用时回退到单机模式
- 记录降级日志
- 网络恢复后自动重新启用分布式锁

### **4. 性能优化**
- 使用 Redis 连接池
- 批量处理锁操作
- 监控锁竞争情况

## 🎯 **最佳实践**

1. **Redis 部署**: 使用专用 Redis 实例，避免与其他业务混用
2. **监控告警**: 监控锁获取失败率、处理延迟等指标
3. **故障预案**: 准备 Redis 故障时的降级方案
4. **容量规划**: 根据消息量规划 Redis 容量
5. **安全配置**: 使用密码认证，限制网络访问

通过 Redis 分布式锁，可以确保阅后即焚功能在多机部署环境中高效、可靠地工作！
