#!/bin/bash

# 红包抢取记录调试脚本

echo "🔍 红包抢取记录调试开始..."

# 数据库连接信息
DB_HOST="*************"
DB_PORT="5000"
DB_USER="zhou<PERSON>ayi"
DB_NAME="dendrite"

echo "📋 步骤1: 检查红包主表记录"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT id, event_id, room_id, sender_id, type, total_amount, total_count, 
       remaining_amount, remaining_count, status, created_at 
FROM redpacketapi_redpackets 
ORDER BY created_at DESC 
LIMIT 5;
"

echo ""
echo "📋 步骤2: 检查红包抢取记录表"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT id, redpacket_id, user_id, amount, grabbed_at, is_luckiest, user_nickname 
FROM redpacketapi_redpacket_grabs 
ORDER BY grabbed_at DESC 
LIMIT 10;
"

echo ""
echo "📋 步骤3: 检查红包抢取记录总数"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT COUNT(*) as total_grabs FROM redpacketapi_redpacket_grabs;
"

echo ""
echo "📋 步骤4: 检查最新红包的抢取情况"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    r.id as redpacket_id,
    r.total_amount,
    r.total_count,
    r.remaining_amount,
    r.remaining_count,
    r.status,
    COUNT(g.id) as grab_count,
    COALESCE(SUM(g.amount), 0) as grabbed_amount
FROM redpacketapi_redpackets r
LEFT JOIN redpacketapi_redpacket_grabs g ON r.id = g.redpacket_id
WHERE r.created_at > NOW() - INTERVAL '1 hour'
GROUP BY r.id, r.total_amount, r.total_count, r.remaining_amount, r.remaining_count, r.status
ORDER BY r.created_at DESC;
"

echo ""
echo "📋 步骤5: 检查表结构"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
\d redpacketapi_redpacket_grabs
"

echo ""
echo "📋 步骤6: 检查外键约束"
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
  AND tc.table_name='redpacketapi_redpacket_grabs';
"

echo ""
echo "🔍 红包抢取记录调试完成！"