# 红包功能实现文档

## 概述

本文档描述了在Dendrite Matrix服务器中实现的完整红包功能。红包功能允许用户在Matrix房间中发送和接收红包，支持普通红包和拼手气红包两种类型。

## 功能特性

### 核心功能
- ✅ **发送红包**: 用户可以在房间中发送红包，指定金额、个数和祝福语
- ✅ **抢红包**: 房间成员可以抢红包，获得随机或平均分配的金额
- ✅ **红包类型**: 支持普通红包（平均分配）和拼手气红包（随机分配）
- ✅ **手气最佳**: 拼手气红包会自动计算并标记手气最佳
- ✅ **过期处理**: 红包支持过期时间，过期后自动失效
- ✅ **防重复抢取**: 每个用户只能抢取同一个红包一次

### 安全特性
- ✅ **参数验证**: 严格验证红包金额、个数等参数
- ✅ **并发安全**: 使用数据库事务确保并发抢红包的安全性
- ✅ **权限控制**: 只有房间成员才能抢红包
- ✅ **金额限制**: 可配置红包最大金额和个数限制

## 架构设计

### 模块结构
```
redpacketapi/
├── api/
│   └── api.go              # API接口定义
├── internal/
│   └── redpacket.go        # 核心业务逻辑
├── storage/
│   ├── interface.go        # 存储接口
│   └── postgres/           # PostgreSQL实现
│       ├── redpackets_table.go
│       ├── redpacket_grabs_table.go
│       └── storage.go
├── routing/
│   └── routing.go          # HTTP路由处理
└── redpacketapi.go         # 服务入口
```

### 数据库设计

#### 红包表 (redpacketapi_redpackets)
```sql
CREATE TABLE redpacketapi_redpackets (
    id TEXT PRIMARY KEY,
    event_id TEXT NOT NULL UNIQUE,
    room_id TEXT NOT NULL,
    sender_id TEXT NOT NULL,
    type TEXT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    total_count INTEGER NOT NULL,
    remaining_amount DECIMAL(10,2) NOT NULL,
    remaining_count INTEGER NOT NULL,
    message TEXT NOT NULL DEFAULT '',
    status TEXT NOT NULL DEFAULT 'active',
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMP NOT NULL
);
```

#### 红包领取记录表 (redpacketapi_redpacket_grabs)
```sql
CREATE TABLE redpacketapi_redpacket_grabs (
    id TEXT PRIMARY KEY,
    redpacket_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    grabbed_at TIMESTAMP NOT NULL DEFAULT NOW(),
    is_luckiest BOOLEAN NOT NULL DEFAULT FALSE,
    user_nickname TEXT NOT NULL DEFAULT '',
    UNIQUE(redpacket_id, user_id)
);
```

## API接口

### 发送红包
```http
POST /_matrix/client/r0/rooms/{roomID}/redpacket
Content-Type: application/json

{
    "type": "lucky",
    "total_amount": 10.0,
    "total_count": 5,
    "message": "恭喜发财",
    "expires_in": 86400
}
```

### 抢红包
```http
POST /_matrix/client/r0/redpacket/{eventID}/grab
```

### 查询红包信息
```http
GET /_matrix/client/r0/redpacket/{eventID}
```

## 配置说明

在 `dendrite.yaml` 中添加红包服务配置：

```yaml
redpacket:
  # 是否启用红包服务
  enabled: true
  
  # 红包最大金额限制
  max_amount: 200.0
  
  # 红包最大个数限制
  max_count: 100
  
  # 红包默认过期时间（秒）
  default_expires_in: 86400
  
  # 红包最小金额
  min_amount: 0.01
  
  # 是否允许拼手气红包
  allow_lucky_redpacket: true
  
  # 数据库配置
  database:
    connection_string: "**************************************************************************"
    max_open_conns: 90
    max_idle_conns: 5
    conn_max_lifetime: -1
```

## 集成说明

### 1. 配置文件更新
- 在 `setup/config/config_redpacket.go` 中定义红包配置结构
- 在 `setup/config/config.go` 中集成红包配置到主配置

### 2. 服务初始化
- 在 `setup/monolith.go` 中添加红包API到Monolith结构体
- 实现 `NewRedPacketAPI` 方法初始化红包服务
- 在 `AddAllPublicRoutes` 中添加红包路由

### 3. 数据库集成
- PostgreSQL存储实现支持事务和并发安全
- 自动创建数据库表和索引
- 支持连接池和性能优化

## 使用示例

### 发送普通红包
```bash
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/redpacket" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "normal",
    "total_amount": 10.0,
    "total_count": 5,
    "message": "平分红包"
  }'
```

### 发送拼手气红包
```bash
curl -X POST "http://localhost:8008/_matrix/client/r0/rooms/!room:localhost/redpacket" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lucky",
    "total_amount": 10.0,
    "total_count": 5,
    "message": "拼手气红包"
  }'
```

### 抢红包
```bash
curl -X POST "http://localhost:8008/_matrix/client/r0/redpacket/$EVENT_ID/grab" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 技术特点

### 1. 高性能
- 使用数据库连接池
- 优化的SQL查询和索引
- 异步过期红包清理

### 2. 高可靠性
- 数据库事务保证一致性
- 错误处理和日志记录
- 防止重复抢取和超额分配

### 3. 可扩展性
- 模块化设计，易于扩展
- 支持不同的存储后端
- 可配置的限制和参数

### 4. 安全性
- 严格的参数验证
- 权限控制和身份验证
- 防止恶意攻击和滥用

## 监控和维护

### 日志记录
- 红包创建、抢取、过期等关键操作都有详细日志
- 错误日志包含足够的上下文信息便于调试

### 性能监控
- 数据库查询性能监控
- API响应时间监控
- 红包使用统计

### 定期维护
- 自动清理过期红包
- 数据库性能优化
- 配置参数调优

## 未来扩展

### 可能的功能扩展
1. **红包统计**: 用户红包收发统计
2. **红包历史**: 红包历史记录查询
3. **红包模板**: 预设红包模板
4. **红包动画**: 客户端红包动画效果
5. **红包通知**: 红包相关的推送通知
6. **红包限制**: 更细粒度的红包限制规则

### 技术优化
1. **缓存优化**: 添加Redis缓存提升性能
2. **分布式锁**: 支持分布式部署的并发控制
3. **消息队列**: 异步处理红包相关任务
4. **监控告警**: 完善的监控和告警系统

## 总结

红包功能的实现遵循了Dendrite的架构设计原则，提供了完整、安全、高性能的红包服务。通过模块化的设计，该功能可以轻松集成到现有的Dendrite部署中，为Matrix用户提供有趣的社交功能。

该实现考虑了实际使用场景中的各种需求，包括安全性、性能、可扩展性等方面，为未来的功能扩展奠定了良好的基础。