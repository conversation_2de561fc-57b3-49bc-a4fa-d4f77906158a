# AI消息处理流程图

## 概述

本文档详细描述了Dendrite项目中用户发送消息到AI服务处理再到回复的完整流程。该流程基于事件驱动架构，通过NATS JetStream实现异步消息处理。

## 完整流程图

```mermaid
sequenceDiagram
    participant User as 用户客户端
    participant ClientAPI as ClientAPI
    participant RoomServer as RoomServer
    participant JetStream as NATS JetStream
    participant AIConsumer as AI消费者
    participant AIService as AI服务
    participant ExternalAI as 外部AI API
    
    Note over User,ExternalAI: 1. 用户发送消息阶段
    User->>ClientAPI: 发送消息 (PUT /rooms/{roomID}/send/m.room.message)
    ClientAPI->>ClientAPI: 验证用户权限和房间状态
    ClientAPI->>ClientAPI: 生成事件 (generateSendEvent)
    ClientAPI->>RoomServer: 提交事件 (SendEvents)
    RoomServer->>RoomServer: 处理和存储事件
    
    Note over User,ExternalAI: 2. 事件分发阶段
    RoomServer->>JetStream: 发布房间事件 (ProduceRoomEvents)
    JetStream-->>AIConsumer: 推送事件消息
    
    Note over User,ExternalAI: 3. AI处理阶段
    AIConsumer->>AIConsumer: 解析房间事件 (onMessage)
    AIConsumer->>AIConsumer: 检查事件类型 (m.room.message)
    AIConsumer->>AIConsumer: 过滤AI用户自己的消息
    AIConsumer->>AIService: 处理消息 (ProcessMessage)
    
    AIService->>AIService: 检查触发条件 (shouldReplyToMessage)
    alt 包含@AI用户或触发词
        AIService->>AIService: 清理消息内容
        AIService->>ExternalAI: 调用AI API (callExternalAI)
        ExternalAI-->>AIService: 返回AI回复
    else 无外部API配置
        AIService->>AIService: 生成本地回复 (getLocalResponse)
    end
    
    AIService-->>AIConsumer: 返回处理结果 (ShouldReply=true)
    
    Note over User,ExternalAI: 4. AI回复阶段
    AIConsumer->>AIConsumer: 确保AI用户已加入房间 (ensureAIUserInRoom)
    AIConsumer->>ClientAPI: 发送AI回复 (SendEvent)
    ClientAPI->>ClientAPI: 生成AI回复事件
    ClientAPI->>RoomServer: 提交AI回复事件
    RoomServer->>RoomServer: 处理和存储AI回复
    RoomServer->>JetStream: 发布AI回复事件
    JetStream-->>User: 用户收到AI回复
```

## 详细流程说明

### 1. 用户发送消息阶段

**入口点**: 用户通过Matrix客户端发送消息

**API路径**: `PUT /rooms/{roomID}/send/m.room.message/{txnID}`

**处理函数**: [`SendEvent()`](clientapi/routing/sendevent.go:71)

**关键步骤**:
1. **权限验证**: 验证用户是否有权限在该房间发送消息
2. **房间状态检查**: 查询房间版本和当前状态
3. **事件生成**: 调用 [`generateSendEvent()`](clientapi/routing/sendevent.go:357) 生成Matrix事件
4. **事件提交**: 通过 [`api.SendEvents()`](clientapi/routing/sendevent.go:243) 提交到房间服务器

**涉及的主要文件**:
- `clientapi/routing/sendevent.go` - 处理发送事件的主要逻辑
- `clientapi/routing/routing.go` - 路由配置

### 2. 事件分发阶段

**房间服务器处理**: 
- 接收并验证用户消息事件
- 将事件存储到数据库
- 更新房间状态

**事件发布**: 
- 通过 [`RoomEventProducer.ProduceRoomEvents()`](roomserver/producers/roomevent.go:34) 发布到NATS JetStream
- 设置事件头信息（房间ID、事件类型等）

**消息队列**: 
- 使用NATS JetStream进行异步事件分发
- 确保事件的可靠传递和处理

**涉及的主要文件**:
- `roomserver/producers/roomevent.go` - 房间事件生产者
- `roomserver/internal/input/input_events.go` - 事件输入处理
- `setup/jetstream/streams.go` - JetStream配置

### 3. AI处理阶段

**事件消费**: 
- [`OutputRoomEventConsumer.onMessage()`](aiservice/consumers/roomserver.go:85) 监听房间事件
- 从NATS JetStream接收事件消息

**消息过滤**:
- 只处理 `m.room.message` 类型事件
- 跳过 `m.room.encrypted` 加密消息（当前不支持）
- 过滤掉AI用户自己发送的消息（避免无限循环）
- 只处理文本消息 (`m.text`)

**触发条件检查**:
- 检查消息是否包含@AI用户
- 检查是否包含配置的触发词
- 通过 [`shouldReplyToMessage()`](aiservice/internal/ai.go:331) 判断是否需要回复

**AI处理**: 
- [`AIService.ProcessMessage()`](aiservice/internal/ai.go:113) 处理消息
- 清理消息内容（移除@符号和触发词）

**回复生成**:
- **优先方案**: 调用外部AI API ([`callExternalAI()`](aiservice/internal/ai.go:156))
  - 构建ChatRequest请求
  - 发送HTTP请求到配置的AI API端点
  - 解析AI API响应
- **备用方案**: 生成本地回复 ([`getLocalResponse()`](aiservice/internal/ai.go:251))
  - 基于关键词匹配生成预定义回复
  - 支持时间、问候、帮助等常见场景

**涉及的主要文件**:
- `aiservice/consumers/roomserver.go` - AI事件消费者
- `aiservice/internal/ai.go` - AI服务核心逻辑
- `aiservice/api/api.go` - AI服务API定义

### 4. AI回复阶段

**房间成员检查**: 
- [`ensureAIUserInRoom()`](aiservice/consumers/roomserver.go:260) 确保AI用户已加入房间
- 如果AI用户不在房间中，自动执行加入操作

**回复发送**: 
- 构造消息内容（msgtype: "m.text"）
- 创建模拟的设备信息和HTTP请求
- 调用 [`SendEvent()`](aiservice/consumers/roomserver.go:228) 发送AI回复
- 使用与用户消息相同的发送流程

**事件循环**: 
- AI回复作为新的房间事件进入系统
- 再次触发事件分发流程
- 但会被AI消费者过滤掉（避免自回复）

**涉及的主要文件**:
- `aiservice/consumers/roomserver.go` - 处理AI回复发送
- `clientapi/routing/sendevent.go` - 复用发送事件逻辑

## 关键技术特点

### 1. 异步处理架构
- **消息队列**: 使用NATS JetStream实现事件的异步分发和处理
- **解耦设计**: 各组件通过事件进行通信，降低耦合度
- **可扩展性**: 支持水平扩展和负载均衡

### 2. 防循环机制
- **发送者检查**: AI服务会检查发送者ID，避免回复自己的消息
- **事件过滤**: 在消费者层面过滤掉不需要处理的事件
- **状态管理**: 维护AI用户的状态信息

### 3. 智能触发机制
- **@用户触发**: 支持通过@AI用户名触发回复
- **关键词触发**: 支持配置自定义触发词
- **灵活配置**: 可通过配置文件调整触发条件

### 4. 容错处理
- **API降级**: 外部AI API失败时自动降级到本地回复
- **错误重试**: 支持消息处理失败时的重试机制
- **日志记录**: 详细的错误日志和调试信息

### 5. 事务支持
- **幂等性**: 支持事务ID，避免重复发送消息
- **缓存机制**: 使用事务缓存提高性能
- **一致性**: 确保消息处理的一致性

## 配置说明

### AI服务配置
```yaml
ai_service:
  enabled: true
  ai_user_id: "@ai:example.com"
  api_endpoint: "https://api.example.com/v1/chat/completions"
  api_key: "your-api-key"
  triggers:
    - "ai"
    - "助手"
  reply_delay: 1000  # 毫秒
  max_length: 1000
```

### JetStream配置
```yaml
jetstream:
  addresses:
    - "nats://localhost:4222"
  topic_prefix: "Dendrite"
```

## 监控和调试

### 日志级别
- **Info**: 记录关键流程节点
- **Debug**: 记录详细的处理过程
- **Error**: 记录错误和异常情况

### 关键日志点
1. 用户消息接收: `"Sent event to roomserver"`
2. AI事件消费: `"内容 %+v"`
3. AI处理决策: `"跳过非消息事件"` / `"AI处理消息失败"`
4. AI回复发送: `"AI回复发送成功"` / `"AI回复发送失败"`

### 性能监控
- **Prometheus指标**: 发送事件持续时间统计
- **事件计数**: 处理的消息数量统计
- **错误率**: API调用成功率监控

## 故障排查

### 常见问题

1. **AI不回复消息**
   - 检查AI服务是否启用
   - 确认触发条件是否满足
   - 查看AI用户是否在房间中

2. **外部AI API调用失败**
   - 检查API端点配置
   - 验证API密钥
   - 查看网络连接状态

3. **消息重复发送**
   - 检查事务ID配置
   - 确认缓存机制工作正常
   - 查看防循环逻辑

4. **加密消息无法处理**
   - 当前版本不支持加密消息
   - 建议在未加密房间中测试
   - 查看加密相关日志

### 调试步骤

1. **启用详细日志**: 设置日志级别为Debug
2. **检查配置文件**: 确认AI服务配置正确
3. **监控JetStream**: 查看消息队列状态
4. **测试API连接**: 手动测试外部AI API
5. **查看房间状态**: 确认AI用户权限

## 扩展和定制

### 添加新的触发条件
1. 修改 `shouldReplyToMessage()` 函数
2. 添加新的配置参数
3. 更新配置文件结构

### 集成其他AI服务
1. 修改 `callExternalAI()` 函数
2. 适配不同的API格式
3. 添加服务特定的配置

### 增强本地回复
1. 扩展 `getLocalResponse()` 函数
2. 添加更多预定义回复
3. 实现基于规则的回复生成

这个架构实现了一个完整的AI聊天机器人系统，能够实时响应用户消息并提供智能回复。整个流程采用事件驱动架构，通过消息队列实现各组件间的解耦，确保系统的可扩展性和可靠性。