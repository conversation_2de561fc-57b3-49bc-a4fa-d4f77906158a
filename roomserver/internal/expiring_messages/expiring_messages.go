package expiring_messages

import (
	"context"
	"crypto/ed25519"
	"fmt"
	"os"
	"time"

	"github.com/element-hq/dendrite/internal/eventutil"
	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/roomserver/storage/shared"
	"github.com/element-hq/dendrite/roomserver/types"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/gomatrixserverlib/fclient"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// ExpiringMessagesService 处理阅后即焚消息的服务
type ExpiringMessagesService struct {
	db         storage.Database
	rsAPI      api.RoomserverInternalAPI
	stopChan   chan struct{}
	serverName spec.ServerName
	keyID      gomatrixserverlib.KeyID
	privateKey ed25519.PrivateKey

	// 配置参数
	checkInterval time.Duration
	batchSize     int
	cleanupDays   int
	maxExpireDays int
	enabled       bool

	// Redis 分布式锁
	lockManager *RedisLockManager
	instanceID  string
}

// ExpiringMessagesConfig 过期消息配置
type ExpiringMessagesConfig struct {
	CheckInterval time.Duration
	BatchSize     int
	CleanupDays   int
	MaxExpireDays int
	Enabled       bool
}

// NewExpiringMessagesService 创建新的过期消息服务
func NewExpiringMessagesService(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	config ExpiringMessagesConfig,
	serverName spec.ServerName,
	keyID gomatrixserverlib.KeyID,
	privateKey ed25519.PrivateKey,
) *ExpiringMessagesService {
	instanceID := fmt.Sprintf("%s-%d", serverName, os.Getpid())

	return &ExpiringMessagesService{
		db:            db,
		rsAPI:         rsAPI,
		stopChan:      make(chan struct{}),
		serverName:    serverName,
		keyID:         keyID,
		privateKey:    privateKey,
		checkInterval: config.CheckInterval,
		batchSize:     config.BatchSize,
		cleanupDays:   config.CleanupDays,
		maxExpireDays: config.MaxExpireDays,
		enabled:       config.Enabled,
		instanceID:    instanceID,
		lockManager:   nil, // 将在 SetRedisLockManager 中设置
	}
}

// SetRedisLockManager 设置 Redis 锁管理器
func (s *ExpiringMessagesService) SetRedisLockManager(lockManager *RedisLockManager) {
	s.lockManager = lockManager
	logrus.WithField("instance_id", s.instanceID).Info("Redis 分布式锁已启用")
}

// NewExpiringMessagesServiceWithDefaults 使用默认配置创建过期消息服务
// 注意：这个函数不包含签名信息，createRedactionEvent 会失败
// 建议使用 NewExpiringMessagesService 并传入完整的签名信息
func NewExpiringMessagesServiceWithDefaults(db storage.Database, rsAPI api.RoomserverInternalAPI) *ExpiringMessagesService {
	defaultConfig := ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}
	// 使用空的签名信息，实际使用时需要提供真实的签名信息
	return NewExpiringMessagesService(
		db, rsAPI, defaultConfig,
		"", "", nil,
	)
}

// Start 启动过期消息处理服务
func (s *ExpiringMessagesService) Start() {
	if !s.enabled {
		logrus.Info("阅后即焚消息功能已禁用，跳过启动")
		return
	}

	logrus.WithFields(logrus.Fields{
		"check_interval":  s.checkInterval,
		"batch_size":      s.batchSize,
		"cleanup_days":    s.cleanupDays,
		"max_expire_days": s.maxExpireDays,
	}).Info("启动阅后即焚消息处理服务")

	// 使用配置的检查间隔
	ticker := time.NewTicker(s.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.processExpiredMessages()
		case <-s.stopChan:
			logrus.Info("停止阅后即焚消息处理服务")
			return
		}
	}
}

// Stop 停止过期消息处理服务
func (s *ExpiringMessagesService) Stop() {
	close(s.stopChan)
}

// processExpiredMessages 处理已过期的消息（支持多机部署）
func (s *ExpiringMessagesService) processExpiredMessages() {
	ctx := context.Background()
	currentTS := time.Now().Unix() // 改为秒级时间戳

	// 🔒 使用分布式锁防止多机重复处理
	lockKey := "expiring_messages_processing"
	lock, acquired := s.acquireDistributedLock(ctx, lockKey)
	if !acquired {
		logrus.Debug("其他实例正在处理过期消息，跳过本次处理")
		return
	}
	defer s.releaseDistributedLock(ctx, lock)

	// 查询已过期的消息，使用配置的批量大小
	expiredMessages, err := s.db.SelectExpiredMessages(ctx, currentTS, s.batchSize)
	if err != nil {
		logrus.WithError(err).Error("查询过期消息失败")
		return
	}

	if len(expiredMessages) == 0 {
		return
	}

	logrus.WithFields(logrus.Fields{
		"count":     len(expiredMessages),
		"server_id": s.getServerInstanceID(),
	}).Info("发现过期消息，开始处理")

	// 🔄 逐个处理消息，每个消息都加锁
	for _, msg := range expiredMessages {
		if s.processExpiredMessageWithLock(ctx, msg) {
			// 处理成功，继续下一个
		} else {
			// 处理失败或被其他实例处理，跳过
			logrus.WithField("event_id", msg.EventID).Debug("消息已被其他实例处理或处理失败")
		}
	}

	// 清理已处理的旧记录
	s.cleanupOldRecords(ctx)
}

// processExpiredMessage 处理单个过期消息
func (s *ExpiringMessagesService) processExpiredMessage(ctx context.Context, msg shared.ExpiringMessage) error {
	logrus.WithFields(logrus.Fields{
		"event_id":  msg.EventID,
		"room_id":   msg.RoomID,
		"sender_id": msg.SenderID,
		"expire_ts": msg.ExpireTS,
	}).Info("处理过期消息")

	// 创建撤回事件
	redactionEvent, err := s.createRedactionEvent(ctx, msg)
	if err != nil {
		return err
	}

	// 发送撤回事件到房间
	if err := s.sendRedactionEvent(ctx, msg.RoomID, redactionEvent); err != nil {
		return err
	}

	// 标记消息为已处理
	if err := s.db.MarkMessageProcessed(ctx, msg.EventID); err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("标记消息为已处理失败")
		return err
	}

	logrus.WithField("event_id", msg.EventID).Info("过期消息处理完成")
	return nil
}

// createRedactionEvent 创建撤回事件
func (s *ExpiringMessagesService) createRedactionEvent(ctx context.Context, msg shared.ExpiringMessage) (*types.HeaderedEvent, error) {
	// 检查必要的参数
	if s.rsAPI == nil {
		return nil, fmt.Errorf("roomserver API 未初始化")
	}

	// 检查签名信息是否可用
	if s.serverName == "" || s.keyID == "" || s.privateKey == nil {
		logrus.WithFields(logrus.Fields{
			"server_name":     s.serverName,
			"key_id":          s.keyID,
			"has_private_key": s.privateKey != nil,
		}).Error("签名信息不完整，无法创建撤回事件")
		return nil, fmt.Errorf("签名信息不完整: serverName=%s, keyID=%s, privateKey=%v",
			s.serverName, s.keyID, s.privateKey != nil)
	}

	// 创建撤回事件内容
	content := map[string]interface{}{
		"reason": "消息已过期（阅后即焚）",
	}

	// 创建 ProtoEvent
	proto := &gomatrixserverlib.ProtoEvent{
		SenderID: msg.SenderID,
		RoomID:   msg.RoomID,
		Type:     "m.room.redaction",
		Redacts:  msg.EventID,
	}

	// 设置内容到 proto
	if err := proto.SetContent(content); err != nil {
		return nil, fmt.Errorf("设置事件内容失败: %w", err)
	}

	// 使用配置的签名信息构建事件
	identity := &fclient.SigningIdentity{
		ServerName: s.serverName,
		KeyID:      s.keyID,
		PrivateKey: s.privateKey,
	}

	logrus.WithFields(logrus.Fields{
		"event_id":    msg.EventID,
		"room_id":     msg.RoomID,
		"sender_id":   msg.SenderID,
		"server_name": s.serverName,
		"key_id":      s.keyID,
	}).Debug("开始构建撤回事件")

	event, err := eventutil.QueryAndBuildEvent(ctx, proto, identity, time.Now(), s.rsAPI, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":  msg.EventID,
			"room_id":   msg.RoomID,
			"sender_id": msg.SenderID,
		}).Error("构建撤回事件失败")
		return nil, fmt.Errorf("构建撤回事件失败: %w", err)
	}

	logrus.WithField("event_id", msg.EventID).Debug("撤回事件构建成功")
	return event, nil
}

// sendRedactionEvent 发送撤回事件
func (s *ExpiringMessagesService) sendRedactionEvent(ctx context.Context, roomID string, event *types.HeaderedEvent) error {
	// 使用 roomserver API 发送撤回事件
	request := api.InputRoomEventsRequest{
		InputRoomEvents: []api.InputRoomEvent{
			{
				Kind:  api.KindNew,
				Event: event,
			},
		},
		Asynchronous: false,
	}

	var response api.InputRoomEventsResponse
	s.rsAPI.InputRoomEvents(ctx, &request, &response)

	if response.ErrMsg != "" {
		return fmt.Errorf("发送撤回事件失败: %s", response.ErrMsg)
	}

	return nil
}

// cleanupOldRecords 清理旧的已处理记录
func (s *ExpiringMessagesService) cleanupOldRecords(ctx context.Context) {
	// 使用配置的天数删除已处理记录
	beforeTime := time.Now().AddDate(0, 0, -s.cleanupDays)

	if err := s.db.DeleteProcessedMessages(ctx, beforeTime); err != nil {
		logrus.WithError(err).WithField("cleanup_days", s.cleanupDays).Error("清理旧记录失败")
	} else {
		logrus.WithField("cleanup_days", s.cleanupDays).Debug("成功清理旧记录")
	}
}

// AddExpiringMessage 添加需要过期的消息
func (s *ExpiringMessagesService) AddExpiringMessage(ctx context.Context, eventID, roomID, senderID string, expireTS int64) error {
	return s.db.InsertExpiringMessage(ctx, eventID, roomID, senderID, expireTS)
}

// 🔒 Redis 分布式锁相关方法

// acquireDistributedLock 获取分布式锁
func (s *ExpiringMessagesService) acquireDistributedLock(ctx context.Context, lockKey string) (*RedisDistributedLock, bool) {
	if s.lockManager == nil {
		// 如果没有 Redis 锁管理器，回退到单机模式
		logrus.Debug("Redis 锁管理器未配置，使用单机模式")
		return nil, true
	}

	// 使用 Redis 获取分布式锁
	lock, err := s.lockManager.AcquireLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		logrus.WithError(err).WithField("lock_key", lockKey).Error("获取 Redis 分布式锁失败")
		return nil, false
	}

	if lock.IsAcquired() {
		logrus.WithFields(logrus.Fields{
			"lock_key":    lockKey,
			"instance_id": s.instanceID,
		}).Debug("成功获取分布式锁")
		return lock, true
	}

	logrus.WithFields(logrus.Fields{
		"lock_key":    lockKey,
		"instance_id": s.instanceID,
	}).Debug("锁已被其他实例持有")
	return nil, false
}

// releaseDistributedLock 释放分布式锁
func (s *ExpiringMessagesService) releaseDistributedLock(ctx context.Context, lock *RedisDistributedLock) {
	if lock == nil {
		return
	}

	if err := lock.Release(ctx); err != nil {
		logrus.WithError(err).WithField("lock_key", lock.lockKey).Error("释放分布式锁失败")
	}
}

// getServerInstanceID 获取服务器实例ID
func (s *ExpiringMessagesService) getServerInstanceID() string {
	return s.instanceID
}

// processExpiredMessageWithLock 带锁处理单个过期消息
func (s *ExpiringMessagesService) processExpiredMessageWithLock(ctx context.Context, msg shared.ExpiringMessage) bool {
	// 为每个消息创建独立的锁
	lockKey := fmt.Sprintf("expiring_message_%s", msg.EventID)

	lock, acquired := s.acquireDistributedLock(ctx, lockKey)
	if !acquired {
		logrus.WithField("event_id", msg.EventID).Debug("消息正在被其他实例处理")
		return false
	}
	defer s.releaseDistributedLock(ctx, lock)

	// 再次检查消息是否已被处理（双重检查）
	existingMsg, err := s.db.SelectExpiringMessage(ctx, msg.EventID)
	if err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("检查消息状态失败")
		return false
	}

	if existingMsg == nil || existingMsg.Processed {
		logrus.WithField("event_id", msg.EventID).Debug("消息已被其他实例处理")
		return false
	}

	// 处理消息
	err = s.processExpiredMessage(ctx, msg)
	if err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("处理过期消息失败")
		return false
	}

	return true
}
