package expiring_messages

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

// RedisDistributedLock Redis 分布式锁实现
type RedisDistributedLock struct {
	client    *redis.Client
	lockKey   string
	lockValue string
	expiry    time.Duration
	acquired  bool
}

// RedisLockManager Redis 锁管理器
type RedisLockManager struct {
	client *redis.Client
}

// NewRedisLockManager 创建 Redis 锁管理器
func NewRedisLockManager(redisAddr, password string, db int) (*RedisLockManager, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis 连接失败: %w", err)
	}

	logrus.WithField("redis_addr", redisAddr).Info("Redis 分布式锁管理器初始化成功")
	return &RedisLockManager{client: client}, nil
}

// AcquireLock 获取分布式锁
func (m *RedisLockManager) AcquireLock(ctx context.Context, lockKey string, expiry time.Duration) (*RedisDistributedLock, error) {
	// 生成唯一的锁值
	lockValue, err := generateLockValue()
	if err != nil {
		return nil, fmt.Errorf("生成锁值失败: %w", err)
	}

	lock := &RedisDistributedLock{
		client:    m.client,
		lockKey:   lockKey,
		lockValue: lockValue,
		expiry:    expiry,
		acquired:  false,
	}

	// 使用 SET NX EX 原子操作获取锁
	result := m.client.SetNX(ctx, lockKey, lockValue, expiry)
	if err := result.Err(); err != nil {
		return nil, fmt.Errorf("获取锁失败: %w", err)
	}

	lock.acquired = result.Val()

	if lock.acquired {
		logrus.WithFields(logrus.Fields{
			"lock_key":   lockKey,
			"lock_value": lockValue,
			"expiry":     expiry,
		}).Debug("成功获取 Redis 分布式锁")
	} else {
		logrus.WithField("lock_key", lockKey).Debug("锁已被其他实例持有")
	}

	return lock, nil
}

// Release 释放锁
func (lock *RedisDistributedLock) Release(ctx context.Context) error {
	if !lock.acquired {
		return nil
	}

	// 使用 Lua 脚本确保只有锁的持有者才能释放锁
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	result := lock.client.Eval(ctx, luaScript, []string{lock.lockKey}, lock.lockValue)
	if err := result.Err(); err != nil {
		return fmt.Errorf("释放锁失败: %w", err)
	}

	released := result.Val().(int64) == 1
	if released {
		logrus.WithFields(logrus.Fields{
			"lock_key":   lock.lockKey,
			"lock_value": lock.lockValue,
		}).Debug("成功释放 Redis 分布式锁")
		lock.acquired = false
	} else {
		logrus.WithField("lock_key", lock.lockKey).Warn("锁已过期或被其他实例释放")
	}

	return nil
}

// IsAcquired 检查锁是否已获取
func (lock *RedisDistributedLock) IsAcquired() bool {
	return lock.acquired
}

// Extend 延长锁的过期时间
func (lock *RedisDistributedLock) Extend(ctx context.Context, expiry time.Duration) error {
	if !lock.acquired {
		return fmt.Errorf("锁未获取，无法延长")
	}

	// 使用 Lua 脚本原子性地延长锁的过期时间
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("EXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	result := lock.client.Eval(ctx, luaScript, []string{lock.lockKey}, lock.lockValue, int(expiry.Seconds()))
	if err := result.Err(); err != nil {
		return fmt.Errorf("延长锁失败: %w", err)
	}

	extended := result.Val().(int64) == 1
	if extended {
		lock.expiry = expiry
		logrus.WithFields(logrus.Fields{
			"lock_key": lock.lockKey,
			"expiry":   expiry,
		}).Debug("成功延长锁过期时间")
	} else {
		logrus.WithField("lock_key", lock.lockKey).Warn("锁已过期，无法延长")
		lock.acquired = false
	}

	return nil
}

// TryAcquireWithRetry 带重试的锁获取
func (m *RedisLockManager) TryAcquireWithRetry(
	ctx context.Context,
	lockKey string,
	expiry time.Duration,
	maxRetries int,
	retryInterval time.Duration,
) (*RedisDistributedLock, error) {
	for i := 0; i < maxRetries; i++ {
		lock, err := m.AcquireLock(ctx, lockKey, expiry)
		if err != nil {
			return nil, err
		}

		if lock.IsAcquired() {
			return lock, nil
		}

		// 等待后重试
		if i < maxRetries-1 {
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryInterval):
				continue
			}
		}
	}

	return nil, fmt.Errorf("在 %d 次重试后仍无法获取锁", maxRetries)
}

// CleanupExpiredLocks 清理过期的锁（可选，Redis 会自动过期）
func (m *RedisLockManager) CleanupExpiredLocks(ctx context.Context, pattern string) (int64, error) {
	// 扫描匹配的键
	keys := make([]string, 0)
	iter := m.client.Scan(ctx, 0, pattern, 0).Iterator()

	for iter.Next(ctx) {
		keys = append(keys, iter.Val())
	}

	if err := iter.Err(); err != nil {
		return 0, fmt.Errorf("扫描键失败: %w", err)
	}

	if len(keys) == 0 {
		return 0, nil
	}

	// 删除找到的键
	result := m.client.Del(ctx, keys...)
	if err := result.Err(); err != nil {
		return 0, fmt.Errorf("删除过期锁失败: %w", err)
	}

	deleted := result.Val()
	logrus.WithFields(logrus.Fields{
		"pattern": pattern,
		"deleted": deleted,
	}).Debug("清理过期锁")

	return deleted, nil
}

// Close 关闭 Redis 连接
func (m *RedisLockManager) Close() error {
	return m.client.Close()
}

// generateLockValue 生成唯一的锁值
func generateLockValue() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// GetLockInfo 获取锁信息（调试用）
func (m *RedisLockManager) GetLockInfo(ctx context.Context, lockKey string) (string, time.Duration, error) {
	// 获取锁值
	value := m.client.Get(ctx, lockKey)
	if err := value.Err(); err != nil {
		if err == redis.Nil {
			return "", 0, fmt.Errorf("锁不存在")
		}
		return "", 0, err
	}

	// 获取 TTL
	ttl := m.client.TTL(ctx, lockKey)
	if err := ttl.Err(); err != nil {
		return "", 0, err
	}

	return value.Val(), ttl.Val(), nil
}
