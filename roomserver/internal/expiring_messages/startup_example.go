package expiring_messages

import (
	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/sirupsen/logrus"
)

// StartupExample 展示如何在 Dendrite 启动时正确集成阅后即焚功能
func StartupExample() {
	logrus.Info("=== 阅后即焚消息服务启动示例 ===")

	// 这个示例展示了在 Dendrite 主程序中如何集成阅后即焚功能
	// 实际使用时，这些代码应该放在 Dendrite 的启动流程中
}

// IntegrateIntoRoomserver 展示如何在 Roomserver 启动时集成
// 这个函数应该在 roomserver 的 NewInternalAPI 创建之后调用
func IntegrateIntoRoomserver(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	cfg *config.Dendrite,
) error {
	logrus.Info("开始集成阅后即焚消息服务到 Roomserver")

	// 方法1: 推荐方式 - 从完整配置初始化（但不启动）
	if cfg != nil {
		err := InitializeServiceFromConfig(db, rsAPI, cfg)
		if err != nil {
			logrus.WithError(err).Error("从配置初始化阅后即焚服务失败")
			return err
		}
		logrus.Info("✅ 阅后即焚消息服务已成功集成（从配置）")
		logrus.Info("⏰ 请在 roomserver 完全启动后调用 StartService() 启动服务")
		return nil
	}

	// 方法2: 备用方式 - 使用默认配置（但无法发送撤回事件）
	logrus.Warn("⚠️  配置为空，使用默认配置初始化（撤回功能将不可用）")
	InitializeService(db, rsAPI)
	logrus.Info("✅ 阅后即焚消息服务已集成（默认配置）")
	logrus.Info("⏰ 请在 roomserver 完全启动后调用 StartService() 启动服务")

	return nil
}

// StartServiceAfterRoomserverReady 在 roomserver 完全准备好后启动服务
// 这个函数应该在 roomserver 完全初始化后调用
func StartServiceAfterRoomserverReady() {
	logrus.Info("Roomserver 已准备就绪，现在启动阅后即焚消息服务")
	StartService()
}

// IntegrateIntoMain 展示如何在 main 函数中集成
func IntegrateIntoMain() {
	logrus.Info(`
=== 在 main 函数中集成阅后即焚功能 ===

正确的集成顺序：

// 1. 创建 roomserver（在 roomserver 初始化阶段）
roomserverDB, err := storage.Open(&cfg.RoomServer.Database, caches)
if err != nil {
    logrus.WithError(err).Panic("failed to connect to room server db")
}

roomserverAPI := roomserver.NewInternalAPI(cfg, roomserverDB, &base.Caches{}, fsAPI, keyRing)

// 2. 初始化阅后即焚服务（但不启动）
err = expiring_messages.IntegrateIntoRoomserver(roomserverDB, roomserverAPI, cfg)
if err != nil {
    logrus.WithError(err).Error("集成阅后即焚功能失败")
}

// 3. 继续其他服务初始化...
// clientAPI := clientapi.NewInternalAPI(...)
// federationAPI := federationapi.NewInternalAPI(...)
// syncAPI := syncapi.NewInternalAPI(...)

// 4. 所有服务初始化完成后，启动阅后即焚服务
defer func() {
    logrus.Info("所有服务初始化完成，启动阅后即焚消息服务")
    expiring_messages.StartServiceAfterRoomserverReady()
}()

// 或者在服务器启动的最后阶段调用：
// expiring_messages.StartServiceAfterRoomserverReady()
`)
}

// CheckServiceStatus 检查服务状态
func CheckServiceStatus() {
	service := GetService()
	if service == nil {
		logrus.Error("❌ 阅后即焚消息服务未初始化")
		return
	}

	if service.enabled {
		logrus.Info("✅ 阅后即焚消息服务已启用")
		logrus.WithFields(logrus.Fields{
			"check_interval":  service.checkInterval,
			"batch_size":      service.batchSize,
			"cleanup_days":    service.cleanupDays,
			"max_expire_days": service.maxExpireDays,
			"server_name":     service.serverName,
			"key_id":          service.keyID,
			"has_private_key": service.privateKey != nil,
		}).Info("服务配置详情")
	} else {
		logrus.Warn("⚠️  阅后即焚消息服务已禁用")
	}
}

// TestServiceIntegration 测试服务集成
func TestServiceIntegration() {
	logrus.Info("=== 测试阅后即焚服务集成 ===")

	// 检查服务状态
	CheckServiceStatus()

	// 测试添加过期消息
	service := GetService()
	if service != nil {
		// 这里可以添加测试代码
		logrus.Info("✅ 服务可用，可以进行功能测试")
	} else {
		logrus.Error("❌ 服务不可用，请检查初始化")
	}
}

// ShowIntegrationSteps 显示集成步骤
func ShowIntegrationSteps() {
	steps := `
=== 阅后即焚功能集成步骤 ===

1. 配置文件设置 (dendrite.yaml):
   global:
     server_name: "your-server.com"
     private_key: "matrix_key.pem"
     key_id: "ed25519:1"
   
   room_server:
     expiring_messages:
       enabled: true
       check_interval: "30s"
       batch_size: 100
       cleanup_days: 7
       max_expire_days: 30

2. 数据库迁移:
   - 数据库表会在启动时自动创建
   - 确保数据库连接正常

3. 代码集成:
   在 roomserver 初始化后添加：
   
   err = expiring_messages.IntegrateIntoRoomserver(db, rsAPI, cfg)
   if err != nil {
       logrus.WithError(err).Error("集成阅后即焚功能失败")
   }

4. 验证集成:
   - 查看启动日志确认服务已启动
   - 发送带有 expire_ts 的测试消息
   - 检查数据库中的过期消息记录

5. 监控和调试:
   - 监控日志中的过期消息处理信息
   - 检查数据库中已处理的消息记录
   - 根据需要调整配置参数

=== 常见问题排查 ===

Q: 消息没有自动撤回？
A: 检查签名配置是否正确，查看错误日志

Q: 服务启动失败？
A: 检查配置文件格式，确认数据库连接

Q: 性能问题？
A: 调整 check_interval 和 batch_size 参数
`

	logrus.Info(steps)
}
