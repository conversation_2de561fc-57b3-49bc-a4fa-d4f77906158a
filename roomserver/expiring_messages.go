package roomserver

import (
	"fmt"

	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/internal"
	"github.com/element-hq/dendrite/roomserver/internal/expiring_messages"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/sirupsen/logrus"
)

// InitializeExpiringMessages 初始化阅后即焚消息服务
// 这是一个公开的接口，供 main.go 调用
func InitializeExpiringMessages(rsAPI api.RoomserverInternalAPI, cfg *config.Dendrite) error {
	logrus.Info("正在初始化阅后即焚消息服务...")

	// 类型断言获取内部 API 实例
	internalAPI, ok := rsAPI.(*internal.RoomserverInternalAPI)
	if !ok {
		logrus.Error("无法获取 roomserver 内部 API 实例")
		return fmt.Errorf("无法获取 roomserver 内部 API 实例")
	}

	// 从 roomserver 内部 API 获取数据库连接
	db := internalAPI.DB

	// 初始化服务（但不启动）
	err := expiring_messages.InitializeServiceFromConfig(db, rsAPI, cfg)
	if err != nil {
		logrus.WithError(err).Error("初始化阅后即焚消息服务失败")
		return err
	}

	logrus.Info("✅ 阅后即焚消息服务初始化成功")
	return nil
}

// StartExpiringMessages 启动阅后即焚消息服务
// 这个函数应该在所有服务初始化完成后调用
func StartExpiringMessages() {
	logrus.Info("正在启动阅后即焚消息服务...")
	expiring_messages.StartServiceImmediately()
}

// StartExpiringMessagesWithDelay 延迟启动阅后即焚消息服务
func StartExpiringMessagesWithDelay() {
	logrus.Info("正在启动阅后即焚消息服务...")
	expiring_messages.StartService()
}

// StopExpiringMessages 停止阅后即焚消息服务
func StopExpiringMessages() {
	logrus.Info("正在停止阅后即焚消息服务...")
	expiring_messages.StopService()
}

// CheckExpiringMessagesStatus 检查阅后即焚消息服务状态
func CheckExpiringMessagesStatus() {
	expiring_messages.CheckServiceStatus()
}
