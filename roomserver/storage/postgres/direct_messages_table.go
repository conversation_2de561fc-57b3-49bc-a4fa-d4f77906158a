// Copyright 2024 The Matrix.org Foundation C.I.C.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package postgres

import (
	"context"
	"database/sql"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/roomserver/storage/tables"
)

const directMessagesSchema = `
-- Stores direct message room relationships between users
CREATE TABLE IF NOT EXISTS roomserver_direct_messages (
    -- User ID of the first user (lexicographically smaller)
    user_id_1 TEXT NOT NULL,
    -- User ID of the second user (lexicographically larger)  
    user_id_2 TEXT NOT NULL,
    -- Room ID for the direct message between these users
    room_id TEXT NOT NULL,
    -- When this relationship was created
    created_ts BIGINT NOT NULL,
    -- Primary key ensures only one DM room per user pair
    PRIMARY KEY (user_id_1, user_id_2)
);

CREATE INDEX IF NOT EXISTS roomserver_direct_messages_room_id_idx ON roomserver_direct_messages(room_id);
`

const insertDirectMessageSQL = `
    INSERT INTO roomserver_direct_messages (user_id_1, user_id_2, room_id, created_ts) 
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (user_id_1, user_id_2) DO UPDATE SET 
        room_id = EXCLUDED.room_id,
        created_ts = EXCLUDED.created_ts
    RETURNING room_id
`

const selectDirectMessageRoomSQL = `
    SELECT room_id FROM roomserver_direct_messages 
    WHERE user_id_1 = $1 AND user_id_2 = $2
`

const deleteDirectMessageByRoomSQL = `
    DELETE FROM roomserver_direct_messages WHERE room_id = $1
`

const deleteDirectMessageByUsersSQL = `
    DELETE FROM roomserver_direct_messages WHERE user_id_1 = $1 AND user_id_2 = $2
`

type directMessagesStatements struct {
	insertDirectMessageStmt        *sql.Stmt
	selectDirectMessageRoomStmt    *sql.Stmt
	deleteDirectMessageByRoomStmt  *sql.Stmt
	deleteDirectMessageByUsersStmt *sql.Stmt
}

func CreateDirectMessagesTable(db *sql.DB) error {
	_, err := db.Exec(directMessagesSchema)
	return err
}

func PrepareDirectMessagesTable(db *sql.DB) (tables.DirectMessages, error) {
	s := &directMessagesStatements{}

	return s, sqlutil.StatementList{
		{Statement: &s.insertDirectMessageStmt, SQL: insertDirectMessageSQL},
		{Statement: &s.selectDirectMessageRoomStmt, SQL: selectDirectMessageRoomSQL},
		{Statement: &s.deleteDirectMessageByRoomStmt, SQL: deleteDirectMessageByRoomSQL},
		{Statement: &s.deleteDirectMessageByUsersStmt, SQL: deleteDirectMessageByUsersSQL},
	}.Prepare(db)
}

func (s *directMessagesStatements) InsertDirectMessage(
	ctx context.Context, txn *sql.Tx, userID1, userID2, roomID string, createdTS int64,
) (string, error) {
	stmt := sqlutil.TxStmt(txn, s.insertDirectMessageStmt)
	var resultRoomID string
	err := stmt.QueryRowContext(ctx, userID1, userID2, roomID, createdTS).Scan(&resultRoomID)
	return resultRoomID, err
}

func (s *directMessagesStatements) SelectDirectMessageRoom(
	ctx context.Context, txn *sql.Tx, userID1, userID2 string,
) (string, error) {
	stmt := sqlutil.TxStmt(txn, s.selectDirectMessageRoomStmt)
	var roomID string
	err := stmt.QueryRowContext(ctx, userID1, userID2).Scan(&roomID)
	if err == sql.ErrNoRows {
		return "", nil
	}
	return roomID, err
}

func (s *directMessagesStatements) DeleteDirectMessageByRoom(
	ctx context.Context, txn *sql.Tx, roomID string,
) error {
	stmt := sqlutil.TxStmt(txn, s.deleteDirectMessageByRoomStmt)
	_, err := stmt.ExecContext(ctx, roomID)
	return err
}

func (s *directMessagesStatements) DeleteDirectMessageByUsers(
	ctx context.Context, txn *sql.Tx, userID1, userID2 string,
) error {
	stmt := sqlutil.TxStmt(txn, s.deleteDirectMessageByUsersStmt)
	_, err := stmt.ExecContext(ctx, userID1, userID2)
	return err
}
