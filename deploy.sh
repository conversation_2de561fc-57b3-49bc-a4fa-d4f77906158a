#!/bin/bash

# Dendrite Matrix 服务器简化部署脚本
# 用法: ./deploy.sh [命令]
# 提供基本的部署、启动、停止等功能

set -e  # 遇到错误立即退出

# === 颜色定义 ===
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'  # 无颜色

# === 日志函数 ===
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# === 核心功能函数 ===

# 检查必要的依赖工具
check_deps() {
    log_info "检查系统依赖..."

    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "请先安装 Docker"
        exit 1
    fi

    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "请先安装 Docker Compose"
        exit 1
    fi

    # 检查 Go（用于生成密钥）
    if ! command -v go &> /dev/null; then
        log_error "请先安装 Go"
        exit 1
    fi

    log_info "依赖检查通过"
}

# 生成 Matrix 服务器签名密钥
generate_keys() {
    log_info "生成签名密钥..."

    if [ ! -f "matrix_key.pem" ]; then
        go run ./cmd/generate-keys --private-key matrix_key.pem
        log_info "密钥已生成: matrix_key.pem"
    else
        log_warn "密钥文件已存在"
    fi
}

# 初始化项目
init_project() {
    log_info "初始化项目..."
    generate_keys
    mkdir -p logs  # 创建日志目录
    log_info "项目初始化完成"
}

# 构建 Docker 镜像
build_image() {
    log_info "构建 Docker 镜像..."
    docker-compose build
    log_info "镜像构建完成"
}

# 启动服务
start_service() {
    log_info "启动 Dendrite 服务..."
    docker-compose up -d

    # 等待服务启动
    sleep 5

    log_info "服务已启动！"
    log_info "客户端访问: http://localhost:8008"
    log_info "联邦接口: http://localhost:8448"
}

# 停止服务
stop_service() {
    log_info "停止服务..."
    docker-compose down
    log_info "服务已停止"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
}

# 查看日志
show_logs() {
    log_info "显示实时日志..."
    docker-compose logs -f dendrite
}

# 测试服务连接
test_service() {
    log_info "测试服务连接..."

    if curl -s http://localhost:8008/_matrix/client/versions > /dev/null; then
        log_info "✓ 服务运行正常"
    else
        log_error "✗ 服务连接失败"
    fi
}

# 创建用户账户
create_user() {
    log_info "创建用户账户..."

    read -p "用户名: " username
    read -s -p "密码: " password
    echo ""

    docker-compose exec dendrite /usr/bin/create-account \
        -config /etc/dendrite/dendrite.yaml \
        -username "$username" \
        -password "$password"
}

# 一键部署
deploy() {
    log_info "开始部署 Dendrite..."

    check_deps      # 检查依赖
    init_project    # 初始化项目
    build_image     # 构建镜像
    start_service   # 启动服务
    test_service    # 测试服务

    log_info "部署完成！访问 http://localhost:8008 开始使用"
}

# 显示帮助信息
show_help() {
    echo "Dendrite 部署脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  deploy    一键部署（默认）"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  status    查看状态"
    echo "  logs      查看日志"
    echo "  test      测试连接"
    echo "  user      创建用户"
    echo "  build     构建镜像"
    echo "  help      显示帮助"
    echo ""
}

# === 主程序入口 ===
main() {
    case "${1:-deploy}" in
        "deploy")   deploy ;;
        "start")    start_service ;;
        "stop")     stop_service ;;
        "status")   show_status ;;
        "logs")     show_logs ;;
        "test")     test_service ;;
        "user")     create_user ;;
        "build")    build_image ;;
        "help"|"-h"|"--help") show_help ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
