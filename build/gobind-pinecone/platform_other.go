// Copyright 2024 New Vector Ltd.
// Copyright 2022 The Matrix.org Foundation C.I.C.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

//go:build !ios
// +build !ios

package gobind

import "log"

type BindLogger struct{}

func (nsl BindLogger) Write(p []byte) (n int, err error) {
	log.Println(string(p))
	return len(p), nil
}
