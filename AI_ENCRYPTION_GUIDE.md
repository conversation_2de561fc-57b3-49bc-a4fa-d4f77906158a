# AI服务加密消息处理指南

## 🔐 问题说明

当你看到事件类型为 `m.room.encrypted` 时，这表示房间启用了端到端加密（E2EE）。在加密房间中：

- 所有消息都被加密成 `m.room.encrypted` 类型
- 消息内容被加密，AI服务无法直接读取明文内容
- 这是Matrix的安全特性，保护用户隐私

## 🚫 当前限制

AI服务目前**不支持**处理加密消息，因为：

1. **解密复杂性**: 需要实现完整的Matrix E2EE协议
2. **密钥管理**: 需要管理设备密钥、会话密钥等
3. **安全考虑**: AI服务访问加密内容可能带来安全风险

## ✅ 解决方案

### 方案1: 创建未加密房间（推荐）

1. **创建新房间**:
   - 在Matrix客户端中创建新房间
   - 确保**不启用**端到端加密
   - 邀请AI用户到房间

2. **验证房间设置**:
   - 检查房间设置中的"加密"选项是否关闭
   - 发送测试消息验证AI是否响应

### 方案2: 关闭现有房间加密

⚠️ **注意**: 一旦房间启用加密，通常无法关闭。建议创建新房间。

### 方案3: 使用未加密的公共房间

1. 加入公共的未加密房间
2. 在房间中测试AI功能

## 🧪 测试步骤

### 1. 创建测试房间
```
房间名称: AI测试房间
加密设置: 关闭
可见性: 私有或公共
```

### 2. 发送测试消息
```
消息内容: "AI你好"
预期结果: AI应该回复
```

### 3. 检查日志
在Dendrite日志中应该看到：
```
INFO 内容event m.room.message  // 而不是 m.room.encrypted
INFO AI用户回复: ...
```

## 🔍 日志分析

### 加密消息日志
```
INFO 内容event m.room.encrypted
INFO 检测到加密消息，AI服务暂不支持加密消息处理
INFO 建议：请在Matrix客户端中关闭房间加密，或创建一个未加密的房间来测试AI功能
```

### 正常消息日志
```
INFO 内容event m.room.message
INFO AI用户 @ai_assistant:localhost 在房间 !xxx:localhost 中回复: ...
INFO AI回复发送成功
```

## 🚀 未来改进

如果需要支持加密消息，需要实现：

1. **Matrix E2EE协议**: 实现Olm/Megolm加密算法
2. **设备验证**: 管理AI用户的加密设备
3. **密钥交换**: 处理房间密钥分发
4. **会话管理**: 管理加密会话状态

这是一个复杂的功能，建议先在未加密环境中测试AI基本功能。

## 📋 快速检查清单

- [ ] 确认房间未启用加密
- [ ] 发送包含"AI"关键词的消息
- [ ] 检查Dendrite日志中的事件类型
- [ ] 验证AI是否成功回复
- [ ] 在Matrix客户端中查看AI回复

## 💡 提示

- 大多数Matrix客户端默认为新房间启用加密
- 创建房间时需要明确关闭加密选项
- 可以在房间设置中查看当前的加密状态
- AI功能在未加密房间中工作正常