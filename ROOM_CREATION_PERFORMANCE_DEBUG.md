# 创建房间性能排查指南

## 🔍 **性能瓶颈分析**

### **主要流程和对应代码位置**

#### **1. 客户端请求处理**
- **位置**: `clientapi/routing/createroom.go:103-128`
- **耗时**: 通常 < 10ms
- **瓶颈**: 请求验证和解析

#### **2. 直接消息检查**
- **位置**: `clientapi/routing/createroom.go:158-224`
- **耗时**: 50-200ms
- **瓶颈**: 数据库查询现有 DM 房间

#### **3. 房间创建核心逻辑**
- **位置**: `roomserver/internal/perform/perform_create_room.go:42-557`
- **耗时**: 500-2000ms (主要瓶颈)
- **子步骤**:
  - 分配房间 NID: 10-50ms
  - 构建初始事件: 100-300ms
  - 事件签名: 50-150ms
  - 批量存储事件: 200-800ms
  - 处理邀请: 100-500ms

#### **4. 事件输入处理**
- **位置**: `roomserver/internal/input/input_events.go:71-500`
- **耗时**: 每个事件 50-200ms
- **瓶颈**: 数据库写入和状态计算

## 🔧 **性能排查方法**

### **1. 添加详细计时日志**

在 `perform_create_room.go` 中添加：

```go
func (c *Creator) PerformCreateRoom(ctx context.Context, userID spec.UserID, roomID spec.RoomID, createRequest *api.PerformCreateRoomRequest) (string, *util.JSONResponse) {
    start := time.Now()
    defer func() {
        logrus.WithFields(logrus.Fields{
            "room_id": roomID.String(),
            "user_id": userID.String(),
            "duration": time.Since(start),
        }).Info("PerformCreateRoom completed")
    }()

    // 1. 分配房间 NID
    step1 := time.Now()
    _, err = c.DB.AssignRoomNID(ctx, roomID, createRequest.RoomVersion)
    logrus.WithField("duration", time.Since(step1)).Debug("AssignRoomNID completed")

    // 2. 构建事件
    step2 := time.Now()
    // ... 事件构建逻辑
    logrus.WithField("duration", time.Since(step2)).Debug("Event building completed")

    // 3. 发送事件
    step3 := time.Now()
    if err = api.SendInputRoomEvents(ctx, c.RSAPI, userID.Domain(), inputs, false); err != nil {
        return "", &util.JSONResponse{...}
    }
    logrus.WithField("duration", time.Since(step3)).Debug("SendInputRoomEvents completed")

    // 4. 处理邀请
    step4 := time.Now()
    for _, invitee := range createRequest.InvitedUsers {
        // ... 邀请逻辑
    }
    logrus.WithField("duration", time.Since(step4)).Debug("Invitations completed")
}
```

### **2. 数据库性能分析**

#### **查看慢查询**
```sql
-- PostgreSQL
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
WHERE query LIKE '%roomserver%' 
ORDER BY mean_time DESC LIMIT 10;

-- 查看当前活跃查询
SELECT pid, now() - pg_stat_activity.query_start AS duration, query 
FROM pg_stat_activity 
WHERE (now() - pg_stat_activity.query_start) > interval '1 seconds';
```

#### **关键表的索引检查**
```sql
-- 检查房间相关表的索引
\d+ roomserver_rooms
\d+ roomserver_events
\d+ roomserver_state_snapshots

-- 检查是否缺少索引
EXPLAIN ANALYZE SELECT * FROM roomserver_events WHERE room_nid = 123;
```

### **3. 系统资源监控**

#### **CPU 使用率**
```bash
# 监控 Dendrite 进程
top -p $(pgrep dendrite)

# 详细 CPU 分析
perf top -p $(pgrep dendrite)
```

#### **内存使用**
```bash
# 内存使用情况
ps aux | grep dendrite

# 内存分配分析
go tool pprof http://localhost:6060/debug/pprof/heap
```

#### **磁盘 I/O**
```bash
# 监控磁盘 I/O
iostat -x 1

# 查看数据库文件 I/O
lsof -p $(pgrep postgres) | grep -E '\.(db|wal)$'
```

## 📊 **常见性能问题和解决方案**

### **1. 数据库瓶颈**

#### **问题症状**
- 创建房间耗时 > 2秒
- 数据库 CPU 使用率高
- 大量 INSERT/UPDATE 操作

#### **解决方案**
```sql
-- 添加缺失的索引
CREATE INDEX CONCURRENTLY idx_roomserver_events_room_nid ON roomserver_events(room_nid);
CREATE INDEX CONCURRENTLY idx_roomserver_state_snapshots_room_nid ON roomserver_state_snapshots(room_nid);

-- 优化数据库配置
-- postgresql.conf
shared_buffers = 256MB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
```

### **2. 事件签名瓶颈**

#### **问题症状**
- CPU 使用率高
- 创建房间时 CPU 峰值

#### **解决方案**
- 使用硬件加速的加密库
- 考虑事件签名缓存
- 优化密钥操作

### **3. 网络延迟**

#### **问题症状**
- 邀请用户时延迟高
- 联邦服务器响应慢

#### **解决方案**
- 并行处理邀请
- 设置合理的超时时间
- 使用连接池

### **4. 内存泄漏**

#### **问题症状**
- 内存使用持续增长
- GC 频繁

#### **解决方案**
```go
// 在事件处理后及时释放资源
defer func() {
    // 清理大对象
    inputs = nil
    builtEvents = nil
    runtime.GC()
}()
```

## 🎯 **优化建议**

### **1. 短期优化**
- 添加详细的性能日志
- 优化数据库索引
- 调整数据库连接池大小
- 启用查询缓存

### **2. 中期优化**
- 实现事件批量处理
- 优化状态计算算法
- 添加 Redis 缓存层
- 并行处理邀请

### **3. 长期优化**
- 重构事件存储架构
- 实现分布式事件处理
- 优化房间状态存储
- 实现智能缓存策略

## 🔍 **排查工具**

### **1. 内置性能监控**
```bash
# 启用 pprof
curl http://localhost:6060/debug/pprof/profile?seconds=30 > cpu.prof
go tool pprof cpu.prof

# 内存分析
curl http://localhost:6060/debug/pprof/heap > heap.prof
go tool pprof heap.prof
```

### **2. 数据库监控**
```sql
-- 启用查询日志
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 100;
SELECT pg_reload_conf();
```

### **3. 应用监控**
```go
// 添加 Prometheus 指标
var roomCreationDuration = prometheus.NewHistogramVec(
    prometheus.HistogramOpts{
        Name: "dendrite_room_creation_duration_seconds",
        Help: "Time taken to create a room",
    },
    []string{"room_type"},
)
```

通过这些方法，您可以准确定位创建房间的性能瓶颈并进行针对性优化。
