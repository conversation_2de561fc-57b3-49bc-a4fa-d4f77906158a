# 阅后即焚消息功能集成指南

## 🚀 快速开始

### 1. 配置文件设置

在 `dendrite.yaml` 中添加以下配置：

```yaml
# 全局配置 - 必须正确配置签名信息
global:
  server_name: "your-server.com"
  private_key: "matrix_key.pem"
  key_id: "ed25519:1"

# 房间服务器配置
room_server:
  database:
    connection_string: "postgresql://user:pass@localhost/dendrite_roomserver"
  
  # 阅后即焚消息配置
  expiring_messages:
    enabled: true
    check_interval: "30s"
    batch_size: 100
    cleanup_days: 7
    max_expire_days: 30
```

### 2. 代码集成

在 Dendrite 的 roomserver 初始化代码中添加：

```go
package main

import (
    "github.com/element-hq/dendrite/roomserver/internal/expiring_messages"
    // ... 其他导入
)

func main() {
    // ... 现有的初始化代码 ...
    
    // 创建 roomserver
    roomserverDB, err := storage.Open(&cfg.RoomServer.Database, caches)
    if err != nil {
        logrus.WithError(err).Panic("failed to connect to room server db")
    }
    
    roomserverAPI := roomserver.NewInternalAPI(cfg, roomserverDB, &base.Caches{}, fsAPI, keyRing)
    
    // 🔥 集成阅后即焚功能
    err = expiring_messages.IntegrateIntoRoomserver(roomserverDB, roomserverAPI, cfg)
    if err != nil {
        logrus.WithError(err).Error("集成阅后即焚功能失败")
        // 根据需要决定是否继续启动
    }
    
    // ... 继续其他初始化 ...
}
```

### 3. 客户端使用

客户端发送阅后即焚消息：

```bash
curl -X PUT 'http://localhost:8008/_matrix/client/v3/rooms/!roomid:server.com/send/m.room.message/txnid' \
  -H 'Authorization: Bearer YOUR_ACCESS_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{
    "msgtype": "m.text",
    "body": "这是一条阅后即焚消息",
    "expire_ts": 1750668509
  }'
```

注意：`expire_ts` 是**秒级时间戳**。

## 📋 集成方式对比

### 方式1: 推荐 - 从配置初始化

```go
// 自动从 dendrite.yaml 读取配置
err := expiring_messages.InitializeServiceFromConfig(db, rsAPI, cfg)
if err != nil {
    logrus.WithError(err).Error("初始化失败")
}
```

**优点**: 
- ✅ 完整的签名支持，可以发送撤回事件
- ✅ 从配置文件读取所有参数
- ✅ 自动启动服务

### 方式2: 手动配置

```go
service, err := expiring_messages.IntegrateWithManualConfig(
    db, rsAPI,
    "your-server.com",  // 服务器名
    "ed25519:1",        // 密钥ID
    privateKey,         // 私钥
)
```

**优点**: 
- ✅ 完整的签名支持
- ✅ 灵活的参数控制

### 方式3: 默认配置（不推荐）

```go
expiring_messages.InitializeService(db, rsAPI)
```

**缺点**: 
- ❌ 无法发送撤回事件（缺少签名信息）
- ❌ 只能检测和存储过期消息

## 🔧 配置参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `enabled` | `true` | 是否启用功能 |
| `check_interval` | `30s` | 检查过期消息的间隔 |
| `batch_size` | `100` | 每次处理的消息数量 |
| `cleanup_days` | `7` | 保留已处理记录的天数 |
| `max_expire_days` | `30` | 最大过期时间限制（天） |

## 📊 监控和日志

### 启动日志

```
INFO 创建阅后即焚消息服务 server_name="your-server.com" key_id="ed25519:1" check_interval=30s batch_size=100
INFO 启动阅后即焚消息处理服务 check_interval=30s batch_size=100 cleanup_days=7 max_expire_days=30
```

### 运行日志

```
INFO 检测到阅后即焚消息 event_id="$xxx" expire_ts=1750668509
INFO 成功添加阅后即焚消息到数据库 event_id="$xxx"
INFO 发现过期消息，开始处理 count=5
INFO 处理过期消息 event_id="$xxx"
INFO 过期消息处理完成 event_id="$xxx"
```

## 🔍 故障排查

### 问题1: 消息没有自动撤回

**可能原因**:
- 签名配置不正确
- 私钥文件路径错误
- 服务器名不匹配

**解决方法**:
```bash
# 检查配置
grep -A 10 "expiring_messages" dendrite.yaml

# 检查日志
grep "签名信息不完整\|构建撤回事件失败" /var/log/dendrite.log

# 验证私钥文件
ls -la matrix_key.pem
```

### 问题2: 服务启动失败

**检查步骤**:
1. 确认配置文件格式正确
2. 检查数据库连接
3. 验证权限设置

### 问题3: 性能问题

**优化建议**:
```yaml
# 高频场景
expiring_messages:
  check_interval: "10s"
  batch_size: 200

# 低频场景  
expiring_messages:
  check_interval: "2m"
  batch_size: 50
```

## 🧪 测试验证

### 1. 功能测试

```go
// 在代码中添加测试
expiring_messages.CheckServiceStatus()
expiring_messages.TestServiceIntegration()
```

### 2. 数据库验证

```sql
-- 查看过期消息记录
SELECT * FROM roomserver_expiring_messages WHERE processed = false;

-- 查看已处理的消息
SELECT * FROM roomserver_expiring_messages WHERE processed = true LIMIT 10;
```

### 3. 手动测试

1. 发送带有 `expire_ts` 的消息
2. 等待过期时间到达
3. 确认消息被自动撤回

## ⚠️ 重要注意事项

1. **签名配置必须正确**: 服务器名、密钥ID 和私钥必须与 Dendrite 配置一致
2. **时间戳格式**: 使用秒级时间戳，不是毫秒
3. **数据库权限**: 确保数据库用户有创建表的权限
4. **私钥安全**: 保护好私钥文件的安全性

## 📈 性能调优

### 数据库优化

```sql
-- 添加复合索引
CREATE INDEX roomserver_expiring_messages_status_expire_idx 
ON roomserver_expiring_messages (processed, expire_ts);

-- 定期清理
DELETE FROM roomserver_expiring_messages 
WHERE processed = true AND created_at < NOW() - INTERVAL '30 days';
```

### 配置调优

根据消息量调整参数：

- **高频使用**: `check_interval: "10s"`, `batch_size: 200`
- **中频使用**: `check_interval: "30s"`, `batch_size: 100` 
- **低频使用**: `check_interval: "2m"`, `batch_size: 50`

现在阅后即焚功能已经完全集成并可以正常工作了！
