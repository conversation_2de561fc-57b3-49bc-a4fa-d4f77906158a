# 创建房间链路跟踪指南

## 🔍 **链路跟踪架构**

我们已经为创建房间的完整流程添加了详细的链路跟踪，包括：

### **1. 主要跟踪点**

#### **ClientAPI 层**
- `CreateRoom` - HTTP 请求入口
- `createRoom.internal` - 内部处理逻辑
- `createRoom.checkDirectMessage` - 直接消息检查
- `createRoom.newRoom` - 新房间创建
- `createRoom.getUserProfile` - 用户资料获取
- `createRoom.performCreateRoom` - 调用 RoomServer

#### **RoomServer 层**
- `RoomServer.PerformCreateRoom` - 主要创建逻辑
- `PerformCreateRoom.assignRoomNID` - 分配房间 NID
- `PerformCreateRoom.buildEvents` - 构建事件
- `PerformCreateRoom.buildEvent.{type}` - 单个事件构建
- `PerformCreateRoom.signEvent.{type}` - 事件签名
- `PerformCreateRoom.sendInputRoomEvents` - 发送事件
- `PerformCreateRoom.processInvites` - 处理邀请
- `PerformCreateRoom.invite.{index}` - 单个邀请

## 📊 **跟踪标签说明**

### **通用标签**
- `user_id` - 创建者用户 ID
- `room_id` - 房间 ID
- `device_id` - 设备 ID
- `error` - 错误类型

### **请求参数标签**
- `room_alias` - 房间别名
- `preset` - 房间预设
- `visibility` - 可见性
- `invite_count` - 邀请用户数量
- `is_direct` - 是否直接消息

### **性能标签**
- `events_count` - 事件数量
- `invited_users_count` - 被邀请用户数量
- `room_version` - 房间版本
- `event_type` - 事件类型
- `event_index` - 事件索引

## 🔧 **启用链路跟踪**

### **1. 配置 Jaeger (推荐)**

```yaml
# dendrite.yaml
tracing:
  enabled: true
  jaeger:
    serviceName: "dendrite"
    endpoint: "http://localhost:14268/api/traces"
    sampler:
      type: "const"
      param: 1.0
```

### **2. 启动 Jaeger**

```bash
# 使用 Docker 启动 Jaeger
docker run -d --name jaeger \
  -p 16686:16686 \
  -p 14268:14268 \
  jaegertracing/all-in-one:latest

# 访问 Jaeger UI
open http://localhost:16686
```

### **3. 配置环境变量**

```bash
export JAEGER_ENDPOINT=http://localhost:14268/api/traces
export JAEGER_SAMPLER_TYPE=const
export JAEGER_SAMPLER_PARAM=1
```

## 📈 **性能分析方法**

### **1. 查看完整链路**

在 Jaeger UI 中搜索：
- Service: `dendrite`
- Operation: `CreateRoom`
- Tags: `user_id:{用户ID}`

### **2. 分析性能瓶颈**

查看各个 Span 的耗时：
```
CreateRoom (总耗时)
├── createRoom.internal (内部逻辑)
│   ├── createRoom.checkDirectMessage (DM 检查)
│   ├── createRoom.newRoom (新房间)
│   │   ├── createRoom.getUserProfile (用户资料)
│   │   └── createRoom.performCreateRoom (RoomServer 调用)
│   └── RoomServer.PerformCreateRoom (RoomServer 处理)
│       ├── PerformCreateRoom.assignRoomNID (分配 NID)
│       ├── PerformCreateRoom.buildEvents (构建事件)
│       │   ├── PerformCreateRoom.buildEvent.m.room.create
│       │   ├── PerformCreateRoom.buildEvent.m.room.member
│       │   └── ...
│       ├── PerformCreateRoom.sendInputRoomEvents (发送事件)
│       └── PerformCreateRoom.processInvites (处理邀请)
```

### **3. 常见性能问题识别**

#### **数据库瓶颈**
- `PerformCreateRoom.assignRoomNID` 耗时 > 100ms
- `PerformCreateRoom.sendInputRoomEvents` 耗时 > 500ms

#### **事件构建瓶颈**
- `PerformCreateRoom.buildEvents` 耗时 > 300ms
- 单个 `buildEvent` 耗时 > 50ms

#### **网络瓶颈**
- `createRoom.getUserProfile` 耗时 > 200ms
- `PerformCreateRoom.invite.*` 耗时 > 100ms

## 🎯 **优化建议**

### **1. 基于跟踪数据的优化**

```go
// 如果 assignRoomNID 慢，优化数据库
if assignRoomNIDTime > 100*time.Millisecond {
    // 检查数据库索引
    // 优化连接池配置
}

// 如果事件构建慢，考虑并行处理
if buildEventsTime > 300*time.Millisecond {
    // 实现事件并行构建
    // 优化事件签名算法
}

// 如果邀请处理慢，实现批量处理
if inviteProcessingTime > 200*time.Millisecond {
    // 并行发送邀请
    // 优化网络连接
}
```

### **2. 设置性能告警**

```yaml
# Prometheus 告警规则
groups:
  - name: dendrite_room_creation
    rules:
      - alert: SlowRoomCreation
        expr: histogram_quantile(0.95, dendrite_trace_duration_seconds{operation="CreateRoom"}) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "房间创建速度慢"
          description: "95% 的房间创建耗时超过 2 秒"

      - alert: DatabaseBottleneck
        expr: histogram_quantile(0.95, dendrite_trace_duration_seconds{operation="PerformCreateRoom.assignRoomNID"}) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "数据库分配房间 NID 慢"
```

## 📊 **监控仪表板**

### **Grafana 面板配置**

```json
{
  "dashboard": {
    "title": "Dendrite 房间创建性能",
    "panels": [
      {
        "title": "房间创建耗时分布",
        "type": "histogram",
        "targets": [
          {
            "expr": "histogram_quantile(0.50, dendrite_trace_duration_seconds{operation=\"CreateRoom\"})",
            "legendFormat": "P50"
          },
          {
            "expr": "histogram_quantile(0.95, dendrite_trace_duration_seconds{operation=\"CreateRoom\"})",
            "legendFormat": "P95"
          },
          {
            "expr": "histogram_quantile(0.99, dendrite_trace_duration_seconds{operation=\"CreateRoom\"})",
            "legendFormat": "P99"
          }
        ]
      },
      {
        "title": "各阶段耗时对比",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(dendrite_trace_duration_seconds_sum{operation=~\".*CreateRoom.*\"}[5m]) / rate(dendrite_trace_duration_seconds_count{operation=~\".*CreateRoom.*\"}[5m])",
            "legendFormat": "{{operation}}"
          }
        ]
      }
    ]
  }
}
```

## 🔍 **故障排查流程**

### **1. 用户报告房间创建慢**

1. 在 Jaeger 中搜索该用户的创建房间请求
2. 查看完整链路，识别最慢的 Span
3. 检查错误标签和日志
4. 对比正常请求的性能数据

### **2. 系统性能下降**

1. 查看 Grafana 仪表板，确认性能趋势
2. 检查各个组件的资源使用情况
3. 分析 Jaeger 中的错误率和延迟分布
4. 根据瓶颈组件进行针对性优化

### **3. 特定场景问题**

```bash
# 查找直接消息创建问题
curl "http://jaeger:16686/api/traces?service=dendrite&operation=CreateRoom&tag=is_direct:true"

# 查找大群组创建问题
curl "http://jaeger:16686/api/traces?service=dendrite&operation=CreateRoom&tag=invite_count>10"

# 查找特定错误
curl "http://jaeger:16686/api/traces?service=dendrite&operation=CreateRoom&tag=error:*"
```

通过这套完整的链路跟踪系统，您可以精确定位创建房间过程中的性能瓶颈，并进行针对性优化！
