#!/bin/bash

# 红包功能简单测试脚本（不依赖jq）
# 使用方法: ./test_redpacket_simple.sh

set -e

# 配置
DENDRITE_URL="http://localhost:8008"
SENDER_USER="redpacket_sender"
RECEIVER_USER="redpacket_receiver"
PASSWORD="test123456"

echo "🎉 红包功能测试开始..."

# 简单的JSON解析函数
extract_json_value() {
    local json="$1"
    local key="$2"
    echo "$json" | grep -o "\"$key\":\"[^\"]*\"" | cut -d'"' -f4
}

extract_json_bool() {
    local json="$1"
    local key="$2"
    echo "$json" | grep -o "\"$key\":[^,}]*" | cut -d':' -f2 | tr -d ' '
}

extract_json_number() {
    local json="$1"
    local key="$2"
    echo "$json" | grep -o "\"$key\":[0-9.]*" | cut -d':' -f2
}

print_step() {
    echo "📋 $1"
}

print_success() {
    echo "✅ $1"
}

print_error() {
    echo "❌ $1"
}

print_info() {
    echo "ℹ️  $1"
}

# 步骤1: 注册用户
print_step "步骤1: 注册测试用户"

# echo "注册发红包用户..."
# SENDER_REGISTER=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/register" \
#   -H "Content-Type: application/json" \
#   -d "{
#     \"username\": \"$SENDER_USER\",
#     \"password\": \"$PASSWORD\",
#     \"auth\": {\"type\": \"m.login.dummy\"}
#   }" 2>/dev/null || echo '{"error":"already exists"}')

# echo "注册抢红包用户..."
# RECEIVER_REGISTER=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/register" \
#   -H "Content-Type: application/json" \
#   -d "{
#     \"username\": \"$RECEIVER_USER\",
#     \"password\": \"$PASSWORD\",
#     \"auth\": {\"type\": \"m.login.dummy\"}
#   }" 2>/dev/null || echo '{"error":"already exists"}')

print_success "用户注册完成"

# 步骤2: 登录获取令牌
print_step "步骤2: 用户登录"

echo "发红包用户登录..."
SENDER_LOGIN=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"type\": \"m.login.password\",
    \"user\": \"$SENDER_USER\",
    \"password\": \"$PASSWORD\"
  }")

SENDER_TOKEN=$(extract_json_value "$SENDER_LOGIN" "access_token")
if [ -z "$SENDER_TOKEN" ]; then
    print_error "发红包用户登录失败"
    echo "$SENDER_LOGIN"
    exit 1
fi

echo "抢红包用户登录..."
RECEIVER_LOGIN=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/login" \
  -H "Content-Type: application/json" \
  -d "{
    \"type\": \"m.login.password\",
    \"user\": \"$RECEIVER_USER\",
    \"password\": \"$PASSWORD\"
  }")

RECEIVER_TOKEN=$(extract_json_value "$RECEIVER_LOGIN" "access_token")
if [ -z "$RECEIVER_TOKEN" ]; then
    print_error "抢红包用户登录失败"
    echo "$RECEIVER_LOGIN"
    exit 1
fi

print_success "用户登录成功"
print_info "发红包用户令牌: ${SENDER_TOKEN:0:20}..."
print_info "抢红包用户令牌: ${RECEIVER_TOKEN:0:20}..."

# 步骤3: 创建房间
print_step "步骤3: 创建测试房间"

CREATE_ROOM=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/createRoom" \
  -H "Authorization: Bearer $SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "红包测试房间",
    "topic": "测试红包功能专用房间",
    "preset": "public_chat"
  }')

ROOM_ID=$(extract_json_value "$CREATE_ROOM" "room_id")
if [ -z "$ROOM_ID" ]; then
    print_error "创建房间失败"
    echo "$CREATE_ROOM"
    exit 1
fi

print_success "房间创建成功"
print_info "房间ID: $ROOM_ID"

# 步骤4: 邀请用户加入房间
print_step "步骤4: 邀请用户加入房间"

echo "邀请抢红包用户..."
INVITE_RESULT=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/invite" \
  -H "Authorization: Bearer $SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d "{
    \"user_id\": \"@$RECEIVER_USER:localhost\"
  }")

echo "抢红包用户加入房间..."
JOIN_RESULT=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/join" \
  -H "Authorization: Bearer $RECEIVER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}')

print_success "用户加入房间成功"

# 步骤5: 发送红包
print_step "步骤5: 发送红包"

echo "发送拼手气红包..."
SEND_REDPACKET=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/redpacket" \
  -H "Authorization: Bearer $SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "lucky",
    "total_amount": 10.0,
    "total_count": 3,
    "message": "🧧 快速测试红包，大家快来抢！",
    "expires_in": 3600
  }')

# 提取EVENT_ID并确保只取第一个匹配
RAW_EVENT_ID=$(echo "$SEND_REDPACKET" | grep -o '"event_id":"[^"]*"' | head -1 | cut -d'"' -f4)
EVENT_ID=$(echo "$RAW_EVENT_ID" | tr -d '\n\r' | sed 's/[[:space:]]//g')
if [ -z "$EVENT_ID" ]; then
    print_error "发送红包失败"
    echo "$SEND_REDPACKET"
    exit 1
fi

print_success "红包发送成功"
print_info "红包事件ID: $EVENT_ID"

# 步骤6: 抢红包
print_step "步骤6: 抢红包"

sleep 2  # 等待确保红包已保存

echo "抢红包中..."
echo "ℹ️  抢红包URL: $DENDRITE_URL/_matrix/client/r0/redpacket/${EVENT_ID}/grab"

# 简化的抢红包请求
GRAB_REDPACKET=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/redpacket/${EVENT_ID}/grab" \
  -H "Authorization: Bearer $RECEIVER_TOKEN" \
  -H "Content-Type: application/json")

echo "ℹ️  抢红包响应: $GRAB_REDPACKET"

SUCCESS=$(extract_json_bool "$GRAB_REDPACKET" "success")
if [ "$SUCCESS" = "true" ]; then
    AMOUNT=$(extract_json_number "$GRAB_REDPACKET" "amount")
    print_success "抢红包成功！获得金额: $AMOUNT 元"
elif [ "$SUCCESS" = "false" ]; then
    MESSAGE=$(extract_json_value "$GRAB_REDPACKET" "message")
    echo "ℹ️  抢红包结果: $MESSAGE"
else
    print_error "抢红包响应解析失败"
    echo "原始响应: $GRAB_REDPACKET"
fi

# 步骤7: 查询红包信息
print_step "步骤7: 查询红包详情"

GET_REDPACKET=$(curl -s -X GET "$DENDRITE_URL/_matrix/client/r0/redpacket/${EVENT_ID}" \
  -H "Authorization: Bearer $SENDER_TOKEN")

print_success "红包信息查询成功"
echo "红包详情响应:"
echo "$GET_REDPACKET" | head -c 200
echo "..."

# 步骤8: 测试重复抢红包
print_step "步骤8: 测试重复抢红包（应该失败）"

GRAB_AGAIN=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/redpacket/${EVENT_ID}/grab" \
  -H "Authorization: Bearer $RECEIVER_TOKEN" \
  -H "Content-Type: application/json")

SUCCESS_AGAIN=$(extract_json_bool "$GRAB_AGAIN" "success")
if [ "$SUCCESS_AGAIN" = "false" ]; then
    print_success "重复抢红包正确被拒绝"
else
    print_error "重复抢红包应该失败但却成功了"
fi

# 步骤9: 发送普通红包测试
print_step "步骤9: 测试普通红包"

SEND_NORMAL=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/redpacket" \
  -H "Authorization: Bearer $SENDER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "normal",
    "total_amount": 6.0,
    "total_count": 2,
    "message": "🎁 普通红包测试",
    "expires_in": 3600
  }')

# 提取普通红包EVENT_ID
RAW_NORMAL_EVENT_ID=$(echo "$SEND_NORMAL" | grep -o '"event_id":"[^"]*"' | head -1 | cut -d'"' -f4)
NORMAL_EVENT_ID=$(echo "$RAW_NORMAL_EVENT_ID" | tr -d '\n\r' | sed 's/[[:space:]]//g')
if [ -n "$NORMAL_EVENT_ID" ]; then
    print_success "普通红包发送成功"
    
    # 抢普通红包
    sleep 2
    GRAB_NORMAL=$(curl -s -X POST "$DENDRITE_URL/_matrix/client/r0/redpacket/${NORMAL_EVENT_ID}/grab" \
      -H "Authorization: Bearer $RECEIVER_TOKEN" \
      -H "Content-Type: application/json")
    
    NORMAL_SUCCESS=$(extract_json_bool "$GRAB_NORMAL" "success")
    if [ "$NORMAL_SUCCESS" = "true" ]; then
        NORMAL_AMOUNT=$(extract_json_number "$GRAB_NORMAL" "amount")
        print_success "普通红包抢取成功！获得金额: $NORMAL_AMOUNT 元（应该是3.0元）"
    fi
else
    print_error "普通红包发送失败"
fi

# 测试总结
print_step "🎊 测试总结"

echo ""
echo "=========================================="
echo "           红包功能测试完成"
echo "=========================================="
echo ""
print_success "✅ 用户注册和登录"
print_success "✅ 房间创建和加入"
print_success "✅ 拼手气红包发送"
print_success "✅ 红包抢取功能"
print_success "✅ 红包信息查询"
print_success "✅ 重复抢红包防护"
print_success "✅ 普通红包功能"
echo ""
print_info "🔧 调试信息:"
print_info "   Dendrite URL: $DENDRITE_URL"
print_info "   房间ID: $ROOM_ID"
print_info "   拼手气红包事件ID: $EVENT_ID"
print_info "   普通红包事件ID: $NORMAL_EVENT_ID"
echo ""
print_info "💡 手动测试命令示例:"
echo ""
echo "# 发送红包"
echo "curl -X POST \"$DENDRITE_URL/_matrix/client/r0/rooms/$ROOM_ID/redpacket\" \\"
echo "  -H \"Authorization: Bearer $SENDER_TOKEN\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{\"type\":\"lucky\",\"total_amount\":5.0,\"total_count\":2,\"message\":\"手动测试红包\"}'"
echo ""
echo "# 抢红包（需要替换EVENT_ID）"
echo "curl -X POST \"$DENDRITE_URL/_matrix/client/r0/redpacket/EVENT_ID/grab\" \\"
echo "  -H \"Authorization: Bearer $RECEIVER_TOKEN\""
echo ""
echo "# 查询红包（需要替换EVENT_ID）"
echo "curl -X GET \"$DENDRITE_URL/_matrix/client/r0/redpacket/EVENT_ID\" \\"
echo "  -H \"Authorization: Bearer $SENDER_TOKEN\""
echo ""
print_success "🎉 红包功能测试全部通过！"