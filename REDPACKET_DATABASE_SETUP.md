# 红包服务独立数据库配置指南

## 🎯 配置说明

红包服务现在使用独立的数据库配置，与全局数据库配置分离，这样可以：
- 更好地隔离红包数据
- 独立管理红包服务的数据库连接
- 便于后续的数据库优化和维护

## ✅ 当前配置

### 1. 配置文件设置
在 [`cmd/dendrite/dendrite.yaml`](cmd/dendrite/dendrite.yaml) 中，红包服务使用独立的数据库配置：

```yaml
# Red packet service configuration - provides red packet functionality
redpacket:
  # Whether to enable red packet service
  enabled: true
  
  # Maximum red packet amount limit
  max_amount: 200.0
  
  # Maximum red packet count limit
  max_count: 100
  
  # Default expiration time (seconds)
  default_expires_in: 86400
  
  # Minimum red packet amount
  min_amount: 0.01
  
  # Whether to allow lucky red packets
  allow_lucky_redpacket: true
  
  # 独立的数据库配置 - 复制自全局配置
  database:
    connection_string: ***************************************************/dendrite?sslmode=disable
    max_open_conns: 90
    max_idle_conns: 5
    conn_max_lifetime: -1
```

### 2. 代码实现
在 [`redpacketapi/redpacketapi.go`](redpacketapi/redpacketapi.go) 中：

```go
// 创建数据库连接 - 使用独立的数据库配置
redpacketDB, err := postgres.NewDatabase(context.Background(), cm, &cfg.Database)
if err != nil {
    logrus.WithError(err).Panic("无法连接到红包数据库")
}
```

## 🚀 启动测试

### 1. 重新编译
```bash
go build -o bin/dendrite ./cmd/dendrite
```

### 2. 启动服务
```bash
./bin/dendrite --config dendrite.yaml
```

### 3. 检查启动日志
应该看到成功的启动日志：
```
INFO[2024-01-01T12:00:00Z] Dendrite 版本 0.13.7+dev
INFO[2024-01-01T12:00:00Z] 红包服务已启用
INFO[2024-01-01T12:00:00Z] 红包数据库表创建成功
```

### 4. 运行测试
```bash
./test_redpacket_simple.sh
```

## 📋 数据库表结构

红包服务会在指定的数据库中自动创建以下表：

### 红包主表 (redpacketapi_redpackets)
```sql
CREATE TABLE IF NOT EXISTS redpacketapi_redpackets (
    id TEXT PRIMARY KEY,                    -- 红包唯一ID
    event_id TEXT NOT NULL UNIQUE,          -- Matrix事件ID
    room_id TEXT NOT NULL,                  -- 房间ID
    sender_id TEXT NOT NULL,                -- 发送者ID
    type TEXT NOT NULL,                     -- 红包类型 (normal/lucky)
    total_amount DECIMAL(10,2) NOT NULL,    -- 总金额
    total_count INTEGER NOT NULL,           -- 总个数
    remaining_amount DECIMAL(10,2) NOT NULL, -- 剩余金额
    remaining_count INTEGER NOT NULL,       -- 剩余个数
    message TEXT NOT NULL DEFAULT '',       -- 红包消息
    status TEXT NOT NULL DEFAULT 'active',  -- 状态 (active/expired/finished)
    created_at TIMESTAMP NOT NULL DEFAULT NOW(), -- 创建时间
    expires_at TIMESTAMP NOT NULL          -- 过期时间
);
```

### 红包领取记录表 (redpacketapi_redpacket_grabs)
```sql
CREATE TABLE IF NOT EXISTS redpacketapi_redpacket_grabs (
    id TEXT PRIMARY KEY,                    -- 领取记录ID
    redpacket_id TEXT NOT NULL,             -- 红包ID
    user_id TEXT NOT NULL,                  -- 用户ID
    amount DECIMAL(10,2) NOT NULL,          -- 领取金额
    grabbed_at TIMESTAMP NOT NULL DEFAULT NOW(), -- 领取时间
    is_luckiest BOOLEAN NOT NULL DEFAULT FALSE,   -- 是否手气最佳
    user_nickname TEXT NOT NULL DEFAULT '', -- 用户昵称
    FOREIGN KEY (redpacket_id) REFERENCES redpacketapi_redpackets(id) ON DELETE CASCADE,
    UNIQUE(redpacket_id, user_id)          -- 每个用户只能抢同一个红包一次
);
```

### 索引
```sql
-- 提高查询性能的索引
CREATE INDEX IF NOT EXISTS idx_redpackets_room_id ON redpacketapi_redpackets(room_id);
CREATE INDEX IF NOT EXISTS idx_redpackets_sender_id ON redpacketapi_redpackets(sender_id);
CREATE INDEX IF NOT EXISTS idx_redpackets_status ON redpacketapi_redpackets(status);
CREATE INDEX IF NOT EXISTS idx_redpackets_expires_at ON redpacketapi_redpackets(expires_at);
CREATE INDEX IF NOT EXISTS idx_redpacket_grabs_redpacket_id ON redpacketapi_redpacket_grabs(redpacket_id);
CREATE INDEX IF NOT EXISTS idx_redpacket_grabs_user_id ON redpacketapi_redpacket_grabs(user_id);
```

## 🔧 配置选项说明

### 数据库连接参数
- **connection_string**: PostgreSQL连接字符串
- **max_open_conns**: 最大打开连接数 (90)
- **max_idle_conns**: 最大空闲连接数 (5)
- **conn_max_lifetime**: 连接最大生存时间 (-1表示永不过期)

### 红包业务参数
- **max_amount**: 单个红包最大金额 (200.0)
- **max_count**: 单个红包最大个数 (100)
- **min_amount**: 单个红包最小金额 (0.01)
- **default_expires_in**: 默认过期时间，秒 (86400 = 24小时)
- **allow_lucky_redpacket**: 是否允许拼手气红包 (true)

## 🎯 API端点

红包服务提供以下API端点：

### 发送红包
```http
POST /_matrix/client/r0/rooms/{roomID}/redpacket
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "type": "lucky",           // 红包类型: "normal" 或 "lucky"
  "total_amount": 10.0,      // 总金额
  "total_count": 5,          // 总个数
  "message": "恭喜发财"       // 红包消息
}
```

### 抢红包
```http
POST /_matrix/client/r0/redpacket/{eventID}/grab
Authorization: Bearer YOUR_ACCESS_TOKEN
```

### 查询红包
```http
GET /_matrix/client/r0/redpacket/{eventID}
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## 🔍 故障排除

### 1. 数据库连接问题
如果遇到连接错误，检查：
- 数据库服务是否运行
- 连接字符串是否正确
- 网络连接是否正常
- 用户权限是否足够

### 2. 表创建失败
如果表创建失败，检查：
- 数据库用户是否有CREATE权限
- 数据库是否存在
- PostgreSQL版本是否兼容

### 3. 测试连接
```bash
# 测试数据库连接
psql -h 192.168.50.26 -p 5000 -U zhoujiayi -d dendrite -c "SELECT version();"

# 检查表是否创建
psql -h 192.168.50.26 -p 5000 -U zhoujiayi -d dendrite -c "\dt redpacketapi_*"
```

## ✅ 预期结果

配置正确后，应该能看到：

1. **启动成功**
   ```
   INFO[2024-01-01T12:00:00Z] 红包服务已启用
   INFO[2024-01-01T12:00:00Z] 红包数据库连接成功
   ```

2. **测试通过**
   ```
   🎉 红包功能测试开始...
   ✅ 用户注册完成
   ✅ 用户登录成功
   ✅ 房间创建成功
   ✅ 红包发送成功
   ✅ 抢红包成功！获得金额: 3.45 元
   🎊 红包功能测试全部通过！
   ```

现在红包服务使用独立的数据库配置，应该可以正常工作了！🎉