# 红包领取记录表详解

## 📋 表名：redpacketapi_redpacket_grabs

这个表是红包系统的核心数据表之一，专门用于记录用户抢红包的详细信息。

## 🎯 主要作用

### 1. **记录抢红包行为**
- 记录谁在什么时候抢了哪个红包
- 记录抢到了多少金额
- 防止用户重复抢同一个红包

### 2. **支持业务逻辑**
- **防重复抢取**：通过 `UNIQUE(redpacket_id, user_id)` 约束确保每个用户只能抢同一个红包一次
- **金额分配记录**：记录每次抢红包的具体金额
- **手气最佳计算**：标记哪个用户是手气最佳

### 3. **数据统计和展示**
- 显示红包的抢取历史
- 计算红包的剩余金额和数量
- 展示谁抢到了红包，抢到了多少

## 🗃️ 表结构详解

```sql
CREATE TABLE IF NOT EXISTS redpacketapi_redpacket_grabs (
    id TEXT PRIMARY KEY,                    -- 领取记录的唯一ID
    redpacket_id TEXT NOT NULL,             -- 关联的红包ID（外键）
    user_id TEXT NOT NULL,                  -- 抢红包的用户ID
    amount DECIMAL(10,2) NOT NULL,          -- 用户抢到的金额
    grabbed_at TIMESTAMP NOT NULL DEFAULT NOW(), -- 抢红包的时间
    is_luckiest BOOLEAN NOT NULL DEFAULT FALSE,   -- 是否是手气最佳
    user_nickname TEXT NOT NULL DEFAULT '', -- 用户昵称（用于显示）
    
    -- 外键约束：确保红包存在
    FOREIGN KEY (redpacket_id) REFERENCES redpacketapi_redpackets(id) ON DELETE CASCADE,
    
    -- 唯一约束：每个用户只能抢同一个红包一次
    UNIQUE(redpacket_id, user_id)
);
```

## 📊 字段说明

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | TEXT | 领取记录唯一标识符 | `grab_123456789` |
| `redpacket_id` | TEXT | 关联的红包ID | `redpacket_abc123` |
| `user_id` | TEXT | 抢红包的用户Matrix ID | `@alice:localhost` |
| `amount` | DECIMAL(10,2) | 抢到的金额（精确到分） | `3.45` |
| `grabbed_at` | TIMESTAMP | 抢红包的时间戳 | `2024-01-01 12:30:45` |
| `is_luckiest` | BOOLEAN | 是否手气最佳 | `true/false` |
| `user_nickname` | TEXT | 用户显示名称 | `Alice` |

## 🔄 业务流程中的作用

### 1. **发送红包时**
- 不涉及此表，只在 `redpacketapi_redpackets` 表中创建红包记录

### 2. **抢红包时**
```sql
-- 1. 检查用户是否已经抢过这个红包
SELECT COUNT(*) FROM redpacketapi_redpacket_grabs 
WHERE redpacket_id = ? AND user_id = ?;

-- 2. 如果没抢过，插入新的抢红包记录
INSERT INTO redpacketapi_redpacket_grabs 
(id, redpacket_id, user_id, amount, grabbed_at, user_nickname) 
VALUES (?, ?, ?, ?, NOW(), ?);

-- 3. 更新红包主表的剩余金额和数量
UPDATE redpacketapi_redpackets 
SET remaining_amount = remaining_amount - ?, 
    remaining_count = remaining_count - 1 
WHERE id = ?;
```

### 3. **查询红包详情时**
```sql
-- 获取红包的所有抢取记录
SELECT user_id, user_nickname, amount, grabbed_at, is_luckiest 
FROM redpacketapi_redpacket_grabs 
WHERE redpacket_id = ? 
ORDER BY grabbed_at ASC;
```

### 4. **计算手气最佳时**
```sql
-- 找出抢到金额最多的记录
SELECT id FROM redpacketapi_redpacket_grabs 
WHERE redpacket_id = ? 
ORDER BY amount DESC 
LIMIT 1;

-- 标记为手气最佳
UPDATE redpacketapi_redpacket_grabs 
SET is_luckiest = true 
WHERE id = ?;
```

## 🎲 拼手气红包算法

对于拼手气红包，每次抢红包时的金额分配算法：

```go
// 简化的拼手气算法
func calculateLuckyAmount(remainingAmount float64, remainingCount int) float64 {
    if remainingCount == 1 {
        // 最后一个红包，返回剩余全部金额
        return remainingAmount
    }
    
    // 随机分配，但确保剩余红包每个至少有0.01元
    minReserve := float64(remainingCount-1) * 0.01
    maxAmount := remainingAmount - minReserve
    
    // 在0.01到maxAmount之间随机
    return 0.01 + rand.Float64()*(maxAmount-0.01)
}
```

## 📈 数据示例

假设有一个10元5个的拼手气红包，抢取记录可能如下：

| id | user_id | amount | grabbed_at | is_luckiest |
|----|---------|--------|------------|-------------|
| grab_001 | @alice:localhost | 2.15 | 12:30:01 | false |
| grab_002 | @bob:localhost | 4.32 | 12:30:05 | **true** |
| grab_003 | @charlie:localhost | 1.88 | 12:30:12 | false |
| grab_004 | @david:localhost | 0.95 | 12:30:18 | false |
| grab_005 | @eve:localhost | 0.70 | 12:30:25 | false |

在这个例子中：
- Bob抢到了4.32元，是手气最佳（`is_luckiest = true`）
- 总计：2.15 + 4.32 + 1.88 + 0.95 + 0.70 = 10.00元 ✅

## 🔍 常用查询

### 查看用户抢红包历史
```sql
SELECT r.message, g.amount, g.grabbed_at, g.is_luckiest
FROM redpacketapi_redpacket_grabs g
JOIN redpacketapi_redpackets r ON g.redpacket_id = r.id
WHERE g.user_id = '@alice:localhost'
ORDER BY g.grabbed_at DESC;
```

### 查看红包排行榜
```sql
SELECT user_nickname, SUM(amount) as total_amount, COUNT(*) as grab_count
FROM redpacketapi_redpacket_grabs
GROUP BY user_id, user_nickname
ORDER BY total_amount DESC
LIMIT 10;
```

### 查看手气最佳统计
```sql
SELECT user_nickname, COUNT(*) as luckiest_count
FROM redpacketapi_redpacket_grabs
WHERE is_luckiest = true
GROUP BY user_id, user_nickname
ORDER BY luckiest_count DESC;
```

## 🛡️ 安全特性

### 1. **防重复抢取**
- `UNIQUE(redpacket_id, user_id)` 约束确保数据库层面防重复
- 应用层也会检查，双重保护

### 2. **数据一致性**
- 使用数据库事务确保红包金额扣减和记录插入的原子性
- 外键约束确保数据完整性

### 3. **并发安全**
- 使用数据库锁机制处理并发抢红包
- 确保不会出现超发或负数情况

## 📝 总结

`redpacketapi_redpacket_grabs` 表是红包系统的核心组件，它：

1. **记录详细信息** - 完整记录每次抢红包的行为
2. **保证业务规则** - 防止重复抢取，支持手气最佳计算
3. **支持数据展示** - 提供丰富的查询和统计功能
4. **确保数据安全** - 通过约束和事务保证数据一致性

这个表与 `redpacketapi_redpackets`（红包主表）配合，共同实现了完整的红包功能。🎉